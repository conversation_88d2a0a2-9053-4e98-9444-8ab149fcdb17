import type { PlasmoCSConfig } from "plasmo";

export const config: PlasmoCSConfig = {
  matches: ["https://www.google.com/maps/*"],
  run_at: "document_end"
};

// Simple content script that creates an overlay and scrapes visible business data
interface BusinessData {
  name: string;
  address: string;
  phone: string;
  website: string;
  rating: number | null;
  reviewCount: number | null;
  category: string;
  placeId: string;
}

interface ScrapingState {
  isActive: boolean;
  status: string;
  totalFound: number;
}

class GoogleMapsScraper {
  private businesses: BusinessData[] = [];
  private isActive = false;
  private state: ScrapingState = {
    isActive: false,
    status: 'idle',
    totalFound: 0
  };
  private overlayElement: HTMLElement | null = null;

  constructor() {
    this.setupMessageListener();
    console.log('Google Maps Scraper initialized');
  }

  private setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('Received message:', message);

      switch (message.action) {
        case 'start-scraping':
          this.startScraping();
          sendResponse({ success: true });
          break;
        case 'stop-scraping':
          this.stopScraping();
          sendResponse({ success: true });
          break;
        case 'get-state':
          sendResponse({ state: this.state, businesses: this.businesses });
          break;
        case 'clear-data':
          this.clearData();
          sendResponse({ success: true });
          break;
        case 'export-data':
          sendResponse({ businesses: this.businesses });
          break;
        default:
          sendResponse({ error: 'Unknown action' });
      }
    });
  }

  private startScraping() {
    console.log('Starting scraping...');
    this.isActive = true;
    this.businesses = [];

    this.updateState({
      isActive: true,
      status: 'scraping',
      totalFound: 0
    });

    this.showOverlay();
    this.scrapeVisibleBusinesses();

    // Set up periodic scraping
    this.setupPeriodicScraping();
  }

  private stopScraping() {
    console.log('Stopping scraping...');
    this.isActive = false;

    this.updateState({
      isActive: false,
      status: 'stopped'
    });

    this.hideOverlay();
  }

  private clearData() {
    this.businesses = [];
    this.updateState({
      totalFound: 0
    });
    console.log('Cleared all data');
  }

  private setupPeriodicScraping() {
    const interval = setInterval(() => {
      if (!this.isActive) {
        clearInterval(interval);
        return;
      }
      this.scrapeVisibleBusinesses();
    }, 2000); // Scrape every 2 seconds
  }

  private scrapeVisibleBusinesses() {
    console.log('Scraping visible businesses...');

    // Look for business listings in the sidebar
    const businessElements = document.querySelectorAll('[data-result-index]');
    console.log(`Found ${businessElements.length} business elements`);

    businessElements.forEach((element, index) => {
      try {
        const business = this.extractBusinessData(element as HTMLElement);
        if (business && !this.businesses.some(b => b.placeId === business.placeId)) {
          this.businesses.push(business);
          console.log('Added business:', business.name);
        }
      } catch (error) {
        console.error('Error extracting business data:', error);
      }
    });

    this.updateState({
      totalFound: this.businesses.length
    });

    this.updateOverlay();
  }

  private extractBusinessData(element: HTMLElement): BusinessData | null {
    try {
      // Extract name
      const nameElement = element.querySelector('[role="button"] > div > div:first-child');
      const name = nameElement?.textContent?.trim() || '';

      // Extract address
      const addressElement = element.querySelector('[data-value="Address"]');
      const address = addressElement?.textContent?.trim() || '';

      // Extract rating and review count
      let rating: number | null = null;
      let reviewCount: number | null = null;

      const ratingElement = element.querySelector('[role="img"][aria-label*="stars"]');
      if (ratingElement) {
        const ariaLabel = ratingElement.getAttribute('aria-label') || '';
        const ratingMatch = ariaLabel.match(/(\d+\.?\d*) stars/);
        if (ratingMatch) {
          rating = parseFloat(ratingMatch[1]);
        }
      }

      const reviewElement = element.querySelector('[aria-label*="reviews"]');
      if (reviewElement) {
        const reviewText = reviewElement.textContent || '';
        const reviewMatch = reviewText.match(/\((\d+)\)/);
        if (reviewMatch) {
          reviewCount = parseInt(reviewMatch[1]);
        }
      }

      // Extract category
      const categoryElement = element.querySelector('[jsaction] > div:last-child > div:first-child');
      const category = categoryElement?.textContent?.trim() || '';

      // Generate a simple place ID based on name and address
      const placeId = btoa(name + address).replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);

      if (!name) return null;

      return {
        name,
        address,
        phone: '', // Phone not easily extractable from list view
        website: '', // Website not easily extractable from list view
        rating,
        reviewCount,
        category,
        placeId
      };
    } catch (error) {
      console.error('Error extracting business data:', error);
      return null;
    }
  }

  private updateState(updates: Partial<ScrapingState>) {
    this.state = { ...this.state, ...updates };

    // Send update to popup
    chrome.runtime.sendMessage({
      action: 'state-updated',
      state: this.state
    }).catch(() => {
      // Ignore errors if popup is not open
    });
  }

  private showOverlay() {
    this.hideOverlay();

    this.overlayElement = document.createElement('div');
    this.overlayElement.id = 'gmaps-scraper-overlay';
    this.overlayElement.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      background: white;
      border: 2px solid #4285f4;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      font-family: Arial, sans-serif;
      min-width: 250px;
      max-width: 300px;
    `;

    this.updateOverlay();
    document.body.appendChild(this.overlayElement);
  }

  private updateOverlay() {
    if (!this.overlayElement) return;

    this.overlayElement.innerHTML = `
      <div style="margin-bottom: 12px;">
        <h3 style="margin: 0 0 8px 0; color: #1a73e8; font-size: 16px;">Google Maps Scraper</h3>
        <div style="font-size: 14px; color: #5f6368;">
          Status: <span style="color: #137333; font-weight: bold;">${this.state.status}</span>
        </div>
        <div style="font-size: 14px; color: #5f6368; margin-top: 4px;">
          Found: <span style="color: #1a73e8; font-weight: bold;">${this.state.totalFound}</span> businesses
        </div>
      </div>
      <div style="display: flex; gap: 8px;">
        <button id="stop-scraping" style="
          background: #ea4335;
          color: white;
          border: none;
          padding: 8px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        ">Stop</button>
        <button id="export-data" style="
          background: #34a853;
          color: white;
          border: none;
          padding: 8px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        ">Export CSV</button>
      </div>
    `;

    // Add event listeners
    const stopButton = this.overlayElement.querySelector('#stop-scraping');
    const exportButton = this.overlayElement.querySelector('#export-data');

    stopButton?.addEventListener('click', () => this.stopScraping());
    exportButton?.addEventListener('click', () => this.exportData());
  }

  private hideOverlay() {
    if (this.overlayElement) {
      this.overlayElement.remove();
      this.overlayElement = null;
    }
  }

  private exportData() {
    if (this.businesses.length === 0) {
      alert('No data to export');
      return;
    }

    const csvContent = this.convertToCSV(this.businesses);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `google-maps-data-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log(`Exported ${this.businesses.length} businesses to CSV`);
  }

  private convertToCSV(data: BusinessData[]): string {
    const headers = ['Name', 'Address', 'Phone', 'Website', 'Rating', 'Reviews', 'Category', 'Place ID'];
    const csvRows = [headers.join(',')];

    data.forEach(business => {
      const row = [
        this.escapeCsvField(business.name),
        this.escapeCsvField(business.address),
        this.escapeCsvField(business.phone),
        this.escapeCsvField(business.website),
        business.rating?.toString() || '',
        business.reviewCount?.toString() || '',
        this.escapeCsvField(business.category),
        this.escapeCsvField(business.placeId)
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  private escapeCsvField(field: string | undefined): string {
    if (!field) return '';
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }
}

// Initialize the scraper
new GoogleMapsScraper();
