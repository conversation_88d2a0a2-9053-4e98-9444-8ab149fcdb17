import type { PlasmoCSConfig } from "plasmo";
import { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";

import { GoogleMapsDataExtractor } from "~utils/data-extractor";
import type { BusinessData, ScrapingState } from "~types/business";
import { ScrapingOverlay } from "~components/scraping-overlay";

export const config: PlasmoCSConfig = {
  matches: [
    "https://www.google.com/maps/*",
    "https://www.google.ad/maps/*",
    "https://www.google.ae/maps/*",
    "https://www.google.com.af/maps/*",
    "https://www.google.com.ag/maps/*",
    "https://www.google.com.ai/maps/*",
    "https://www.google.al/maps/*",
    "https://www.google.am/maps/*",
    "https://www.google.co.ao/maps/*",
    "https://www.google.com.ar/maps/*",
    "https://www.google.as/maps/*",
    "https://www.google.at/maps/*",
    "https://www.google.com.au/maps/*",
    "https://www.google.az/maps/*",
    "https://www.google.ba/maps/*",
    "https://www.google.com.bd/maps/*",
    "https://www.google.be/maps/*",
    "https://www.google.bf/maps/*",
    "https://www.google.bg/maps/*",
    "https://www.google.com.bh/maps/*",
    "https://www.google.bi/maps/*",
    "https://www.google.bj/maps/*",
    "https://www.google.com.bn/maps/*",
    "https://www.google.com.bo/maps/*",
    "https://www.google.com.br/maps/*",
    "https://www.google.bs/maps/*",
    "https://www.google.bt/maps/*",
    "https://www.google.co.bw/maps/*",
    "https://www.google.by/maps/*",
    "https://www.google.com.bz/maps/*",
    "https://www.google.ca/maps/*"
  ],
  run_at: "document_end"
};

class GoogleMapsScraper {
  private businesses: BusinessData[] = [];
  private seenPlaceIds = new Set<string>();
  private isActive = false;
  private state: ScrapingState = {
    isActive: false,
    status: 'idle',
    totalFound: 0,
    currentIndex: 0,
    hasNextPage: false
  };

  constructor() {
    this.initializeInterceptor();
    this.setupEventListeners();
  }

  private initializeInterceptor() {
    // Inject the HTTP interceptor script
    const script = document.createElement('script');
    script.textContent = `
      ${this.getInterceptorCode()}
    `;
    (document.head || document.documentElement).appendChild(script);
    script.remove();
  }

  private getInterceptorCode(): string {
    return `
      (function() {
        const originalXHR = window.XMLHttpRequest;
        const originalFetch = window.fetch;
        
        // Intercept XMLHttpRequest
        window.XMLHttpRequest = function() {
          const xhr = new originalXHR();
          const originalOpen = xhr.open;
          const originalSend = xhr.send;
          
          let requestUrl = '';
          
          xhr.open = function(method, url, ...args) {
            requestUrl = url;
            return originalOpen.apply(this, [method, url, ...args]);
          };
          
          xhr.addEventListener('readystatechange', function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
              if (requestUrl.includes('/search') || requestUrl.includes('maps/api')) {
                try {
                  window.dispatchEvent(new CustomEvent('gmaps-api-intercepted', {
                    detail: {
                      url: requestUrl,
                      data: xhr.responseText,
                      timestamp: Date.now()
                    }
                  }));
                } catch (e) {
                  console.warn('Failed to dispatch intercepted data:', e);
                }
              }
            }
          });
          
          return xhr;
        };
        
        // Copy static properties
        Object.setPrototypeOf(window.XMLHttpRequest, originalXHR);
        Object.defineProperty(window.XMLHttpRequest, 'prototype', {
          value: originalXHR.prototype,
          writable: false
        });
        
        console.log('🔍 Google Maps API interceptor initialized');
      })();
    `;
  }

  private setupEventListeners() {
    // Listen for intercepted API calls
    window.addEventListener('gmaps-api-intercepted', (event: any) => {
      this.handleInterceptedData(event.detail);
    });

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sendResponse);
    });
  }

  private handleInterceptedData(data: any) {
    if (!this.isActive) return;

    try {
      let responseData;
      
      if (typeof data.data === 'string') {
        // Try to parse JSON response
        if (data.data.startsWith(')]}\'')) {
          // Remove XSSI protection prefix
          responseData = JSON.parse(data.data.substring(4));
        } else {
          responseData = JSON.parse(data.data);
        }
      } else {
        responseData = data.data;
      }

      const extractedBusinesses = GoogleMapsDataExtractor.extractBusinessData(responseData);
      
      // Filter out duplicates
      const newBusinesses = extractedBusinesses.filter(business => {
        if (!business.placeId || this.seenPlaceIds.has(business.placeId)) {
          return false;
        }
        this.seenPlaceIds.add(business.placeId);
        return true;
      });

      if (newBusinesses.length > 0) {
        this.businesses.push(...newBusinesses);
        this.updateState({
          totalFound: this.businesses.length
        });

        // Save to storage
        this.saveToStorage();
        
        console.log(`📍 Found ${newBusinesses.length} new businesses (Total: ${this.businesses.length})`);
      }
    } catch (error) {
      console.error('Error processing intercepted data:', error);
    }
  }

  private handleMessage(message: any, sendResponse: (response: any) => void) {
    switch (message.action) {
      case 'start-scraping':
        this.startScraping();
        sendResponse({ success: true });
        break;
        
      case 'stop-scraping':
        this.stopScraping();
        sendResponse({ success: true });
        break;
        
      case 'get-state':
        sendResponse({ state: this.state, businesses: this.businesses });
        break;
        
      case 'clear-data':
        this.clearData();
        sendResponse({ success: true });
        break;
        
      case 'export-data':
        sendResponse({ businesses: this.businesses });
        break;
        
      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  private startScraping() {
    this.isActive = true;
    this.updateState({
      isActive: true,
      status: 'finding'
    });
    
    // Start auto-scrolling to load more results
    this.startAutoScroll();
    
    console.log('🚀 Started scraping Google Maps');
  }

  private stopScraping() {
    this.isActive = false;
    this.updateState({
      isActive: false,
      status: 'idle'
    });
    
    this.stopAutoScroll();
    
    console.log('⏹️ Stopped scraping Google Maps');
  }

  private clearData() {
    this.businesses = [];
    this.seenPlaceIds.clear();
    this.updateState({
      totalFound: 0,
      currentIndex: 0
    });
    
    // Clear storage
    chrome.storage.local.remove(['scrapedBusinesses']);
    
    console.log('🗑️ Cleared all scraped data');
  }

  private updateState(updates: Partial<ScrapingState>) {
    this.state = { ...this.state, ...updates };
    
    // Notify popup of state change
    chrome.runtime.sendMessage({
      action: 'state-updated',
      state: this.state
    }).catch(() => {
      // Popup might not be open, ignore error
    });
  }

  private async saveToStorage() {
    try {
      await chrome.storage.local.set({
        scrapedBusinesses: this.businesses,
        lastScrapeDate: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to save to storage:', error);
    }
  }

  private scrollInterval: number | null = null;

  private startAutoScroll() {
    if (this.scrollInterval) return;
    
    this.scrollInterval = window.setInterval(() => {
      if (!this.isActive) {
        this.stopAutoScroll();
        return;
      }
      
      // Find the results panel and scroll it
      const resultsPanel = document.querySelector('[role="main"]') || 
                          document.querySelector('.m6QErb') ||
                          document.querySelector('#pane');
      
      if (resultsPanel) {
        resultsPanel.scrollTop = resultsPanel.scrollHeight;
        
        // Check if we've reached the bottom
        const isAtBottom = resultsPanel.scrollTop + resultsPanel.clientHeight >= resultsPanel.scrollHeight - 10;
        
        this.updateState({
          hasNextPage: !isAtBottom
        });
      }
    }, 2000); // Scroll every 2 seconds
  }

  private stopAutoScroll() {
    if (this.scrollInterval) {
      clearInterval(this.scrollInterval);
      this.scrollInterval = null;
    }
  }
}

// Initialize the scraper
const scraper = new GoogleMapsScraper();

// Export for debugging
(window as any).googleMapsScraper = scraper;
