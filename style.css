/* Global styles for the extension */

/* Tailwind CSS base styles */
@import url('https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');

/* Custom styles for the extension */
.gmaps-scraper-overlay {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 999999 !important;
  position: fixed !important;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
}

.gmaps-scraper-overlay * {
  box-sizing: border-box;
}

.gmaps-scraper-button {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.gmaps-scraper-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.gmaps-scraper-button:active {
  transform: translateY(0);
}

.gmaps-scraper-button.primary {
  background-color: #3b82f6;
  color: white;
}

.gmaps-scraper-button.primary:hover {
  background-color: #2563eb;
}

.gmaps-scraper-button.success {
  background-color: #10b981;
  color: white;
}

.gmaps-scraper-button.success:hover {
  background-color: #059669;
}

.gmaps-scraper-button.danger {
  background-color: #ef4444;
  color: white;
}

.gmaps-scraper-button.danger:hover {
  background-color: #dc2626;
}

.gmaps-scraper-button.secondary {
  background-color: #6b7280;
  color: white;
}

.gmaps-scraper-button.secondary:hover {
  background-color: #4b5563;
}

.gmaps-scraper-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.gmaps-scraper-status-indicator.active {
  background-color: #10b981;
  animation: pulse 2s infinite;
}

.gmaps-scraper-status-indicator.paused {
  background-color: #f59e0b;
}

.gmaps-scraper-status-indicator.idle {
  background-color: #6b7280;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.gmaps-scraper-draggable {
  cursor: move;
  user-select: none;
}

.gmaps-scraper-draggable:active {
  cursor: grabbing;
}

/* Popup styles */
.gmaps-scraper-popup {
  width: 320px;
  max-height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.gmaps-scraper-popup .tab-button {
  transition: all 0.2s ease-in-out;
  border-bottom: 2px solid transparent;
}

.gmaps-scraper-popup .tab-button.active {
  border-bottom-color: #3b82f6;
  color: #3b82f6;
}

.gmaps-scraper-popup .tab-button:hover {
  color: #1f2937;
  background-color: #f9fafb;
}

/* Business card styles */
.business-card {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease-in-out;
}

.business-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.business-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.business-address {
  color: #6b7280;
  font-size: 12px;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.business-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: #6b7280;
}

.business-detail-item {
  display: flex;
  align-items: center;
  gap: 3px;
}

/* Scrollbar styles */
.gmaps-scraper-scrollable::-webkit-scrollbar {
  width: 6px;
}

.gmaps-scraper-scrollable::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.gmaps-scraper-scrollable::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.gmaps-scraper-scrollable::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease-in-out;
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000000;
  max-width: 300px;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  border-left: 4px solid #10b981;
}

.notification.error {
  border-left: 4px solid #ef4444;
}

.notification.warning {
  border-left: 4px solid #f59e0b;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .gmaps-scraper-overlay {
    width: calc(100vw - 40px) !important;
    left: 20px !important;
    right: 20px !important;
  }
  
  .gmaps-scraper-popup {
    width: 100%;
    max-width: 320px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .gmaps-scraper-overlay {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .business-card {
    background: #374151;
    border-color: #4b5563;
  }
  
  .business-card:hover {
    border-color: #60a5fa;
  }
  
  .business-name {
    color: #f9fafb;
  }
  
  .business-address {
    color: #d1d5db;
  }
  
  .business-details {
    color: #d1d5db;
  }
}
