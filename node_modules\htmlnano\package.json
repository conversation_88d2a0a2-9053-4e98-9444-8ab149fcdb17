{"name": "htmlnano", "version": "2.1.2", "description": "Modular HTML minifier, built on top of the PostHTML", "main": "index.cjs", "module": "index.mjs", "source": "index.mjs", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./index.mjs": {"types": "./index.d.mts", "import": "./index.mjs"}, "./index.cjs": {"types": "./index.d.cts", "require": "./index.cjs"}}, "types": "index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"clean": "rimraf lib/*.cjs lib/**/*.cjs", "compile": "npm run clean && babel -d lib/ lib/ --out-file-extension .cjs", "lint": "eslint --fix *.mjs lib/*.mjs lib/**/*.mjs test/*.mjs test/**/*.mjs", "pretest": "npm run lint && npm run compile", "test": ":", "posttest": "mocha --timeout 5000 --require @babel/register --recursive --check-leaks --globals addresses", "prepare": "npm run compile"}, "keywords": ["posthtml", "posthtml-plugin", "html", "postproccessor", "minifier"], "babel": {"presets": [["@babel/env", {"targets": {"node": 10}}]], "plugins": [["@aminya/babel-plugin-replace-import-extension", {"extMapping": {".mjs": ".cjs"}}]]}, "dependencies": {"cosmiconfig": "^9.0.0", "posthtml": "^0.16.5"}, "devDependencies": {"@aminya/babel-plugin-replace-import-extension": "1.2.0", "@babel/cli": "^7.15.7", "@babel/core": "^7.15.5", "@babel/eslint-parser": "^7.17.0", "@babel/preset-env": "^7.15.6", "@babel/register": "^7.15.3", "cssnano": "^7.0.0", "eslint": "^8.12.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-path-import-extension": "^0.9.0", "expect": "^29.0.0", "mocha": "^11.0.1", "postcss": "^8.3.11", "purgecss": "^7.0.2", "relateurl": "^0.2.7", "rimraf": "^6.0.0", "srcset": "5.0.1", "svgo": "^3.0.2", "terser": "^5.21.0", "uncss": "^0.17.3"}, "peerDependencies": {"cssnano": "^7.0.0", "postcss": "^8.3.11", "purgecss": "^7.0.2", "relateurl": "^0.2.7", "srcset": "5.0.1", "svgo": "^3.0.2", "terser": "^5.10.0", "uncss": "^0.17.3"}, "peerDependenciesMeta": {"cssnano": {"optional": true}, "postcss": {"optional": true}, "purgecss": {"optional": true}, "relateurl": {"optional": true}, "srcset": {"optional": true}, "svgo": {"optional": true}, "terser": {"optional": true}, "uncss": {"optional": true}}, "repository": {"type": "git", "url": "git://github.com/posthtml/htmlnano.git"}, "bugs": {"url": "https://github.com/posthtml/htmlnano/issues"}, "homepage": "https://github.com/posthtml/htmlnano"}