# Quick Installation Guide

## ✅ Extension Ready to Install!

The extension has been successfully built and is ready to install in Chrome.

## Prerequisites

- Chrome browser
- The built extension files (already created in `build/chrome-mv3-prod`)

## Step-by-Step Installation

### 1. Load in Chrome

1. Open Chrome and navigate to: `chrome://extensions/`

2. Enable "Developer mode" (toggle in the top right corner)

3. Click "Load unpacked" button

4. Navigate to your project folder and select: `build/chrome-mv3-prod`

5. The extension should now appear in your extensions list with the name "Personal Google Maps Scraper"

### 2. Pin the Extension (Recommended)

1. Click the puzzle piece icon in Chrome's toolbar
2. Find "Personal Google Maps Scraper"
3. Click the pin icon to keep it visible in your toolbar

## If You Need to Rebuild

If you make changes to the code, you'll need to rebuild:

### Prerequisites for Development
- Node.js (version 16 or higher)

### Rebuild Steps
```bash
# Install dependencies (first time only)
npm install

# Build the extension
npm run build

# Then reload the extension in Chrome
```

### Development Mode (Optional)
For live development with hot reload:
```bash
npm run dev
```
Then load `build/chrome-mv3-dev` instead.

### 5. Pin the Extension (Optional)

1. Click the puzzle piece icon in Chrome's toolbar
2. Find "Personal Google Maps Scraper"
3. Click the pin icon to keep it visible

## First Use

1. Navigate to [Google Maps](https://www.google.com/maps)
2. Search for businesses (e.g., "restaurants near me")
3. Click the extension icon in your toolbar
4. Click "Start Scraping" in the popup
5. Watch as business data is collected automatically!

## Troubleshooting

### Extension not loading
- Make sure you selected the correct build folder
- Check that all files were built successfully
- Try refreshing the extensions page

### No data being collected
- Ensure you're on a Google Maps search results page
- Make sure the extension is enabled
- Try refreshing the Google Maps page

### Build errors
- Make sure Node.js is installed correctly
- Delete `node_modules` and run `npm install` again
- Check that you have the latest version of npm

## Development Mode

If you want to modify the extension:

1. Run `npm run dev` instead of `npm run build`
2. Load the `build/chrome-mv3-dev` folder in Chrome
3. Changes will be automatically reloaded
4. Check the console for any errors

## Updating

To update the extension:

1. Pull the latest changes (if using Git)
2. Run `npm install` to update dependencies
3. Run `npm run build` to rebuild
4. Reload the extension in Chrome (click the refresh icon)

## Uninstalling

To remove the extension:

1. Go to `chrome://extensions/`
2. Find "Personal Google Maps Scraper"
3. Click "Remove"
4. Confirm the removal

Your collected data will be removed from Chrome's storage automatically.

## Support

If you encounter issues:

1. Check the browser console (F12) for error messages
2. Make sure you're using a supported Chrome version
3. Verify that Google Maps is working normally
4. Try disabling other extensions temporarily

## Security Note

This extension only works locally on your computer. No data is sent to external servers, and all collected information stays on your device.
