# Quick Installation Guide

## Prerequisites

- Node.js (version 16 or higher)
- Chrome browser
- Git (optional, for cloning)

## Step-by-Step Installation

### 1. Get the Code

**Option A: Download ZIP**
- Download the project as a ZIP file
- Extract to a folder on your computer

**Option B: Clone with Git**
```bash
git clone <repository-url>
cd google-maps-scraper-personal
```

### 2. Install Dependencies

Open a terminal in the project folder and run:

```bash
npm install
```

This will install all required dependencies including:
- Plasmo framework
- React and TypeScript
- Export libraries (xlsx, papaparse)
- UI components (lucide-react)

### 3. Build the Extension

For production use:
```bash
npm run build
```

For development (with hot reload):
```bash
npm run dev
```

### 4. Load in Chrome

1. Open Chrome and navigate to: `chrome://extensions/`

2. Enable "Developer mode" (toggle in the top right corner)

3. Click "Load unpacked" button

4. Select the build folder:
   - For production: `build/chrome-mv3-prod`
   - For development: `build/chrome-mv3-dev`

5. The extension should now appear in your extensions list

### 5. Pin the Extension (Optional)

1. Click the puzzle piece icon in Chrome's toolbar
2. Find "Personal Google Maps Scraper"
3. Click the pin icon to keep it visible

## First Use

1. Navigate to [Google Maps](https://www.google.com/maps)
2. Search for businesses (e.g., "restaurants near me")
3. Click the extension icon in your toolbar
4. Click "Start Scraping" in the popup
5. Watch as business data is collected automatically!

## Troubleshooting

### Extension not loading
- Make sure you selected the correct build folder
- Check that all files were built successfully
- Try refreshing the extensions page

### No data being collected
- Ensure you're on a Google Maps search results page
- Make sure the extension is enabled
- Try refreshing the Google Maps page

### Build errors
- Make sure Node.js is installed correctly
- Delete `node_modules` and run `npm install` again
- Check that you have the latest version of npm

## Development Mode

If you want to modify the extension:

1. Run `npm run dev` instead of `npm run build`
2. Load the `build/chrome-mv3-dev` folder in Chrome
3. Changes will be automatically reloaded
4. Check the console for any errors

## Updating

To update the extension:

1. Pull the latest changes (if using Git)
2. Run `npm install` to update dependencies
3. Run `npm run build` to rebuild
4. Reload the extension in Chrome (click the refresh icon)

## Uninstalling

To remove the extension:

1. Go to `chrome://extensions/`
2. Find "Personal Google Maps Scraper"
3. Click "Remove"
4. Confirm the removal

Your collected data will be removed from Chrome's storage automatically.

## Support

If you encounter issues:

1. Check the browser console (F12) for error messages
2. Make sure you're using a supported Chrome version
3. Verify that Google Maps is working normally
4. Try disabling other extensions temporarily

## Security Note

This extension only works locally on your computer. No data is sent to external servers, and all collected information stays on your device.
