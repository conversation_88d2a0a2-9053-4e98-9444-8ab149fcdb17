var e,t;"function"==typeof(e=globalThis.define)&&(t=e,e=null),function(t,a,s,r,o){var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},i="function"==typeof n[r]&&n[r],c=i.cache||{},l="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function u(e,a){if(!c[e]){if(!t[e]){var s="function"==typeof n[r]&&n[r];if(!a&&s)return s(e,!0);if(i)return i(e,!0);if(l&&"string"==typeof e)return l(e);var o=Error("Cannot find module '"+e+"'");throw o.code="MODULE_NOT_FOUND",o}g.resolve=function(a){var s=t[e][1][a];return null!=s?s:a},g.cache={};var d=c[e]=new u.Module(e);t[e][0].call(d.exports,g,d,d.exports,this)}return c[e].exports;function g(e){var t=g.resolve(e);return!1===t?{}:u(t)}}u.isParcelRequire=!0,u.Module=function(e){this.id=e,this.bundle=u,this.exports={}},u.modules=t,u.cache=c,u.parent=i,u.register=function(e,a){t[e]=[function(e,t){t.exports=a},{}]},Object.defineProperty(u,"root",{get:function(){return n[r]}}),n[r]=u;for(var d=0;d<a.length;d++)u(a[d]);if(s){var g=u(s);"object"==typeof exports&&"undefined"!=typeof module?module.exports=g:"function"==typeof e&&e.amd?e(function(){return g}):o&&(this[o]=g)}}({kgW6q:[function(e,t,a){e("../../../background")},{"../../../background":"8VaxY"}],"8VaxY":[function(e,t,a){var s=e("@parcel/transformer-js/src/esmodule-helpers.js");s.defineInteropFlag(a),s.export(a,"backgroundUtils",()=>o);var r=e("~utils/storage");new class{constructor(){this.setupEventListeners(),this.initializeExtension()}setupEventListeners(){chrome.runtime.onInstalled.addListener(e=>{this.handleInstallation(e)}),chrome.runtime.onMessage.addListener((e,t,a)=>(this.handleMessage(e,t,a),!0)),chrome.tabs.onUpdated.addListener((e,t,a)=>{this.handleTabUpdate(e,t,a)}),chrome.runtime.onStartup.addListener(()=>{this.handleStartup()})}async initializeExtension(){try{let e=await (0,r.StorageManager).loadConfig();await (0,r.StorageManager).saveConfig(e),console.log("\uD83D\uDE80 Google Maps Scraper extension initialized")}catch(e){console.error("Failed to initialize extension:",e)}}handleInstallation(e){"install"===e.reason?this.showWelcomeMessage():"update"===e.reason&&this.handleUpdate(e.previousVersion)}showWelcomeMessage(){chrome.tabs.create({url:"https://www.google.com/maps",active:!0}),chrome.notifications.create({type:"basic",iconUrl:"icon48.png",title:"Google Maps Scraper Installed",message:"Navigate to Google Maps and click the extension icon to start scraping business data!"})}handleUpdate(e){console.log(`Extension updated from ${e} to ${chrome.runtime.getManifest().version}`),this.migrateData(e)}async migrateData(e){try{console.log("Data migration completed")}catch(e){console.error("Data migration failed:",e)}}async handleMessage(e,t,a){try{switch(e.action){case"get-storage-stats":let t=await (0,r.StorageManager).getStorageStats();a({success:!0,data:t});break;case"export-all-data":let s=await (0,r.StorageManager).exportAllData();a({success:!0,data:s});break;case"import-all-data":await (0,r.StorageManager).importAllData(e.data),a({success:!0});break;case"cleanup-old-data":let o=await (0,r.StorageManager).cleanupOldData(e.daysToKeep);a({success:!0,cleanedCount:o});break;case"get-config":let n=await (0,r.StorageManager).loadConfig();a({success:!0,config:n});break;case"save-config":await (0,r.StorageManager).saveConfig(e.config),a({success:!0});break;case"ping":a({success:!0,message:"pong"});break;default:a({success:!1,error:"Unknown action"})}}catch(e){console.error("Error handling message:",e),a({success:!1,error:e.message})}}handleTabUpdate(e,t,a){"complete"===t.status&&a.url&&this.isGoogleMapsUrl(a.url)&&this.injectContentScript(e)}isGoogleMapsUrl(e){return[/^https:\/\/www\.google\.[a-z.]+\/maps/,/^https:\/\/maps\.google\.[a-z.]+/].some(t=>t.test(e))}async injectContentScript(e){try{let t=await chrome.scripting.executeScript({target:{tabId:e},func:()=>!0===window.googleMapsScraperInjected});t[0]?.result||(await chrome.scripting.executeScript({target:{tabId:e},files:["contents/google-maps.js"]}),await chrome.scripting.executeScript({target:{tabId:e},func:()=>{window.googleMapsScraperInjected=!0}}),console.log(`Content script injected into tab ${e}`))}catch(e){console.error("Failed to inject content script:",e)}}handleStartup(){console.log("Extension started")}},chrome.runtime.onSuspend.addListener(()=>{console.log("Extension context suspended")});let o={async sendToActiveTab(e){try{let[t]=await chrome.tabs.query({active:!0,currentWindow:!0});if(t.id)return await chrome.tabs.sendMessage(t.id,e);throw Error("No active tab found")}catch(e){throw console.error("Failed to send message to active tab:",e),e}},async isCurrentTabGoogleMaps(){try{let[e]=await chrome.tabs.query({active:!0,currentWindow:!0});return!!e.url&&/google\.[a-z.]+\/maps/.test(e.url)}catch(e){return console.error("Failed to check current tab:",e),!1}},async openGoogleMaps(){try{await chrome.tabs.create({url:"https://www.google.com/maps",active:!0})}catch(e){throw console.error("Failed to open Google Maps:",e),e}}}},{"~utils/storage":"fmp01","@parcel/transformer-js/src/esmodule-helpers.js":"f6DG4"}],fmp01:[function(e,t,a){var s=e("@parcel/transformer-js/src/esmodule-helpers.js");s.defineInteropFlag(a),s.export(a,"StorageManager",()=>r),s.export(a,"StateManager",()=>o);class r{static{this.STORAGE_KEYS={BUSINESSES:"scrapedBusinesses",CONFIG:"scrapingConfig",LAST_SCRAPE:"lastScrapeDate",SESSION_DATA:"sessionData"}}static async saveBusinesses(e){try{await chrome.storage.local.set({[this.STORAGE_KEYS.BUSINESSES]:e,[this.STORAGE_KEYS.LAST_SCRAPE]:new Date().toISOString()})}catch(e){throw console.error("Failed to save businesses:",e),e}}static async loadBusinesses(){try{let e=await chrome.storage.local.get([this.STORAGE_KEYS.BUSINESSES]);return e[this.STORAGE_KEYS.BUSINESSES]||[]}catch(e){return console.error("Failed to load businesses:",e),[]}}static async addBusinesses(e){try{let t=await this.loadBusinesses(),a=new Set(t.map(e=>e.placeId)),s=e.filter(e=>e.placeId&&!a.has(e.placeId)),r=[...t,...s];return await this.saveBusinesses(r),r}catch(e){throw console.error("Failed to add businesses:",e),e}}static async clearBusinesses(){try{await chrome.storage.local.remove([this.STORAGE_KEYS.BUSINESSES,this.STORAGE_KEYS.LAST_SCRAPE])}catch(e){throw console.error("Failed to clear businesses:",e),e}}static async saveConfig(e){try{await chrome.storage.local.set({[this.STORAGE_KEYS.CONFIG]:e})}catch(e){throw console.error("Failed to save config:",e),e}}static async loadConfig(){try{let e=await chrome.storage.local.get([this.STORAGE_KEYS.CONFIG]);return e[this.STORAGE_KEYS.CONFIG]||this.getDefaultConfig()}catch(e){return console.error("Failed to load config:",e),this.getDefaultConfig()}}static getDefaultConfig(){return{autoScroll:!0,scrollDelay:2e3,maxResults:1e3,includeEmails:!1,includeSocialMedia:!1}}static async saveSessionData(e){try{await chrome.storage.session.set({[this.STORAGE_KEYS.SESSION_DATA]:e})}catch(t){console.error("Failed to save session data:",t),await chrome.storage.local.set({[this.STORAGE_KEYS.SESSION_DATA]:e})}}static async loadSessionData(){try{let e=await chrome.storage.session.get([this.STORAGE_KEYS.SESSION_DATA]);return e[this.STORAGE_KEYS.SESSION_DATA]||(e=await chrome.storage.local.get([this.STORAGE_KEYS.SESSION_DATA])),e[this.STORAGE_KEYS.SESSION_DATA]||{}}catch(e){return console.error("Failed to load session data:",e),{}}}static async getStorageStats(){try{let[e,t]=await Promise.all([this.loadBusinesses(),chrome.storage.local.get([this.STORAGE_KEYS.LAST_SCRAPE])]),a=new Blob([JSON.stringify(e)]).size;return{businessCount:e.length,storageUsed:a,lastScrapeDate:t[this.STORAGE_KEYS.LAST_SCRAPE]||null}}catch(e){return console.error("Failed to get storage stats:",e),{businessCount:0,storageUsed:0,lastScrapeDate:null}}}static async exportAllData(){try{let[e,t]=await Promise.all([this.loadBusinesses(),this.loadConfig()]),a=await chrome.storage.local.get([this.STORAGE_KEYS.LAST_SCRAPE]);return{businesses:e,config:t,lastScrapeDate:a[this.STORAGE_KEYS.LAST_SCRAPE]||new Date().toISOString()}}catch(e){throw console.error("Failed to export all data:",e),e}}static async importAllData(e){try{await chrome.storage.local.set({[this.STORAGE_KEYS.BUSINESSES]:e.businesses,[this.STORAGE_KEYS.CONFIG]:e.config,[this.STORAGE_KEYS.LAST_SCRAPE]:e.lastScrapeDate})}catch(e){throw console.error("Failed to import all data:",e),e}}static async cleanupOldData(e=30){try{let t=await this.loadBusinesses(),a=new Date;a.setDate(a.getDate()-e);let s=await chrome.storage.local.get([this.STORAGE_KEYS.LAST_SCRAPE]),r=s[this.STORAGE_KEYS.LAST_SCRAPE];if(r&&new Date(r)<a)return await this.clearBusinesses(),t.length;return 0}catch(e){return console.error("Failed to cleanup old data:",e),0}}static onStorageChanged(e){chrome.storage.onChanged.addListener((t,a)=>{"local"===a&&e(t)})}static removeStorageListener(e){chrome.storage.onChanged.removeListener(e)}}class o{static{this.listeners=new Map}static subscribe(e,t){return this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t),()=>{let a=this.listeners.get(e);if(a){let e=a.indexOf(t);e>-1&&a.splice(e,1)}}}static emit(e,t){let a=this.listeners.get(e);a&&a.forEach(e=>{try{e(t)}catch(e){console.error("Error in state change callback:",e)}})}static clearListeners(){this.listeners.clear()}}},{"@parcel/transformer-js/src/esmodule-helpers.js":"f6DG4"}],f6DG4:[function(e,t,a){a.interopDefault=function(e){return e&&e.__esModule?e:{default:e}},a.defineInteropFlag=function(e){Object.defineProperty(e,"__esModule",{value:!0})},a.exportAll=function(e,t){return Object.keys(e).forEach(function(a){"default"===a||"__esModule"===a||t.hasOwnProperty(a)||Object.defineProperty(t,a,{enumerable:!0,get:function(){return e[a]}})}),t},a.export=function(e,t,a){Object.defineProperty(e,t,{enumerable:!0,get:a})}},{}]},["kgW6q"],"kgW6q","parcelRequireb3c2"),globalThis.define=t;