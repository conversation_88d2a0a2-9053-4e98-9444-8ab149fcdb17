# Personal Google Maps Scraper

A Chrome extension for personal use to extract business data from Google Maps search results. This tool helps you collect business information including names, addresses, phone numbers, websites, ratings, and more.

## ⚠️ Important Notice

This extension is for **personal use only**. Please respect Google's Terms of Service and use this tool responsibly. Do not use it for commercial purposes or large-scale data collection that might violate Google's policies.

## Features

- 🔍 **Real-time Data Extraction**: Automatically captures business data as you browse Google Maps
- 📊 **Comprehensive Data**: Extracts names, addresses, phone numbers, websites, ratings, categories, and more
- 📁 **Multiple Export Formats**: Export data to CSV, Excel, or JSON
- 🎯 **Smart Filtering**: Automatically removes duplicates and validates data
- 💾 **Local Storage**: All data is stored locally in your browser
- 🎨 **Clean Interface**: Intuitive popup and overlay controls
- 🔄 **Auto-scroll**: Automatically scrolls to load more results
- 📱 **Responsive Design**: Works on different screen sizes

## Data Fields Collected

- Business Name
- Full Address
- Street & Municipality
- Categories
- Phone Number(s)
- Website & Domain
- Latitude & Longitude
- Plus Code
- Claimed Status
- Review Count & Average Rating
- Review URL
- Google Maps URL
- Opening Hours
- Featured Image
- Business Identifiers (CID, Place ID, etc.)

## Installation

### Option 1: Build from Source (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd google-maps-scraper-personal
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the extension**:
   ```bash
   npm run build
   ```

4. **Load in Chrome**:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the `build/chrome-mv3-prod` folder

### Option 2: Development Mode

1. **Start development server**:
   ```bash
   npm run dev
   ```

2. **Load in Chrome**:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked"
   - Select the `build/chrome-mv3-dev` folder

## Usage

### Getting Started

1. **Navigate to Google Maps**: Open [Google Maps](https://www.google.com/maps) in Chrome
2. **Search for businesses**: Use Google Maps search to find businesses (e.g., "restaurants in New York")
3. **Open the extension**: Click the extension icon in your browser toolbar
4. **Start scraping**: Click the "Start Scraping" button in the popup

### Using the Extension

#### Popup Interface
- **Control Tab**: Start/stop scraping, view status, export data
- **Data Tab**: Preview collected business data
- **Settings Tab**: Configure scraping behavior

#### Overlay on Google Maps
- A draggable overlay appears on Google Maps pages
- Shows real-time scraping status and count
- Quick access to start/stop and export functions
- Can be minimized to save screen space

### Scraping Process

1. **Start scraping** using the popup or overlay
2. The extension will **automatically scroll** to load more results
3. **Business data is extracted** in real-time as Google Maps loads results
4. **Duplicates are filtered** automatically
5. **Stop scraping** when you have enough data or manually

### Exporting Data

1. Click the **Export** button in the popup or overlay
2. Choose your preferred format:
   - **CSV**: For spreadsheet applications
   - **Excel**: For Microsoft Excel
   - **JSON**: For developers or data analysis

## Configuration

### Settings Options

- **Auto-scroll delay**: Time between scroll actions (1-10 seconds)
- **Max results**: Maximum number of businesses to collect
- **Auto-export**: Automatically export when scraping finishes

### Storage Management

- All data is stored locally in your browser
- Use the "Clear Data" button to remove collected data
- Export data regularly for backup

## Technical Details

### Architecture

- **Framework**: Plasmo (Chrome Extension Framework)
- **Language**: TypeScript + React
- **UI Library**: Tailwind CSS + Lucide Icons
- **Data Processing**: Custom Google Maps API response parser
- **Storage**: Chrome Extension Storage API

### How It Works

1. **HTTP Interception**: Hooks into Google Maps API calls using XMLHttpRequest interception
2. **Data Parsing**: Extracts business information from API responses
3. **Real-time Processing**: Processes data as it's received from Google Maps
4. **Storage**: Saves data locally using Chrome's storage API
5. **Export**: Converts data to various formats for download

## Troubleshooting

### Common Issues

**Extension not working on Google Maps**:
- Refresh the Google Maps page
- Make sure you're on a Google Maps search results page
- Check that the extension is enabled

**No data being collected**:
- Ensure you've started scraping using the popup
- Try searching for businesses in Google Maps
- Check the browser console for errors

**Export not working**:
- Make sure you have data collected first
- Check your browser's download settings
- Try a different export format

### Browser Console

For debugging, open the browser console (F12) and look for:
- `🔍 HTTP Interceptor started`
- `📍 Found X new businesses`
- Any error messages

## Limitations

- **Personal Use Only**: Not for commercial or large-scale use
- **Rate Limiting**: Respect Google's rate limits
- **Data Accuracy**: Data quality depends on Google Maps data
- **Browser Dependent**: Only works in Chrome-based browsers

## Privacy & Ethics

- **Local Storage**: All data stays on your device
- **No External Servers**: No data is sent to external servers
- **Respect ToS**: Use responsibly and respect Google's Terms of Service
- **Personal Use**: Intended for personal research and small-scale use only

## Development

### Project Structure

```
├── contents/           # Content scripts
├── components/         # React components
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
├── background.ts      # Background script
├── popup.tsx          # Popup interface
└── style.css          # Global styles
```

### Building

```bash
# Development build
npm run dev

# Production build
npm run build

# Package for distribution
npm run package
```

## License

This project is for personal use only. Please respect Google's Terms of Service and use responsibly.

## Disclaimer

This tool is provided as-is for educational and personal use. The developers are not responsible for any misuse or violations of terms of service. Always ensure you comply with applicable laws and website terms of service when scraping data.
