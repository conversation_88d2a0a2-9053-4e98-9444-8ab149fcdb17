{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iBAAiB;AACjB,4BAAM,OAAO;;AAIb,SAAS,mCAAc,IAAI,EAAE,OAAO;IAClC,IAAI,UAAU,QAAQ,YAAY,YAChC,QAAQ,UAAU,QAAQ,IAAI;IAEhC,IAAI,CAAC,SACH,OAAO;IAGT,UAAU,QAAQ,MAAM;IACxB,IAAI,QAAQ,QAAQ,QAAQ,IAC1B,OAAO;IAET,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAK;QACvC,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC;QACnB,IAAI,KAAK,KAAK,OAAO,CAAC,EAAE,QAAQ,kBAAkB,GAChD,OAAO;IAEX;IACA,OAAO;AACT;AAEA,SAAS,gCAAW,IAAI,EAAE,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,UAClC,OAAO;IAET,OAAO,mCAAa,MAAM;AAC5B;AAEA,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE;IAC/B,eAAQ,MAAM,SAAU,EAAE,EAAE,IAAI;QAC9B,GAAG,IAAI,KAAK,QAAQ,gCAAU,MAAM,MAAM;IAC5C;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO;IAC1B,OAAO,gCAAU,mBAAY,OAAO,MAAM;AAC5C;;;;;ACzCA,iBAAiB;AACjB,4BAAM,OAAO;;AAIb,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE;IAC/B,eAAQ,MAAM,SAAU,EAAE,EAAE,IAAI;QAC9B,GAAG,IAAI,KAAK,QAAQ,gCAAU,MAAM;IACtC;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO;IAC1B,OAAO,gCAAU,mBAAY,OAAO;AACtC;AAEA,SAAS,gCAAW,IAAI,EAAE,OAAO;IAC/B,OAAO,KAAK,YAAY,gCAAU,MAAM;AAC1C;AAEA,SAAS,gCAAW,IAAI,EAAE,OAAO;IAC/B,IAAI,MAAM,KAAK;IACf,IAAI,MAAM,KAAK;IACf,IAAI,MAAM,KAAK;IAEf,IAAI,QAAQ,QAAQ,QAAQ,YAC1B,QAAQ,MAAM,QAAQ,UAAU,QAAQ;IAC1C,IAAI,QAAQ,QAAQ,QAAQ,YAC1B,QAAQ,MAAM,QAAQ,UAAU,QAAQ;IAE1C,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,KAAK,IAAI;IAEb,IAAI,MAAM,AAAC,MAAM,KACf,AAAC,MAAM,KAAM,QAAQ,SACrB,AAAC,MAAM,KAAM,QAAQ,SACrB,AAAC,MAAM,MAAO,UAAU;IAE1B,OAAO;AACT;;;;;ACxCA;AACA,iBAAiB,CAAA;IAChB,OAAO,QAAQ,CAAC;IAEhB,MAAM,MAAM,KAAK,OAAO,QAAQ;IAChC,MAAM,WAAW,KAAK,YAAY,QAAQ;IAE1C,IAAI,aAAa,SAChB,OAAO;IAGR,OAAO,OAAO,KAAK,KAAK,KAAK,CAAA,IAAK,EAAE,kBAAkB,WAAW;AAClE;;;;;ACZA;;qCAEI;;yCACA;;;AAGJ,IAAI,+BAAS,UAAG;AAChB,IAAI,mCAAa,UAAG;AACpB,IAAI,kCAAY,UAAG,aAAa;AAEhC,IAAI,uCAAiB,QAAQ,YAAY;AAEzC,IAAI,sCAAgB,SAAS,WAAW,EAAE,QAAQ;IAC9C,6BAAO,aAAa,gCAAU,MAC9B,SAAS,GAAG;QACR,SAAS,CAAC;IACd;AACJ;AAEA,IAAI,0CAAoB,SAAS,WAAW;IACxC,IAAG;QACC,iCAAW,aAAa,gCAAU;QAClC,OAAO;IACX,EAAC,OAAM,GAAE;QACL,OAAO;IACX;AACJ;AAEA,IAAI,wCAAkB,SAAS,WAAW,EAAE,QAAQ;IAChD,6BAAO,aAAa,gCAAU,OAAO,gCAAU,MAC3C,SAAS,GAAG;QACZ,SAAS,MAAM,CAAC;IACpB;AACJ;AAEA,IAAI,4CAAsB,SAAS,WAAW;IAC1C,IAAG;QACC,iCAAW,aAAa,gCAAU,OAAO,gCAAU;QACnD,OAAO;IACX,EAAC,OAAM,GAAE;QACL,OAAO;IACX;AACJ;AAEA,IAAI,0CAAoB,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ;IAEtE,oCAAc,aAAa,SAAS,MAAM;QAEtC,IAAG,CAAC,QAAO;YACP,IAAI,QAAQ,+BAAK,gBAAgB,qBAC3B,iBACA,oBAAoB,qBAAqB,eACzC,SAAU,KAAK,EAAE,MAAM,EAAE,MAAM;gBAC3B,SAAS,MAAM,CAAC,CAAC;YACrB;YACN;QACJ;QAEA,sCAAgB,aAAa;IACjC;AAEJ;AAEA,IAAI,6CAAuB,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ;IAC3E,mGAAmG;IACnG,IAAI,CAAE,uFAAuF,KAAK,cAAe;QAC/G,SAAS,MAAM;QACf;IACF;IACA,IAAI,QAAQ,+BAAK,WAAW,oBAC1B,SAAU,KAAK;QACb,IAAI,UAAU,MACZ,SAAS,MAAM;aAEf,SAAS,MAAM;IAEnB;AAEJ;AAEA,IAAI,8CAAwB,SAAS,WAAW,EAAE,kBAAkB;IAClE,IAAG,wCAAkB,cACjB,IAAI;QACF,IAAI,SAAS,mCAAS,gBAAgB,qBAChC,iBACA,oBAAoB,qBAAqB;QAE/C,OAAO,CAAC,CAAC;IACX,EAAE,OAAO,OAAO;QACd,OAAO;IACT;IAEJ,OAAO,0CAAoB;AAC7B;AAEA,IAAI,iDAA2B,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ;IAC/E,mGAAmG;IACnG,IAAI,CAAE,uFAAuF,KAAK,cAChG,OAAO;IAET,IAAI;QACA,IAAI,SAAS,mCAAS,WAAW,oBAAoB;YAAC,OAAO,EAAE;QAAA;QAC/D,OAAO,CAAC,CAAC;IACb,EAAE,OAAO,OAAO;QACZ,OAAO;IACX;AACF;AAEA,IAAI,mCAAa,SAAS,CAAC;IACzB,IAAI,qBAAqB,KAAK,IAAI;QAChC,IAAI,MAAI,EAAE,QAAQ,MAAK,WAAS;QAChC,IAAI,EAAE,QAAQ,aAAa,IAAI,4CAA4C;SACxE,QAAQ,UAAU,QAAS,0EAA0E;IAC1G;IACA,OAAO;AACT;AAEA,IAAI,sCACF,mCAAa,SAAS,CAAC;IACrB,IAAI,aAAa,OAAO,KAAK;IAC7B,IAAI,YAAY;QACd,IAAI,UAAU,MAAM,oBAAa,KAAK;QACtC,IAAI,WAAW,MAAM,qBAAc,KAAK;QACxC,OAAO,UAAU,MAAM;IACzB;IACA,OAAO,MAAM,IAAI;AACnB;AAGF,iBAAiB,SAAS,cAAc,WAAW,EAAE,QAAQ;IAC3D,IAAI,qBAAqB,iCAAW;IACpC,IAAI,CAAC,YAAY,OAAO,YAAY,aAClC,OAAO,IAAI,QAAQ,SAAS,OAAO,EAAE,MAAM;QACzC,cAAc,aAAa,SAAS,KAAK,EAAE,MAAM;YAC/C,IAAI,QACF,QAAQ;iBAER,OAAO;QAEX;IACF;IAEF,IAAI,sCACF,2CAAqB,aAAa,oBAAoB;SAEtD,wCAAkB,aAAa,oBAAoB;AAEvD;AAEA,eAAe,OAAO,SAAS,WAAW;IACxC,IAAI,qBAAqB,iCAAW;IACpC,IAAI,sCACF,OAAO,+CAAyB,aAAa;SAE7C,OAAO,4CAAsB,aAAa;AAE9C;;;;;;;;;;;;;AC3JA,IAAI,QAAQ,IAAI,oBAAoB,aAAa,eAAQ;IACvD,OAAO,UAAU,cAAO;IACxB,OAAO,OAAO,OAAO,SAAS;IAC9B,OAAO,QAAQ,SAAS;AAC1B,OAAO;IACL,UAAU,OAAO,UAAU;IAC3B,QAAQ,SAAS,iBAAU;IAC3B,QAAQ,WAAW;IACnB,QAAQ,WAAW;IACnB,QAAQ,SAAS;IACjB,QAAQ,YAAY;IACpB,QAAQ,cAAc;IACtB,QAAQ,WAAW;IACnB,QAAQ,WAAW;AACrB;;;;ACfA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC;AAEA,iBAAiB;AACjB,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,+BAAS,gBAAgB;;mCAGrB;AAEJ,IAAI,wCAAkB,SAAS,gBAAgB,OAAO,EAAE,IAAI;IAC1D,OAAO,QAAQ,UAAU,MAAM;AACjC;;;;uCAUI;AAEJ,IAAI,sCAAgB,eAAO,cAAc,YAAa;AAEtD,SAAS,0CAAoB,KAAK;IAChC,OAAO,iCAAO,KAAK;AACrB;AAEA,SAAS,oCAAc,GAAG;IACxB,OAAO,iCAAO,SAAS,QAAQ,eAAe;AAChD;;AAMA,IAAI;AAEJ,IAAI,eAAa,YAAU,UACzB,8BAAQ,YAAU,SAAS;KAE3B,8BAAQ,SAAS,SAAS;;;;;;;AAS5B,IACI,yCAAmB;;;+CAEnB;AAAJ,IACI,6CAAuB,yCAAe,sBACtC,kDAA4B,yCAAe,2BAC3C,mDAA6B,yCAAe,4BAC5C,2DAAqC,yCAAe,oCAAoC,kDAAkD;AAG9I,IAAI;AACJ,IAAI;AACJ,IAAI;;AAEJ,yBAAoB,gCAAU;AAE9B,IAAI,uCAAiB;AACrB,IAAI,qCAAe;IAAC;IAAS;IAAS;IAAW;IAAS;CAAS;AAEnE,SAAS,sCAAgB,OAAO,EAAE,KAAK,EAAE,EAAE;IACzC,iEAAiE;IACjE,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,oBAAoB,YAAY,OAAO,QAAQ,gBAAgB,OAAO,KAAK,4EAA4E;IAC1K,2EAA2E;IAC3E,yEAAyE;IACzE,4EAA4E;IAE5E,IAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,QAAQ,GAAG,OAAO;SAAS,IAAI,MAAM,QAAQ,QAAQ,OAAO,CAAC,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ;SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG;QAAC;QAAI,QAAQ,OAAO,CAAC,MAAM;KAAC;AACtN;;;AAEA,SAAS,oCAAc,OAAO,EAAE,MAAM,EAAE,QAAQ;IAC9C,+BAAS,gCAAU;IACnB,UAAU,WAAW,CAAC,GAAG,2DAA2D;IACpF,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAE3E,IAAI,OAAO,aAAa,WAAW,WAAW,kBAAkB,8BAAQ,2DAA2D;IACnI,wDAAwD;IAExD,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ;IAC5B,IAAI,UAAU,IAAI,CAAC,aAAa,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,oBAAoB,iEAAiE;IAClJ,uEAAuE;IAEvE,IAAI,CAAC,gBAAgB,uCAAiB,IAAI,EAAE,SAAS,yBAAyB,WAAW,6EAA6E;IACtK,iEAAiE;IACjE,gBAAgB;IAEhB,IAAI,CAAC,SAAS,IAAI;IAClB,IAAI,CAAC,SAAS;IACd,IAAI,CAAC,QAAQ;IACb,IAAI,CAAC,aAAa;IAClB,IAAI,CAAC,UAAU;IACf,IAAI,CAAC,QAAQ;IACb,IAAI,CAAC,aAAa;IAClB,IAAI,CAAC,UAAU,OAAO,sEAAsE;IAC5F,0EAA0E;IAC1E,wEAAwE;IACxE,yCAAyC;IAEzC,IAAI,CAAC,OAAO,MAAM,qDAAqD;IACvE,mDAAmD;IAEnD,IAAI,CAAC,eAAe;IACpB,IAAI,CAAC,kBAAkB;IACvB,IAAI,CAAC,oBAAoB;IACzB,IAAI,CAAC,kBAAkB;IACvB,IAAI,CAAC,SAAS,MAAM,wDAAwD;IAE5E,IAAI,CAAC,YAAY,QAAQ,cAAc,OAAO,qEAAqE;IAEnH,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,aAAa,wBAAwB;IAElE,IAAI,CAAC,YAAY,OAAO,sEAAsE;IAC9F,6DAA6D;IAC7D,uDAAuD;IAEvD,IAAI,CAAC,kBAAkB,QAAQ,mBAAmB,QAAQ,oEAAoE;IAE9H,IAAI,CAAC,aAAa,GAAG,8CAA8C;IAEnE,IAAI,CAAC,cAAc;IACnB,IAAI,CAAC,UAAU;IACf,IAAI,CAAC,WAAW;IAEhB,IAAI,QAAQ,UAAU;QACpB,IAAI,CAAC,qCAAe,sCAAgB;QACpC,IAAI,CAAC,UAAU,IAAI,oCAAc,QAAQ;QACzC,IAAI,CAAC,WAAW,QAAQ;IAC1B;AACF;;AAEA,SAAS,+BAAS,OAAO;IACvB,+BAAS,gCAAU;IACnB,IAAI,CAAE,CAAA,IAAI,YAAY,8BAAO,GAAI,OAAO,IAAI,+BAAS,UAAU,yEAAyE;IACxI,sDAAsD;IAEtD,IAAI,WAAW,IAAI,YAAY;IAC/B,IAAI,CAAC,iBAAiB,IAAI,oCAAc,SAAS,IAAI,EAAE,WAAW,SAAS;IAE3E,IAAI,CAAC,WAAW;IAEhB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,SAAS,YAAY,IAAI,CAAC,QAAQ,QAAQ;QAC7D,IAAI,OAAO,QAAQ,YAAY,YAAY,IAAI,CAAC,WAAW,QAAQ;IACrE;IAEA,OAAO,KAAK,IAAI;AAClB;AAEA,OAAO,eAAe,+BAAS,WAAW,aAAa;IACrD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,IAAI,IAAI,CAAC,mBAAmB,WAC1B,OAAO;QAGT,OAAO,IAAI,CAAC,eAAe;IAC7B;IACA,KAAK,SAAS,IAAI,KAAK;QACrB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,gBACR;SACA,iDAAiD;QACnD,qBAAqB;QAGrB,IAAI,CAAC,eAAe,YAAY;IAClC;AACF;AACA,+BAAS,UAAU,UAAU;AAC7B,+BAAS,UAAU,aAAa;AAEhC,+BAAS,UAAU,WAAW,SAAU,GAAG,EAAE,EAAE;IAC7C,GAAG;AACL,GAAG,mDAAmD;AACtD,+DAA+D;AAC/D,6DAA6D;AAC7D,qBAAqB;AAGrB,+BAAS,UAAU,OAAO,SAAU,KAAK,EAAE,QAAQ;IACjD,IAAI,QAAQ,IAAI,CAAC;IACjB,IAAI;IAEJ,IAAI,CAAC,MAAM,YACT;QAAA,IAAI,OAAO,UAAU,UAAU;YAC7B,WAAW,YAAY,MAAM;YAE7B,IAAI,aAAa,MAAM,UAAU;gBAC/B,QAAQ,iCAAO,KAAK,OAAO;gBAC3B,WAAW;YACb;YAEA,iBAAiB;QACnB;IAAA,OAEA,iBAAiB;IAGnB,OAAO,uCAAiB,IAAI,EAAE,OAAO,UAAU,OAAO;AACxD,GAAG,8DAA8D;AAGjE,+BAAS,UAAU,UAAU,SAAU,KAAK;IAC1C,OAAO,uCAAiB,IAAI,EAAE,OAAO,MAAM,MAAM;AACnD;AAEA,SAAS,uCAAiB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc;IAC3E,4BAAM,oBAAoB;IAC1B,IAAI,QAAQ,OAAO;IAEnB,IAAI,UAAU,MAAM;QAClB,MAAM,UAAU;QAChB,iCAAW,QAAQ;IACrB,OAAO;QACL,IAAI;QACJ,IAAI,CAAC,gBAAgB,KAAK,mCAAa,OAAO;QAE9C,IAAI,IACF,qCAAe,QAAQ;aAClB,IAAI,MAAM,cAAc,SAAS,MAAM,SAAS,GAAG;YACxD,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,cAAc,OAAO,eAAe,WAAW,iCAAO,WAC5F,QAAQ,0CAAoB;YAG9B,IAAI;gBACF,IAAI,MAAM,YAAY,qCAAe,QAAQ,IAAI;qBAA2C,+BAAS,QAAQ,OAAO,OAAO;mBACtH,IAAI,MAAM,OACf,qCAAe,QAAQ,IAAI;iBACtB,IAAI,MAAM,WACf,OAAO;iBACF;gBACL,MAAM,UAAU;gBAEhB,IAAI,MAAM,WAAW,CAAC,UAAU;oBAC9B,QAAQ,MAAM,QAAQ,MAAM;oBAC5B,IAAI,MAAM,cAAc,MAAM,WAAW,GAAG,+BAAS,QAAQ,OAAO,OAAO;yBAAY,oCAAc,QAAQ;gBAC/G,OACE,+BAAS,QAAQ,OAAO,OAAO;YAEnC;QACF,OAAO,IAAI,CAAC,YAAY;YACtB,MAAM,UAAU;YAChB,oCAAc,QAAQ;QACxB;IACF,EAAE,2DAA2D;IAC7D,8DAA8D;IAC9D,8DAA8D;IAG9D,OAAO,CAAC,MAAM,SAAU,CAAA,MAAM,SAAS,MAAM,iBAAiB,MAAM,WAAW,CAAA;AACjF;AAEA,SAAS,+BAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;IAChD,IAAI,MAAM,WAAW,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM;QACtD,MAAM,aAAa;QACnB,OAAO,KAAK,QAAQ;IACtB,OAAO;QACL,0BAA0B;QAC1B,MAAM,UAAU,MAAM,aAAa,IAAI,MAAM;QAC7C,IAAI,YAAY,MAAM,OAAO,QAAQ;aAAY,MAAM,OAAO,KAAK;QACnE,IAAI,MAAM,cAAc,mCAAa;IACvC;IAEA,oCAAc,QAAQ;AACxB;AAEA,SAAS,mCAAa,KAAK,EAAE,KAAK;IAChC,IAAI;IAEJ,IAAI,CAAC,oCAAc,UAAU,OAAO,UAAU,YAAY,UAAU,aAAa,CAAC,MAAM,YACtF,KAAK,IAAI,2CAAqB,SAAS;QAAC;QAAU;QAAU;KAAa,EAAE;IAG7E,OAAO;AACT;AAEA,+BAAS,UAAU,WAAW;IAC5B,OAAO,IAAI,CAAC,eAAe,YAAY;AACzC,GAAG,2BAA2B;;AAG9B,+BAAS,UAAU,cAAc,SAAU,GAAG;IAC5C,IAAI,CAAC,qCAAe,sCAAgB;IACpC,IAAI,UAAU,IAAI,oCAAc;IAChC,IAAI,CAAC,eAAe,UAAU,SAAS,qDAAqD;IAE5F,IAAI,CAAC,eAAe,WAAW,IAAI,CAAC,eAAe,QAAQ,UAAU,iEAAiE;IAEtI,IAAI,IAAI,IAAI,CAAC,eAAe,OAAO;IACnC,IAAI,UAAU;IAEd,MAAO,MAAM,KAAM;QACjB,WAAW,QAAQ,MAAM,EAAE;QAC3B,IAAI,EAAE;IACR;IAEA,IAAI,CAAC,eAAe,OAAO;IAE3B,IAAI,YAAY,IAAI,IAAI,CAAC,eAAe,OAAO,KAAK;IACpD,IAAI,CAAC,eAAe,SAAS,QAAQ;IACrC,OAAO,IAAI;AACb,GAAG,4BAA4B;AAG/B,IAAI,gCAAU;AAEd,SAAS,8CAAwB,CAAC;IAChC,IAAI,KAAK,+BACP,6CAA6C;IAC7C,IAAI;SACC;QACL,2EAA2E;QAC3E,eAAe;QACf;QACA,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX;IACF;IAEA,OAAO;AACT,EAAE,6EAA6E;AAC/E,gCAAgC;AAGhC,SAAS,oCAAc,CAAC,EAAE,KAAK;IAC7B,IAAI,KAAK,KAAK,MAAM,WAAW,KAAK,MAAM,OAAO,OAAO;IACxD,IAAI,MAAM,YAAY,OAAO;IAE7B,IAAI,MAAM,GAAG;QACX,iCAAiC;QACjC,IAAI,MAAM,WAAW,MAAM,QAAQ,OAAO,MAAM,OAAO,KAAK,KAAK;aAAY,OAAO,MAAM;IAC5F,EAAE,qEAAqE;IAGvE,IAAI,IAAI,MAAM,eAAe,MAAM,gBAAgB,8CAAwB;IAC3E,IAAI,KAAK,MAAM,QAAQ,OAAO,GAAG,oBAAoB;IAErD,IAAI,CAAC,MAAM,OAAO;QAChB,MAAM,eAAe;QACrB,OAAO;IACT;IAEA,OAAO,MAAM;AACf,EAAE,oEAAoE;AAGtE,+BAAS,UAAU,OAAO,SAAU,CAAC;IACnC,4BAAM,QAAQ;IACd,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,IAAI,CAAC;IACjB,IAAI,QAAQ;IACZ,IAAI,MAAM,GAAG,MAAM,kBAAkB,OAAO,6DAA6D;IACzG,gEAAgE;IAChE,oCAAoC;IAEpC,IAAI,MAAM,KAAK,MAAM,gBAAiB,CAAA,AAAC,CAAA,MAAM,kBAAkB,IAAI,MAAM,UAAU,MAAM,gBAAgB,MAAM,SAAS,CAAA,KAAM,MAAM,KAAI,GAAI;QAC1I,4BAAM,sBAAsB,MAAM,QAAQ,MAAM;QAChD,IAAI,MAAM,WAAW,KAAK,MAAM,OAAO,kCAAY,IAAI;aAAO,mCAAa,IAAI;QAC/E,OAAO;IACT;IAEA,IAAI,oCAAc,GAAG,QAAQ,0DAA0D;IAEvF,IAAI,MAAM,KAAK,MAAM,OAAO;QAC1B,IAAI,MAAM,WAAW,GAAG,kCAAY,IAAI;QACxC,OAAO;IACT,EAAE,oDAAoD;IACtD,4DAA4D;IAC5D,6DAA6D;IAC7D,6DAA6D;IAC7D,2DAA2D;IAC3D,iCAAiC;IACjC,EAAE;IACF,qBAAqB;IACrB,6DAA6D;IAC7D,0BAA0B;IAC1B,EAAE;IACF,oEAAoE;IACpE,kEAAkE;IAClE,kEAAkE;IAClE,mEAAmE;IACnE,sCAAsC;IACtC,qEAAqE;IACrE,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,sEAAsE;IACtE,gEAAgE;IAGhE,IAAI,SAAS,MAAM;IACnB,4BAAM,iBAAiB,SAAS,wEAAwE;IAExG,IAAI,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI,MAAM,eAAe;QAChE,SAAS;QACT,4BAAM,8BAA8B;IACtC,EAAE,uEAAuE;IACzE,kCAAkC;IAGlC,IAAI,MAAM,SAAS,MAAM,SAAS;QAChC,SAAS;QACT,4BAAM,oBAAoB;IAC5B,OAAO,IAAI,QAAQ;QACjB,4BAAM;QACN,MAAM,UAAU;QAChB,MAAM,OAAO,MAAM,oEAAoE;QAEvF,IAAI,MAAM,WAAW,GAAG,MAAM,eAAe,MAAM,4BAA4B;QAE/E,IAAI,CAAC,MAAM,MAAM;QAEjB,MAAM,OAAO,OAAO,oEAAoE;QACxF,sEAAsE;QAEtE,IAAI,CAAC,MAAM,SAAS,IAAI,oCAAc,OAAO;IAC/C;IAEA,IAAI;IACJ,IAAI,IAAI,GAAG,MAAM,+BAAS,GAAG;SAAY,MAAM;IAE/C,IAAI,QAAQ,MAAM;QAChB,MAAM,eAAe,MAAM,UAAU,MAAM;QAC3C,IAAI;IACN,OAAO;QACL,MAAM,UAAU;QAChB,MAAM,aAAa;IACrB;IAEA,IAAI,MAAM,WAAW,GAAG;QACtB,yDAAyD;QACzD,oDAAoD;QACpD,IAAI,CAAC,MAAM,OAAO,MAAM,eAAe,MAAM,sEAAsE;QAEnH,IAAI,UAAU,KAAK,MAAM,OAAO,kCAAY,IAAI;IAClD;IAEA,IAAI,QAAQ,MAAM,IAAI,CAAC,KAAK,QAAQ;IACpC,OAAO;AACT;AAEA,SAAS,iCAAW,MAAM,EAAE,KAAK;IAC/B,4BAAM;IACN,IAAI,MAAM,OAAO;IAEjB,IAAI,MAAM,SAAS;QACjB,IAAI,QAAQ,MAAM,QAAQ;QAE1B,IAAI,SAAS,MAAM,QAAQ;YACzB,MAAM,OAAO,KAAK;YAClB,MAAM,UAAU,MAAM,aAAa,IAAI,MAAM;QAC/C;IACF;IAEA,MAAM,QAAQ;IAEd,IAAI,MAAM,MACR,yDAAyD;IACzD,gDAAgD;IAChD,kDAAkD;IAClD,mCAAa;SACR;QACL,sDAAsD;QACtD,MAAM,eAAe;QAErB,IAAI,CAAC,MAAM,iBAAiB;YAC1B,MAAM,kBAAkB;YACxB,oCAAc;QAChB;IACF;AACF,EAAE,wEAAwE;AAC1E,qEAAqE;AACrE,uDAAuD;AAGvD,SAAS,mCAAa,MAAM;IAC1B,IAAI,QAAQ,OAAO;IACnB,4BAAM,gBAAgB,MAAM,cAAc,MAAM;IAChD,MAAM,eAAe;IAErB,IAAI,CAAC,MAAM,iBAAiB;QAC1B,4BAAM,gBAAgB,MAAM;QAC5B,MAAM,kBAAkB;QACxB,QAAQ,SAAS,qCAAe;IAClC;AACF;AAEA,SAAS,oCAAc,MAAM;IAC3B,IAAI,QAAQ,OAAO;IACnB,4BAAM,iBAAiB,MAAM,WAAW,MAAM,QAAQ,MAAM;IAE5D,IAAI,CAAC,MAAM,aAAc,CAAA,MAAM,UAAU,MAAM,KAAI,GAAI;QACrD,OAAO,KAAK;QACZ,MAAM,kBAAkB;IAC1B,EAAE,6CAA6C;IAC/C,wDAAwD;IACxD,iBAAiB;IACjB,sBAAsB;IACtB,uDAAuD;IACvD,6BAA6B;IAG7B,MAAM,eAAe,CAAC,MAAM,WAAW,CAAC,MAAM,SAAS,MAAM,UAAU,MAAM;IAC7E,2BAAK;AACP,EAAE,oEAAoE;AACtE,mEAAmE;AACnE,iEAAiE;AACjE,oBAAoB;AACpB,iEAAiE;AACjE,wDAAwD;AAGxD,SAAS,oCAAc,MAAM,EAAE,KAAK;IAClC,IAAI,CAAC,MAAM,aAAa;QACtB,MAAM,cAAc;QACpB,QAAQ,SAAS,sCAAgB,QAAQ;IAC3C;AACF;AAEA,SAAS,qCAAe,MAAM,EAAE,KAAK;IACnC,0CAA0C;IAC1C,EAAE;IACF,qDAAqD;IACrD,4EAA4E;IAC5E,wEAAwE;IACxE,2EAA2E;IAC3E,0EAA0E;IAC1E,mDAAmD;IACnD,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,0EAA0E;IAC1E,+CAA+C;IAC/C,wCAAwC;IACxC,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,sEAAsE;IACtE,4EAA4E;IAC5E,sCAAsC;IACtC,MAAO,CAAC,MAAM,WAAW,CAAC,MAAM,SAAU,CAAA,MAAM,SAAS,MAAM,iBAAiB,MAAM,WAAW,MAAM,WAAW,CAAA,EAAI;QACpH,IAAI,MAAM,MAAM;QAChB,4BAAM;QACN,OAAO,KAAK;QACZ,IAAI,QAAQ,MAAM,QAChB;IACJ;IAEA,MAAM,cAAc;AACtB,EAAE,yEAAyE;AAC3E,kDAAkD;AAClD,qEAAqE;AACrE,8CAA8C;AAG9C,+BAAS,UAAU,QAAQ,SAAU,CAAC;IACpC,qCAAe,IAAI,EAAE,IAAI,iDAA2B;AACtD;AAEA,+BAAS,UAAU,OAAO,SAAU,IAAI,EAAE,QAAQ;IAChD,IAAI,MAAM,IAAI;IACd,IAAI,QAAQ,IAAI,CAAC;IAEjB,OAAQ,MAAM;QACZ,KAAK;YACH,MAAM,QAAQ;YACd;QAEF,KAAK;YACH,MAAM,QAAQ;gBAAC,MAAM;gBAAO;aAAK;YACjC;QAEF;YACE,MAAM,MAAM,KAAK;YACjB;IACJ;IAEA,MAAM,cAAc;IACpB,4BAAM,yBAAyB,MAAM,YAAY;IACjD,IAAI,QAAQ,AAAC,CAAA,CAAC,YAAY,SAAS,QAAQ,KAAI,KAAM,SAAS,QAAQ,UAAU,SAAS,QAAQ;IACjG,IAAI,QAAQ,QAAQ,QAAQ;IAC5B,IAAI,MAAM,YAAY,QAAQ,SAAS;SAAY,IAAI,KAAK,OAAO;IACnE,KAAK,GAAG,UAAU;IAElB,SAAS,SAAS,QAAQ,EAAE,UAAU;QACpC,4BAAM;QAEN,IAAI,aAAa,KACf;YAAA,IAAI,cAAc,WAAW,eAAe,OAAO;gBACjD,WAAW,aAAa;gBACxB;YACF;QAAA;IAEJ;IAEA,SAAS;QACP,4BAAM;QACN,KAAK;IACP,EAAE,0DAA0D;IAC5D,4DAA4D;IAC5D,2DAA2D;IAC3D,YAAY;IAGZ,IAAI,UAAU,kCAAY;IAC1B,KAAK,GAAG,SAAS;IACjB,IAAI,YAAY;IAEhB,SAAS;QACP,4BAAM,YAAY,iDAAiD;QAEnE,KAAK,eAAe,SAAS;QAC7B,KAAK,eAAe,UAAU;QAC9B,KAAK,eAAe,SAAS;QAC7B,KAAK,eAAe,SAAS;QAC7B,KAAK,eAAe,UAAU;QAC9B,IAAI,eAAe,OAAO;QAC1B,IAAI,eAAe,OAAO;QAC1B,IAAI,eAAe,QAAQ;QAC3B,YAAY,MAAM,uDAAuD;QACzE,yDAAyD;QACzD,iBAAiB;QACjB,6DAA6D;QAC7D,6DAA6D;QAE7D,IAAI,MAAM,cAAe,CAAA,CAAC,KAAK,kBAAkB,KAAK,eAAe,SAAQ,GAAI;IACnF;IAEA,IAAI,GAAG,QAAQ;IAEf,SAAS,OAAO,KAAK;QACnB,4BAAM;QACN,IAAI,MAAM,KAAK,MAAM;QACrB,4BAAM,cAAc;QAEpB,IAAI,QAAQ,OAAO;YACjB,4DAA4D;YAC5D,2DAA2D;YAC3D,uBAAuB;YACvB,yDAAyD;YACzD,IAAI,AAAC,CAAA,MAAM,eAAe,KAAK,MAAM,UAAU,QAAQ,MAAM,aAAa,KAAK,8BAAQ,MAAM,OAAO,UAAU,EAAC,KAAM,CAAC,WAAW;gBAC/H,4BAAM,+BAA+B,MAAM;gBAC3C,MAAM;YACR;YAEA,IAAI;QACN;IACF,EAAE,sDAAsD;IACxD,0DAA0D;IAG1D,SAAS,QAAQ,EAAE;QACjB,4BAAM,WAAW;QACjB;QACA,KAAK,eAAe,SAAS;QAC7B,IAAI,sCAAgB,MAAM,aAAa,GAAG,qCAAe,MAAM;IACjE,EAAE,gEAAgE;IAGlE,sCAAgB,MAAM,SAAS,UAAU,8DAA8D;IAEvG,SAAS;QACP,KAAK,eAAe,UAAU;QAC9B;IACF;IAEA,KAAK,KAAK,SAAS;IAEnB,SAAS;QACP,4BAAM;QACN,KAAK,eAAe,SAAS;QAC7B;IACF;IAEA,KAAK,KAAK,UAAU;IAEpB,SAAS;QACP,4BAAM;QACN,IAAI,OAAO;IACb,EAAE,yCAAyC;IAG3C,KAAK,KAAK,QAAQ,MAAM,oDAAoD;IAE5E,IAAI,CAAC,MAAM,SAAS;QAClB,4BAAM;QACN,IAAI;IACN;IAEA,OAAO;AACT;AAEA,SAAS,kCAAY,GAAG;IACtB,OAAO,SAAS;QACd,IAAI,QAAQ,IAAI;QAChB,4BAAM,eAAe,MAAM;QAC3B,IAAI,MAAM,YAAY,MAAM;QAE5B,IAAI,MAAM,eAAe,KAAK,sCAAgB,KAAK,SAAS;YAC1D,MAAM,UAAU;YAChB,2BAAK;QACP;IACF;AACF;AAEA,+BAAS,UAAU,SAAS,SAAU,IAAI;IACxC,IAAI,QAAQ,IAAI,CAAC;IACjB,IAAI,aAAa;QACf,YAAY;IACd,GAAG,iDAAiD;IAEpD,IAAI,MAAM,eAAe,GAAG,OAAO,IAAI,EAAE,2CAA2C;IAEpF,IAAI,MAAM,eAAe,GAAG;QAC1B,6CAA6C;QAC7C,IAAI,QAAQ,SAAS,MAAM,OAAO,OAAO,IAAI;QAC7C,IAAI,CAAC,MAAM,OAAO,MAAM,OAAO,eAAe;QAE9C,MAAM,QAAQ;QACd,MAAM,aAAa;QACnB,MAAM,UAAU;QAChB,IAAI,MAAM,KAAK,KAAK,UAAU,IAAI,EAAE;QACpC,OAAO,IAAI;IACb,EAAE,yCAAyC;IAG3C,IAAI,CAAC,MAAM;QACT,cAAc;QACd,IAAI,QAAQ,MAAM;QAClB,IAAI,MAAM,MAAM;QAChB,MAAM,QAAQ;QACd,MAAM,aAAa;QACnB,MAAM,UAAU;QAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IACvB,KAAK,CAAC,EAAE,CAAC,KAAK,UAAU,IAAI,EAAE;YAC5B,YAAY;QACd;QAGF,OAAO,IAAI;IACb,EAAE,6BAA6B;IAG/B,IAAI,QAAQ,8BAAQ,MAAM,OAAO;IACjC,IAAI,UAAU,IAAI,OAAO,IAAI;IAC7B,MAAM,MAAM,OAAO,OAAO;IAC1B,MAAM,cAAc;IACpB,IAAI,MAAM,eAAe,GAAG,MAAM,QAAQ,MAAM,KAAK,CAAC,EAAE;IACxD,KAAK,KAAK,UAAU,IAAI,EAAE;IAC1B,OAAO,IAAI;AACb,GAAG,2CAA2C;AAC9C,qDAAqD;AAGrD,+BAAS,UAAU,KAAK,SAAU,EAAE,EAAE,EAAE;IACtC,IAAI,MAAM,OAAO,UAAU,GAAG,KAAK,IAAI,EAAE,IAAI;IAC7C,IAAI,QAAQ,IAAI,CAAC;IAEjB,IAAI,OAAO,QAAQ;QACjB,2DAA2D;QAC3D,gEAAgE;QAChE,MAAM,oBAAoB,IAAI,CAAC,cAAc,cAAc,GAAG,mEAAmE;QAEjI,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;IACpC,OAAO,IAAI,OAAO,YAChB;QAAA,IAAI,CAAC,MAAM,cAAc,CAAC,MAAM,mBAAmB;YACjD,MAAM,oBAAoB,MAAM,eAAe;YAC/C,MAAM,UAAU;YAChB,MAAM,kBAAkB;YACxB,4BAAM,eAAe,MAAM,QAAQ,MAAM;YAEzC,IAAI,MAAM,QACR,mCAAa,IAAI;iBACZ,IAAI,CAAC,MAAM,SAChB,QAAQ,SAAS,wCAAkB,IAAI;QAE3C;IAAA;IAGF,OAAO;AACT;AAEA,+BAAS,UAAU,cAAc,+BAAS,UAAU;AAEpD,+BAAS,UAAU,iBAAiB,SAAU,EAAE,EAAE,EAAE;IAClD,IAAI,MAAM,OAAO,UAAU,eAAe,KAAK,IAAI,EAAE,IAAI;IAEzD,IAAI,OAAO,YACT,0DAA0D;IAC1D,6DAA6D;IAC7D,+DAA+D;IAC/D,+DAA+D;IAC/D,2CAA2C;IAC3C,UAAU;IACV,QAAQ,SAAS,+CAAyB,IAAI;IAGhD,OAAO;AACT;AAEA,+BAAS,UAAU,qBAAqB,SAAU,EAAE;IAClD,IAAI,MAAM,OAAO,UAAU,mBAAmB,MAAM,IAAI,EAAE;IAE1D,IAAI,OAAO,cAAc,OAAO,WAC9B,0DAA0D;IAC1D,6DAA6D;IAC7D,+DAA+D;IAC/D,+DAA+D;IAC/D,2CAA2C;IAC3C,UAAU;IACV,QAAQ,SAAS,+CAAyB,IAAI;IAGhD,OAAO;AACT;AAEA,SAAS,8CAAwB,IAAI;IACnC,IAAI,QAAQ,KAAK;IACjB,MAAM,oBAAoB,KAAK,cAAc,cAAc;IAE3D,IAAI,MAAM,mBAAmB,CAAC,MAAM,QAClC,iDAAiD;IACjD,qCAAqC;IACrC,MAAM,UAAU,MAAM,yCAAyC;SAC1D,IAAI,KAAK,cAAc,UAAU,GACtC,KAAK;AAET;AAEA,SAAS,uCAAiB,IAAI;IAC5B,4BAAM;IACN,KAAK,KAAK;AACZ,EAAE,sEAAsE;AACxE,oDAAoD;AAGpD,+BAAS,UAAU,SAAS;IAC1B,IAAI,QAAQ,IAAI,CAAC;IAEjB,IAAI,CAAC,MAAM,SAAS;QAClB,4BAAM,WAAW,4CAA4C;QAC7D,0CAA0C;QAC1C,WAAW;QAEX,MAAM,UAAU,CAAC,MAAM;QACvB,6BAAO,IAAI,EAAE;IACf;IAEA,MAAM,SAAS;IACf,OAAO,IAAI;AACb;AAEA,SAAS,6BAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,CAAC,MAAM,iBAAiB;QAC1B,MAAM,kBAAkB;QACxB,QAAQ,SAAS,+BAAS,QAAQ;IACpC;AACF;AAEA,SAAS,8BAAQ,MAAM,EAAE,KAAK;IAC5B,4BAAM,UAAU,MAAM;IAEtB,IAAI,CAAC,MAAM,SACT,OAAO,KAAK;IAGd,MAAM,kBAAkB;IACxB,OAAO,KAAK;IACZ,2BAAK;IACL,IAAI,MAAM,WAAW,CAAC,MAAM,SAAS,OAAO,KAAK;AACnD;AAEA,+BAAS,UAAU,QAAQ;IACzB,4BAAM,yBAAyB,IAAI,CAAC,eAAe;IAEnD,IAAI,IAAI,CAAC,eAAe,YAAY,OAAO;QACzC,4BAAM;QACN,IAAI,CAAC,eAAe,UAAU;QAC9B,IAAI,CAAC,KAAK;IACZ;IAEA,IAAI,CAAC,eAAe,SAAS;IAC7B,OAAO,IAAI;AACb;AAEA,SAAS,2BAAK,MAAM;IAClB,IAAI,QAAQ,OAAO;IACnB,4BAAM,QAAQ,MAAM;IAEpB,MAAO,MAAM,WAAW,OAAO,WAAW;AAG5C,EAAE,qDAAqD;AACvD,uDAAuD;AACvD,6CAA6C;AAG7C,+BAAS,UAAU,OAAO,SAAU,MAAM;IACxC,IAAI,QAAQ,IAAI;IAEhB,IAAI,QAAQ,IAAI,CAAC;IACjB,IAAI,SAAS;IACb,OAAO,GAAG,OAAO;QACf,4BAAM;QAEN,IAAI,MAAM,WAAW,CAAC,MAAM,OAAO;YACjC,IAAI,QAAQ,MAAM,QAAQ;YAC1B,IAAI,SAAS,MAAM,QAAQ,MAAM,KAAK;QACxC;QAEA,MAAM,KAAK;IACb;IACA,OAAO,GAAG,QAAQ,SAAU,KAAK;QAC/B,4BAAM;QACN,IAAI,MAAM,SAAS,QAAQ,MAAM,QAAQ,MAAM,QAAQ,6CAA6C;QAEpG,IAAI,MAAM,cAAe,CAAA,UAAU,QAAQ,UAAU,SAAQ,GAAI;aAAY,IAAI,CAAC,MAAM,cAAe,CAAA,CAAC,SAAS,CAAC,MAAM,MAAK,GAAI;QAEjI,IAAI,MAAM,MAAM,KAAK;QAErB,IAAI,CAAC,KAAK;YACR,SAAS;YACT,OAAO;QACT;IACF,IAAI,+BAA+B;IACnC,gDAAgD;IAEhD,IAAK,IAAI,KAAK,OACZ,IAAI,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO,MAAM,CAAC,EAAE,KAAK,YAChD,IAAI,CAAC,EAAE,GAAG,SAAS,WAAW,MAAM;QAClC,OAAO,SAAS;YACd,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,QAAQ;QACtC;IACF,EAAE;KAEJ,kCAAkC;IAGpC,IAAK,IAAI,IAAI,GAAG,IAAI,mCAAa,QAAQ,IACvC,OAAO,GAAG,kCAAY,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,kCAAY,CAAC,EAAE;KAC/D,6DAA6D;IAC/D,qBAAqB;IAGrB,IAAI,CAAC,QAAQ,SAAU,CAAC;QACtB,4BAAM,iBAAiB;QAEvB,IAAI,QAAQ;YACV,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO,IAAI;AACb;;AAEA,IAAI,OAAO,WAAW,YACpB,+BAAS,SAAS,CAAC,OAAO,cAAc,GAAG;IACzC,IAAI,4DAAsC,WACxC,0DAAoC;IAGtC,OAAO,wDAAkC,IAAI;AAC/C;AAGF,OAAO,eAAe,+BAAS,WAAW,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF;AACA,OAAO,eAAe,+BAAS,WAAW,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,eAAe;IACpD;AACF;AACA,OAAO,eAAe,+BAAS,WAAW,mBAAmB;IAC3D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;IACA,KAAK,SAAS,IAAI,KAAK;QACrB,IAAI,IAAI,CAAC,gBACP,IAAI,CAAC,eAAe,UAAU;IAElC;AACF,IAAI,qCAAqC;AAEzC,+BAAS,YAAY;AACrB,OAAO,eAAe,+BAAS,WAAW,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF,IAAI,8CAA8C;AAClD,iEAAiE;AACjE,6EAA6E;AAC7E,gCAAgC;AAEhC,SAAS,+BAAS,CAAC,EAAE,KAAK;IACxB,mBAAmB;IACnB,IAAI,MAAM,WAAW,GAAG,OAAO;IAC/B,IAAI;IACJ,IAAI,MAAM,YAAY,MAAM,MAAM,OAAO;SAAa,IAAI,CAAC,KAAK,KAAK,MAAM,QAAQ;QACjF,iCAAiC;QACjC,IAAI,MAAM,SAAS,MAAM,MAAM,OAAO,KAAK;aAAS,IAAI,MAAM,OAAO,WAAW,GAAG,MAAM,MAAM,OAAO;aAAa,MAAM,MAAM,OAAO,OAAO,MAAM;QACnJ,MAAM,OAAO;IACf,OACE,oBAAoB;IACpB,MAAM,MAAM,OAAO,QAAQ,GAAG,MAAM;IAEtC,OAAO;AACT;AAEA,SAAS,kCAAY,MAAM;IACzB,IAAI,QAAQ,OAAO;IACnB,4BAAM,eAAe,MAAM;IAE3B,IAAI,CAAC,MAAM,YAAY;QACrB,MAAM,QAAQ;QACd,QAAQ,SAAS,qCAAe,OAAO;IACzC;AACF;AAEA,SAAS,oCAAc,KAAK,EAAE,MAAM;IAClC,4BAAM,iBAAiB,MAAM,YAAY,MAAM,SAAS,6CAA6C;IAErG,IAAI,CAAC,MAAM,cAAc,MAAM,WAAW,GAAG;QAC3C,MAAM,aAAa;QACnB,OAAO,WAAW;QAClB,OAAO,KAAK;QAEZ,IAAI,MAAM,aAAa;YACrB,oDAAoD;YACpD,wDAAwD;YACxD,IAAI,SAAS,OAAO;YAEpB,IAAI,CAAC,UAAU,OAAO,eAAe,OAAO,UAC1C,OAAO;QAEX;IACF;AACF;;AAEA,IAAI,OAAO,WAAW,YACpB,+BAAS,OAAO,SAAU,QAAQ,EAAE,IAAI;IACtC,IAAI,+BAAS,WACX,6BAAO;IAGT,OAAO,2BAAK,gCAAU,UAAU;AAClC;AAGF,SAAS,8BAAQ,EAAE,EAAE,CAAC;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,IAAK;QACzC,IAAI,EAAE,CAAC,EAAE,KAAK,GAAG,OAAO;IAC1B;IAEA,OAAO;AACT;;;;;ACnmCA,iBAAiB;;;;;ACAjB;AAEA,SAAS,8BAAQ,MAAM,EAAE,cAAc;IAAI,IAAI,OAAO,OAAO,KAAK;IAAS,IAAI,OAAO,uBAAuB;QAAE,IAAI,UAAU,OAAO,sBAAsB;QAAS,IAAI,gBAAgB,UAAU,QAAQ,OAAO,SAAU,GAAG;YAAI,OAAO,OAAO,yBAAyB,QAAQ,KAAK;QAAY;QAAI,KAAK,KAAK,MAAM,MAAM;IAAU;IAAE,OAAO;AAAM;AAEpV,SAAS,oCAAc,MAAM;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK;QAAE,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,GAAK,8BAAQ,OAAO,SAAS,MAAM,QAAQ,SAAU,GAAG;YAAI,sCAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;QAAG;aAAW,IAAI,OAAO,2BAA6B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B;aAAmB,8BAAQ,OAAO,SAAS,QAAQ,SAAU,GAAG;YAAI,OAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ;QAAO;IAAM;IAAE,OAAO;AAAQ;AAErhB,SAAS,sCAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,IAAI,OAAO,KAAO,OAAO,eAAe,KAAK,KAAK;QAAE,OAAO;QAAO,YAAY;QAAM,cAAc;QAAM,UAAU;IAAK;SAAa,GAAG,CAAC,IAAI,GAAG;IAAS,OAAO;AAAK;AAEhN,SAAS,sCAAgB,QAAQ,EAAE,WAAW;IAAI,IAAI,CAAE,CAAA,oBAAoB,WAAU,GAAM,MAAM,IAAI,UAAU;AAAwC;AAExJ,SAAS,wCAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,aAAa,WAAW,cAAc;QAAO,WAAW,eAAe;QAAM,IAAI,WAAW,YAAY,WAAW,WAAW;QAAM,OAAO,eAAe,QAAQ,WAAW,KAAK;IAAa;AAAE;AAE5T,SAAS,mCAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,wCAAkB,YAAY,WAAW;IAAa,IAAI,aAAa,wCAAkB,aAAa;IAAc,OAAO;AAAa;;AAEtN,IACI,+BAAS;;AAEb,IACI,gCAAU;AAEd,IAAI,+BAAS,iCAAW,8BAAQ,UAAU;AAE1C,SAAS,iCAAW,GAAG,EAAE,MAAM,EAAE,MAAM;IACrC,6BAAO,UAAU,KAAK,KAAK,KAAK,QAAQ;AAC1C;AAEA,iBACA,WAAW,GACX;IACE,SAAS;QACP,sCAAgB,IAAI,EAAE;QAEtB,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,SAAS;IAChB;IAEA,mCAAa,YAAY;QAAC;YACxB,KAAK;YACL,OAAO,SAAS,KAAK,CAAC;gBACpB,IAAI,QAAQ;oBACV,MAAM;oBACN,MAAM;gBACR;gBACA,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,OAAO;qBAAW,IAAI,CAAC,OAAO;gBAC7D,IAAI,CAAC,OAAO;gBACZ,EAAE,IAAI,CAAC;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ,CAAC;gBACvB,IAAI,QAAQ;oBACV,MAAM;oBACN,MAAM,IAAI,CAAC;gBACb;gBACA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO;gBACnC,IAAI,CAAC,OAAO;gBACZ,EAAE,IAAI,CAAC;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,IAAI,IAAI,CAAC,WAAW,GAAG;gBACvB,IAAI,MAAM,IAAI,CAAC,KAAK;gBACpB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO;qBAAU,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;gBAC/E,EAAE,IAAI,CAAC;gBACP,OAAO;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO;gBACxB,IAAI,CAAC,SAAS;YAChB;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,KAAK,CAAC;gBACpB,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO;gBAC9B,IAAI,IAAI,IAAI,CAAC;gBACb,IAAI,MAAM,KAAK,EAAE;gBAEjB,MAAO,IAAI,EAAE,KACX,OAAO,IAAI,EAAE;gBAGf,OAAO;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,OAAO,CAAC;gBACtB,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,6BAAO,MAAM;gBAC3C,IAAI,MAAM,6BAAO,YAAY,MAAM;gBACnC,IAAI,IAAI,IAAI,CAAC;gBACb,IAAI,IAAI;gBAER,MAAO,EAAG;oBACR,iCAAW,EAAE,MAAM,KAAK;oBACxB,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE;gBACR;gBAEA,OAAO;YACT,EAAE,6EAA6E;QAEjF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ,CAAC,EAAE,UAAU;gBACnC,IAAI;gBAEJ,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ;oBAC7B,+CAA+C;oBAC/C,MAAM,IAAI,CAAC,KAAK,KAAK,MAAM,GAAG;oBAC9B,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM;gBACxC,OAAO,IAAI,MAAM,IAAI,CAAC,KAAK,KAAK,QAC9B,kCAAkC;gBAClC,MAAM,IAAI,CAAC;qBAEX,qCAAqC;gBACrC,MAAM,aAAa,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW;gBAG1D,OAAO;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,KAAK;YACnB,EAAE,oEAAoE;QAExE;QAAG;YACD,KAAK;YACL,OAAO,SAAS,WAAW,CAAC;gBAC1B,IAAI,IAAI,IAAI,CAAC;gBACb,IAAI,IAAI;gBACR,IAAI,MAAM,EAAE;gBACZ,KAAK,IAAI;gBAET,MAAO,IAAI,EAAE,KAAM;oBACjB,IAAI,MAAM,EAAE;oBACZ,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI,SAAS;oBACvC,IAAI,OAAO,IAAI,QAAQ,OAAO;yBAAS,OAAO,IAAI,MAAM,GAAG;oBAC3D,KAAK;oBAEL,IAAI,MAAM,GAAG;wBACX,IAAI,OAAO,IAAI,QAAQ;4BACrB,EAAE;4BACF,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;iCAAU,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO;wBAC9D,OAAO;4BACL,IAAI,CAAC,OAAO;4BACZ,EAAE,OAAO,IAAI,MAAM;wBACrB;wBAEA;oBACF;oBAEA,EAAE;gBACJ;gBAEA,IAAI,CAAC,UAAU;gBACf,OAAO;YACT,EAAE,+DAA+D;QAEnE;QAAG;YACD,KAAK;YACL,OAAO,SAAS,WAAW,CAAC;gBAC1B,IAAI,MAAM,6BAAO,YAAY;gBAC7B,IAAI,IAAI,IAAI,CAAC;gBACb,IAAI,IAAI;gBACR,EAAE,KAAK,KAAK;gBACZ,KAAK,EAAE,KAAK;gBAEZ,MAAO,IAAI,EAAE,KAAM;oBACjB,IAAI,MAAM,EAAE;oBACZ,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI,SAAS;oBACvC,IAAI,KAAK,KAAK,IAAI,SAAS,GAAG,GAAG;oBACjC,KAAK;oBAEL,IAAI,MAAM,GAAG;wBACX,IAAI,OAAO,IAAI,QAAQ;4BACrB,EAAE;4BACF,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;iCAAU,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO;wBAC9D,OAAO;4BACL,IAAI,CAAC,OAAO;4BACZ,EAAE,OAAO,IAAI,MAAM;wBACrB;wBAEA;oBACF;oBAEA,EAAE;gBACJ;gBAEA,IAAI,CAAC,UAAU;gBACf,OAAO;YACT,EAAE,0EAA0E;QAE9E;QAAG;YACD,KAAK;YACL,OAAO,SAAS,MAAM,CAAC,EAAE,OAAO;gBAC9B,OAAO,8BAAQ,IAAI,EAAE,oCAAc,CAAC,GAAG,SAAS;oBAC9C,0BAA0B;oBAC1B,OAAO;oBACP,yBAAyB;oBACzB,eAAe;gBACjB;YACF;QACF;KAAE;IAEF,OAAO;AACT;;;;;ACjNA,cAAc,6DAA6D;AAE3E,SAAS,8BAAQ,GAAG,EAAE,EAAE;IACtB,IAAI,QAAQ,IAAI;IAEhB,IAAI,oBAAoB,IAAI,CAAC,kBAAkB,IAAI,CAAC,eAAe;IACnE,IAAI,oBAAoB,IAAI,CAAC,kBAAkB,IAAI,CAAC,eAAe;IAEnE,IAAI,qBAAqB,mBAAmB;QAC1C,IAAI,IACF,GAAG;aACE,IAAI,KAAK;YACd,IAAI,CAAC,IAAI,CAAC,gBACR,QAAQ,SAAS,mCAAa,IAAI,EAAE;iBAC/B,IAAI,CAAC,IAAI,CAAC,eAAe,cAAc;gBAC5C,IAAI,CAAC,eAAe,eAAe;gBACnC,QAAQ,SAAS,mCAAa,IAAI,EAAE;YACtC;QACF;QAEA,OAAO,IAAI;IACb,EAAE,kEAAkE;IACpE,2EAA2E;IAG3E,IAAI,IAAI,CAAC,gBACP,IAAI,CAAC,eAAe,YAAY;KAChC,yEAAyE;IAG3E,IAAI,IAAI,CAAC,gBACP,IAAI,CAAC,eAAe,YAAY;IAGlC,IAAI,CAAC,SAAS,OAAO,MAAM,SAAU,GAAG;QACtC,IAAI,CAAC,MAAM,KAAK;YACd,IAAI,CAAC,MAAM,gBACT,QAAQ,SAAS,2CAAqB,OAAO;iBACxC,IAAI,CAAC,MAAM,eAAe,cAAc;gBAC7C,MAAM,eAAe,eAAe;gBACpC,QAAQ,SAAS,2CAAqB,OAAO;YAC/C,OACE,QAAQ,SAAS,mCAAa;QAElC,OAAO,IAAI,IAAI;YACb,QAAQ,SAAS,mCAAa;YAC9B,GAAG;QACL,OACE,QAAQ,SAAS,mCAAa;IAElC;IAEA,OAAO,IAAI;AACb;AAEA,SAAS,0CAAoB,IAAI,EAAE,GAAG;IACpC,kCAAY,MAAM;IAClB,kCAAY;AACd;AAEA,SAAS,kCAAY,IAAI;IACvB,IAAI,KAAK,kBAAkB,CAAC,KAAK,eAAe,WAAW;IAC3D,IAAI,KAAK,kBAAkB,CAAC,KAAK,eAAe,WAAW;IAC3D,KAAK,KAAK;AACZ;AAEA,SAAS;IACP,IAAI,IAAI,CAAC,gBAAgB;QACvB,IAAI,CAAC,eAAe,YAAY;QAChC,IAAI,CAAC,eAAe,UAAU;QAC9B,IAAI,CAAC,eAAe,QAAQ;QAC5B,IAAI,CAAC,eAAe,aAAa;IACnC;IAEA,IAAI,IAAI,CAAC,gBAAgB;QACvB,IAAI,CAAC,eAAe,YAAY;QAChC,IAAI,CAAC,eAAe,QAAQ;QAC5B,IAAI,CAAC,eAAe,SAAS;QAC7B,IAAI,CAAC,eAAe,cAAc;QAClC,IAAI,CAAC,eAAe,cAAc;QAClC,IAAI,CAAC,eAAe,WAAW;QAC/B,IAAI,CAAC,eAAe,eAAe;IACrC;AACF;AAEA,SAAS,kCAAY,IAAI,EAAE,GAAG;IAC5B,KAAK,KAAK,SAAS;AACrB;AAEA,SAAS,qCAAe,MAAM,EAAE,GAAG;IACjC,kDAAkD;IAClD,sDAAsD;IACtD,kDAAkD;IAClD,gDAAgD;IAChD,4DAA4D;IAC5D,IAAI,SAAS,OAAO;IACpB,IAAI,SAAS,OAAO;IACpB,IAAI,UAAU,OAAO,eAAe,UAAU,OAAO,aAAa,OAAO,QAAQ;SAAU,OAAO,KAAK,SAAS;AAClH;AAEA,iBAAiB;IACf,SAAS;IACT,WAAW;IACX,gBAAgB;AAClB;;;;;ACxGA;;;AAEA,IAAI,8CAAwB,aAAiC;AAE7D,SAAS,wCAAkB,OAAO,EAAE,QAAQ,EAAE,SAAS;IACrD,OAAO,QAAQ,iBAAiB,OAAO,QAAQ,gBAAgB,WAAW,OAAO,CAAC,UAAU,GAAG;AACjG;AAEA,SAAS,uCAAiB,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;IAC3D,IAAI,MAAM,wCAAkB,SAAS,UAAU;IAE/C,IAAI,OAAO,MAAM;QACf,IAAI,CAAE,CAAA,SAAS,QAAQ,KAAK,MAAM,SAAS,GAAE,KAAM,MAAM,GAAG;YAC1D,IAAI,OAAO,WAAW,YAAY;YAClC,MAAM,IAAI,4CAAsB,MAAM;QACxC;QAEA,OAAO,KAAK,MAAM;IACpB,EAAE,gBAAgB;IAGlB,OAAO,MAAM,aAAa,KAAK;AACjC;AAEA,iBAAiB;IACf,kBAAkB;AACpB;;;;;;ACyFA,IAAA;AAnHA;AAEA,MAAM,8BAAQ,CAAC;AAEf,SAAS,sCAAgB,IAAI,EAAE,OAAO,EAAE,IAAI;IAC1C,IAAI,CAAC,MACH,OAAO;IAGT,SAAS,WAAY,IAAI,EAAE,IAAI,EAAE,IAAI;QACnC,IAAI,OAAO,YAAY,UACrB,OAAO;aAEP,OAAO,QAAQ,MAAM,MAAM;IAE/B;IAEA,MAAM,kBAAkB;QACtB,YAAa,IAAI,EAAE,IAAI,EAAE,IAAI,CAAE;YAC7B,KAAK,CAAC,WAAW,MAAM,MAAM;QAC/B;IACF;IAEA,UAAU,UAAU,OAAO,KAAK;IAChC,UAAU,UAAU,OAAO;IAE3B,2BAAK,CAAC,KAAK,GAAG;AAChB;AAEA,qEAAqE;AACrE,SAAS,4BAAM,QAAQ,EAAE,KAAK;IAC5B,IAAI,MAAM,QAAQ,WAAW;QAC3B,MAAM,MAAM,SAAS;QACrB,WAAW,SAAS,IAAI,CAAC,IAAM,OAAO;QACtC,IAAI,MAAM,GACR,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,SAAS,MAAM,GAAG,MAAM,GAAG,KAAK,MAAM,KAAK,CAAC,GAC/D,QAAQ,CAAC,MAAM,EAAE;aACnB,IAAI,QAAQ,GACjB,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;aAEzD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;IAEvC,OACE,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,UAAU,CAAC;AAE5C;AAEA,qGAAqG;AACrG,SAAS,iCAAW,GAAG,EAAE,MAAM,EAAE,GAAG;IACnC,OAAO,IAAI,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,OAAO,YAAY;AAClE;AAEA,mGAAmG;AACnG,SAAS,+BAAS,GAAG,EAAE,MAAM,EAAE,QAAQ;IACtC,IAAI,aAAa,aAAa,WAAW,IAAI,QAC5C,WAAW,IAAI;IAEhB,OAAO,IAAI,UAAU,WAAW,OAAO,QAAQ,cAAc;AAC9D;AAEA,mGAAmG;AACnG,SAAS,+BAAS,GAAG,EAAE,MAAM,EAAE,KAAK;IAClC,IAAI,OAAO,UAAU,UACnB,QAAQ;IAGV,IAAI,QAAQ,OAAO,SAAS,IAAI,QAC9B,OAAO;SAEP,OAAO,IAAI,QAAQ,QAAQ,WAAW;AAE1C;AAEA,sCAAgB,yBAAyB,SAAU,IAAI,EAAE,KAAK;IAC5D,OAAO,gBAAgB,QAAQ,8BAA8B,OAAO;AACtE,GAAG;AACH,sCAAgB,wBAAwB,SAAU,IAAI,EAAE,QAAQ,EAAE,MAAM;IACtE,yCAAyC;IACzC,IAAI;IACJ,IAAI,OAAO,aAAa,YAAY,iCAAW,UAAU,SAAS;QAChE,aAAa;QACb,WAAW,SAAS,QAAQ,SAAS;IACvC,OACE,aAAa;IAGf,IAAI;IACJ,IAAI,+BAAS,MAAM,cACjB,kCAAkC;IAClC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,4BAAM,UAAU,QAAQ,CAAC;SACvD;QACL,MAAM,OAAO,+BAAS,MAAM,OAAO,aAAa;QAChD,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,4BAAM,UAAU,QAAQ,CAAC;IACxE;IAEA,OAAO,CAAC,gBAAgB,EAAE,OAAO,OAAO,CAAC;IACzC,OAAO;AACT,GAAG;AACH,sCAAgB,6BAA6B;AAC7C,sCAAgB,8BAA8B,SAAU,IAAI;IAC1D,OAAO,SAAS,OAAO;AACzB;AACA,sCAAgB,8BAA8B;AAC9C,sCAAgB,wBAAwB,SAAU,IAAI;IACpD,OAAO,iBAAiB,OAAO;AACjC;AACA,sCAAgB,yBAAyB;AACzC,sCAAgB,0BAA0B;AAC1C,sCAAgB,8BAA8B;AAC9C,sCAAgB,0BAA0B,uCAAuC;AACjF,sCAAgB,wBAAwB,SAAU,GAAG;IACnD,OAAO,uBAAuB;AAChC,GAAG;AACH,sCAAgB,sCAAsC;AAEtD,4CAAuB;;;;;;;;ACnHvB,IAAI;IACF,IAAI,6BAAO;IACX,wBAAwB,GACxB,IAAI,OAAO,2BAAK,aAAa,YAAY,MAAM;IAC/C,iBAAiB,2BAAK;AACxB,EAAE,OAAO,GAAG;IACV,wBAAwB,GACxB,iBAAiB;AACnB;;;;ACRA,IAAI,OAAO,OAAO,WAAW,YAC3B,qDAAqD;AACrD,iBAAiB,SAAS,SAAS,IAAI,EAAE,SAAS;IAChD,IAAI,WAAW;QACb,KAAK,SAAS;QACd,KAAK,YAAY,OAAO,OAAO,UAAU,WAAW;YAClD,aAAa;gBACX,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,cAAc;YAChB;QACF;IACF;AACF;KAEA,mCAAmC;AACnC,iBAAiB,SAAS,SAAS,IAAI,EAAE,SAAS;IAChD,IAAI,WAAW;QACb,KAAK,SAAS;QACd,IAAI,WAAW,YAAa;QAC5B,SAAS,YAAY,UAAU;QAC/B,KAAK,YAAY,IAAI;QACrB,KAAK,UAAU,cAAc;IAC/B;AACF;;;;;;ACzBF,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,uEAAuE;AACvE,oEAAoE;AACpE,mEAAmE;AACnE,YAAY;AACZ;AACA,eAAe,GAEf,IAAI,mCAAa,OAAO,QAAQ,SAAU,GAAG;IAC3C,IAAI,OAAO,EAAE;IAEb,IAAK,IAAI,OAAO,IACd,KAAK,KAAK;IAGZ,OAAO;AACT;AACA,gBAAgB,GAGhB,iBAAiB;;;;;;AAMjB,yBAAoB,8BAAQ;AAG1B,oCAAoC;AACpC,IAAI,6BAAO,iCAAW;AAEtB,IAAK,IAAI,0BAAI,GAAG,0BAAI,2BAAK,QAAQ,0BAAK;IACpC,IAAI,+BAAS,0BAAI,CAAC,wBAAE;IACpB,IAAI,CAAC,6BAAO,SAAS,CAAC,6BAAO,EAAE,6BAAO,SAAS,CAAC,6BAAO,GAAG,gBAAkB,CAAC,6BAAO;AACtF;AAGF,SAAS,6BAAO,OAAO;IACrB,IAAI,CAAE,CAAA,IAAI,YAAY,4BAAK,GAAI,OAAO,IAAI,6BAAO;IACjD,OAAS,KAAK,IAAI,EAAE;IACpB,YAAc,IAAI,EAAE;IACpB,IAAI,CAAC,gBAAgB;IAErB,IAAI,SAAS;QACX,IAAI,QAAQ,aAAa,OAAO,IAAI,CAAC,WAAW;QAChD,IAAI,QAAQ,aAAa,OAAO,IAAI,CAAC,WAAW;QAEhD,IAAI,QAAQ,kBAAkB,OAAO;YACnC,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,KAAK,OAAO;QACnB;IACF;AACF;AAEA,OAAO,eAAe,6BAAO,WAAW,yBAAyB;IAC/D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF;AACA,OAAO,eAAe,6BAAO,WAAW,kBAAkB;IACxD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,eAAe;IACpD;AACF;AACA,OAAO,eAAe,6BAAO,WAAW,kBAAkB;IACxD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF,IAAI,4BAA4B;AAEhC,SAAS;IACP,6CAA6C;IAC7C,IAAI,IAAI,CAAC,eAAe,OAAO,QAAQ,+BAA+B;IACtE,gDAAgD;IAEhD,QAAQ,SAAS,+BAAS,IAAI;AAChC;AAEA,SAAS,8BAAQ,IAAI;IACnB,KAAK;AACP;AAEA,OAAO,eAAe,6BAAO,WAAW,aAAa;IACnD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,IAAI,IAAI,CAAC,mBAAmB,aAAa,IAAI,CAAC,mBAAmB,WAC/D,OAAO;QAGT,OAAO,IAAI,CAAC,eAAe,aAAa,IAAI,CAAC,eAAe;IAC9D;IACA,KAAK,SAAS,IAAI,KAAK;QACrB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,IAAI,CAAC,mBAAmB,aAAa,IAAI,CAAC,mBAAmB,WAC/D;SACA,iDAAiD;QACnD,qBAAqB;QAGrB,IAAI,CAAC,eAAe,YAAY;QAChC,IAAI,CAAC,eAAe,YAAY;IAClC;AACF;;;;AC1IA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,uCAAuC;AACvC,wEAAwE;AACxE,0CAA0C;AAC1C;AAEA,iBAAiB;AACjB,iBAAiB,GAEjB,SAAS,+BAAS,KAAK,EAAE,QAAQ,EAAE,EAAE;IACnC,IAAI,CAAC,QAAQ;IACb,IAAI,CAAC,WAAW;IAChB,IAAI,CAAC,WAAW;IAChB,IAAI,CAAC,OAAO;AACd,EAAE,uCAAuC;AACzC,gDAAgD;AAGhD,SAAS,oCAAc,KAAK;IAC1B,IAAI,QAAQ,IAAI;IAEhB,IAAI,CAAC,OAAO;IACZ,IAAI,CAAC,QAAQ;IAEb,IAAI,CAAC,SAAS;QACZ,qCAAe,OAAO;IACxB;AACF;AACA,kBAAkB,GAElB,eAAe,GAGf,IAAI;AACJ,gBAAgB,GAEhB,+BAAS,gBAAgB;;AACzB,eAAe,GAEf,IAAI,qCAAe;IACjB,WAAW;AACb;;;;uCASI;AAEJ,IAAI,sCAAgB,eAAO,cAAc,YAAa;AAEtD,SAAS,0CAAoB,KAAK;IAChC,OAAO,iCAAO,KAAK;AACrB;AAEA,SAAS,oCAAc,GAAG;IACxB,OAAO,iCAAO,SAAS,QAAQ,eAAe;AAChD;;;;;AAIA,IACI,yCAAmB;;;+CAEnB;AAAJ,IACI,6CAAuB,yCAAe,sBACtC,mDAA6B,yCAAe,4BAC5C,8CAAwB,yCAAe,uBACvC,+CAAyB,yCAAe,wBACxC,6CAAuB,yCAAe,sBACtC,+CAAyB,yCAAe,wBACxC,mDAA6B,yCAAe,4BAC5C,6CAAuB,yCAAe;AAE1C,IAAI,uCAAiB;;AAErB,yBAAoB,gCAAU;AAE9B,SAAS,6BAAO;;AAEhB,SAAS,oCAAc,OAAO,EAAE,MAAM,EAAE,QAAQ;IAC9C,+BAAS,gCAAU;IACnB,UAAU,WAAW,CAAC,GAAG,2DAA2D;IACpF,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,uEAAuE;IAEvE,IAAI,OAAO,aAAa,WAAW,WAAW,kBAAkB,8BAAQ,4DAA4D;IACpI,+BAA+B;IAE/B,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ;IAC5B,IAAI,UAAU,IAAI,CAAC,aAAa,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,oBAAoB,oDAAoD;IACrI,iEAAiE;IACjE,0DAA0D;IAE1D,IAAI,CAAC,gBAAgB,uCAAiB,IAAI,EAAE,SAAS,yBAAyB,WAAW,4BAA4B;IAErH,IAAI,CAAC,cAAc,OAAO,oBAAoB;IAE9C,IAAI,CAAC,YAAY,OAAO,gCAAgC;IAExD,IAAI,CAAC,SAAS,OAAO,2CAA2C;IAEhE,IAAI,CAAC,QAAQ,OAAO,2BAA2B;IAE/C,IAAI,CAAC,WAAW,OAAO,wBAAwB;IAE/C,IAAI,CAAC,YAAY,OAAO,kEAAkE;IAC1F,kEAAkE;IAClE,6BAA6B;IAE7B,IAAI,WAAW,QAAQ,kBAAkB;IACzC,IAAI,CAAC,gBAAgB,CAAC,UAAU,sEAAsE;IACtG,6DAA6D;IAC7D,uDAAuD;IAEvD,IAAI,CAAC,kBAAkB,QAAQ,mBAAmB,QAAQ,2DAA2D;IACrH,6DAA6D;IAC7D,kBAAkB;IAElB,IAAI,CAAC,SAAS,GAAG,qDAAqD;IAEtE,IAAI,CAAC,UAAU,OAAO,6DAA6D;IAEnF,IAAI,CAAC,SAAS,GAAG,qEAAqE;IACtF,iEAAiE;IACjE,oEAAoE;IACpE,0CAA0C;IAE1C,IAAI,CAAC,OAAO,MAAM,sEAAsE;IACxF,oEAAoE;IACpE,6CAA6C;IAE7C,IAAI,CAAC,mBAAmB,OAAO,iDAAiD;IAEhF,IAAI,CAAC,UAAU,SAAU,EAAE;QACzB,8BAAQ,QAAQ;IAClB,GAAG,kEAAkE;IAGrE,IAAI,CAAC,UAAU,MAAM,0DAA0D;IAE/E,IAAI,CAAC,WAAW;IAChB,IAAI,CAAC,kBAAkB;IACvB,IAAI,CAAC,sBAAsB,MAAM,kDAAkD;IACnF,gDAAgD;IAEhD,IAAI,CAAC,YAAY,GAAG,mEAAmE;IACvF,qDAAqD;IAErD,IAAI,CAAC,cAAc,OAAO,uEAAuE;IAEjG,IAAI,CAAC,eAAe,OAAO,wDAAwD;IAEnF,IAAI,CAAC,YAAY,QAAQ,cAAc,OAAO,qEAAqE;IAEnH,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,aAAa,0BAA0B;IAEpE,IAAI,CAAC,uBAAuB,GAAG,oDAAoD;IACnF,6DAA6D;IAE7D,IAAI,CAAC,qBAAqB,IAAI,oCAAc,IAAI;AAClD;AAEA,oCAAc,UAAU,YAAY,SAAS;IAC3C,IAAI,UAAU,IAAI,CAAC;IACnB,IAAI,MAAM,EAAE;IAEZ,MAAO,QAAS;QACd,IAAI,KAAK;QACT,UAAU,QAAQ;IACpB;IAEA,OAAO;AACT;AAEC,CAAA;IACC,IAAI;QACF,OAAO,eAAe,oCAAc,WAAW,UAAU;YACvD,KAAK,mCAAa,UAAU,SAAS;gBACnC,OAAO,IAAI,CAAC;YACd,GAAG,8EAAmF;QACxF;IACF,EAAE,OAAO,GAAG,CAAC;AACf,CAAA,KAAM,qEAAqE;AAC3E,iDAAiD;AAGjD,IAAI;AAEJ,IAAI,OAAO,WAAW,cAAc,OAAO,eAAe,OAAO,SAAS,SAAS,CAAC,OAAO,YAAY,KAAK,YAAY;IACtH,wCAAkB,SAAS,SAAS,CAAC,OAAO,YAAY;IACxD,OAAO,eAAe,gCAAU,OAAO,aAAa;QAClD,OAAO,SAAS,MAAM,MAAM;YAC1B,IAAI,sCAAgB,KAAK,IAAI,EAAE,SAAS,OAAO;YAC/C,IAAI,IAAI,KAAK,gCAAU,OAAO;YAC9B,OAAO,UAAU,OAAO,0BAA0B;QACpD;IACF;AACF,OACE,wCAAkB,SAAS,gBAAgB,MAAM;IAC/C,OAAO,kBAAkB,IAAI;AAC/B;;AAGF,SAAS,+BAAS,OAAO;IACvB,+BAAS,gCAAU,0BAA6B,6CAA6C;IAC7F,kEAAkE;IAClE,mEAAmE;IACnE,8EAA8E;IAC9E,2EAA2E;IAC3E,0DAA0D;IAC1D,yEAAyE;IACzE,sDAAsD;IAEtD,IAAI,WAAW,IAAI,YAAY;IAC/B,IAAI,CAAC,YAAY,CAAC,sCAAgB,KAAK,gCAAU,IAAI,GAAG,OAAO,IAAI,+BAAS;IAC5E,IAAI,CAAC,iBAAiB,IAAI,oCAAc,SAAS,IAAI,EAAE,WAAW,UAAU;IAE5E,IAAI,CAAC,WAAW;IAEhB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,UAAU,YAAY,IAAI,CAAC,SAAS,QAAQ;QAC/D,IAAI,OAAO,QAAQ,WAAW,YAAY,IAAI,CAAC,UAAU,QAAQ;QACjE,IAAI,OAAO,QAAQ,YAAY,YAAY,IAAI,CAAC,WAAW,QAAQ;QACnE,IAAI,OAAO,QAAQ,UAAU,YAAY,IAAI,CAAC,SAAS,QAAQ;IACjE;IAEA,OAAO,KAAK,IAAI;AAClB,EAAE,mEAAmE;AAGrE,+BAAS,UAAU,OAAO;IACxB,qCAAe,IAAI,EAAE,IAAI;AAC3B;AAEA,SAAS,oCAAc,MAAM,EAAE,EAAE;IAC/B,IAAI,KAAK,IAAI,oDAA8B,oEAAoE;IAE/G,qCAAe,QAAQ;IACvB,QAAQ,SAAS,IAAI;AACvB,EAAE,4EAA4E;AAC9E,4EAA4E;AAC5E,mEAAmE;AAGnE,SAAS,iCAAW,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;IAC1C,IAAI;IAEJ,IAAI,UAAU,MACZ,KAAK,IAAI;SACJ,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,YAC7C,KAAK,IAAI,2CAAqB,SAAS;QAAC;QAAU;KAAS,EAAE;IAG/D,IAAI,IAAI;QACN,qCAAe,QAAQ;QACvB,QAAQ,SAAS,IAAI;QACrB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,+BAAS,UAAU,QAAQ,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACtD,IAAI,QAAQ,IAAI,CAAC;IACjB,IAAI,MAAM;IAEV,IAAI,QAAQ,CAAC,MAAM,cAAc,oCAAc;IAE/C,IAAI,SAAS,CAAC,iCAAO,SAAS,QAC5B,QAAQ,0CAAoB;IAG9B,IAAI,OAAO,aAAa,YAAY;QAClC,KAAK;QACL,WAAW;IACb;IAEA,IAAI,OAAO,WAAW;SAAc,IAAI,CAAC,UAAU,WAAW,MAAM;IACpE,IAAI,OAAO,OAAO,YAAY,KAAK;IACnC,IAAI,MAAM,QAAQ,oCAAc,IAAI,EAAE;SAAS,IAAI,SAAS,iCAAW,IAAI,EAAE,OAAO,OAAO,KAAK;QAC9F,MAAM;QACN,MAAM,oCAAc,IAAI,EAAE,OAAO,OAAO,OAAO,UAAU;IAC3D;IACA,OAAO;AACT;AAEA,+BAAS,UAAU,OAAO;IACxB,IAAI,CAAC,eAAe;AACtB;AAEA,+BAAS,UAAU,SAAS;IAC1B,IAAI,QAAQ,IAAI,CAAC;IAEjB,IAAI,MAAM,QAAQ;QAChB,MAAM;QACN,IAAI,CAAC,MAAM,WAAW,CAAC,MAAM,UAAU,CAAC,MAAM,oBAAoB,MAAM,iBAAiB,kCAAY,IAAI,EAAE;IAC7G;AACF;AAEA,+BAAS,UAAU,qBAAqB,SAAS,mBAAmB,QAAQ;IAC1E,6CAA6C;IAC7C,IAAI,OAAO,aAAa,UAAU,WAAW,SAAS;IACtD,IAAI,CAAE,CAAA;QAAC;QAAO;QAAQ;QAAS;QAAS;QAAU;QAAU;QAAQ;QAAS;QAAW;QAAY;KAAM,CAAC,QAAQ,AAAC,CAAA,WAAW,EAAC,EAAG,iBAAiB,EAAC,GAAI,MAAM,IAAI,2CAAqB;IACxL,IAAI,CAAC,eAAe,kBAAkB;IACtC,OAAO,IAAI;AACb;AAEA,OAAO,eAAe,+BAAS,WAAW,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,eAAe;IACpD;AACF;AAEA,SAAS,kCAAY,KAAK,EAAE,KAAK,EAAE,QAAQ;IACzC,IAAI,CAAC,MAAM,cAAc,MAAM,kBAAkB,SAAS,OAAO,UAAU,UACzE,QAAQ,iCAAO,KAAK,OAAO;IAG7B,OAAO;AACT;AAEA,OAAO,eAAe,+BAAS,WAAW,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF,IAAI,yDAAyD;AAC7D,2DAA2D;AAC3D,oEAAoE;AAEpE,SAAS,oCAAc,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,IAAI,CAAC,OAAO;QACV,IAAI,WAAW,kCAAY,OAAO,OAAO;QAEzC,IAAI,UAAU,UAAU;YACtB,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;IACF;IAEA,IAAI,MAAM,MAAM,aAAa,IAAI,MAAM;IACvC,MAAM,UAAU;IAChB,IAAI,MAAM,MAAM,SAAS,MAAM,eAAe,qEAAqE;IAEnH,IAAI,CAAC,KAAK,MAAM,YAAY;IAE5B,IAAI,MAAM,WAAW,MAAM,QAAQ;QACjC,IAAI,OAAO,MAAM;QACjB,MAAM,sBAAsB;YAC1B,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;QACR;QAEA,IAAI,MACF,KAAK,OAAO,MAAM;aAElB,MAAM,kBAAkB,MAAM;QAGhC,MAAM,wBAAwB;IAChC,OACE,8BAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;IAGtD,OAAO;AACT;AAEA,SAAS,8BAAQ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,MAAM,WAAW;IACjB,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,MAAM,OAAO;IACb,IAAI,MAAM,WAAW,MAAM,QAAQ,IAAI,2CAAqB;SAAe,IAAI,QAAQ,OAAO,QAAQ,OAAO,MAAM;SAAc,OAAO,OAAO,OAAO,UAAU,MAAM;IACtK,MAAM,OAAO;AACf;AAEA,SAAS,mCAAa,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAC/C,EAAE,MAAM;IAER,IAAI,MAAM;QACR,0DAA0D;QAC1D,yCAAyC;QACzC,QAAQ,SAAS,IAAI,KAAK,kDAAkD;QAC5E,cAAc;QAEd,QAAQ,SAAS,mCAAa,QAAQ;QACtC,OAAO,eAAe,eAAe;QACrC,qCAAe,QAAQ;IACzB,OAAO;QACL,6CAA6C;QAC7C,cAAc;QACd,GAAG;QACH,OAAO,eAAe,eAAe;QACrC,qCAAe,QAAQ,KAAK,wCAAwC;QACpE,sBAAsB;QAEtB,kCAAY,QAAQ;IACtB;AACF;AAEA,SAAS,yCAAmB,KAAK;IAC/B,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,MAAM,UAAU,MAAM;IACtB,MAAM,WAAW;AACnB;AAEA,SAAS,8BAAQ,MAAM,EAAE,EAAE;IACzB,IAAI,QAAQ,OAAO;IACnB,IAAI,OAAO,MAAM;IACjB,IAAI,KAAK,MAAM;IACf,IAAI,OAAO,OAAO,YAAY,MAAM,IAAI;IACxC,yCAAmB;IACnB,IAAI,IAAI,mCAAa,QAAQ,OAAO,MAAM,IAAI;SAAS;QACrD,8DAA8D;QAC9D,IAAI,WAAW,iCAAW,UAAU,OAAO;QAE3C,IAAI,CAAC,YAAY,CAAC,MAAM,UAAU,CAAC,MAAM,oBAAoB,MAAM,iBACjE,kCAAY,QAAQ;QAGtB,IAAI,MACF,QAAQ,SAAS,kCAAY,QAAQ,OAAO,UAAU;aAEtD,iCAAW,QAAQ,OAAO,UAAU;IAExC;AACF;AAEA,SAAS,iCAAW,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC7C,IAAI,CAAC,UAAU,mCAAa,QAAQ;IACpC,MAAM;IACN;IACA,kCAAY,QAAQ;AACtB,EAAE,iEAAiE;AACnE,mEAAmE;AACnE,wDAAwD;AAGxD,SAAS,mCAAa,MAAM,EAAE,KAAK;IACjC,IAAI,MAAM,WAAW,KAAK,MAAM,WAAW;QACzC,MAAM,YAAY;QAClB,OAAO,KAAK;IACd;AACF,EAAE,8DAA8D;AAGhE,SAAS,kCAAY,MAAM,EAAE,KAAK;IAChC,MAAM,mBAAmB;IACzB,IAAI,QAAQ,MAAM;IAElB,IAAI,OAAO,WAAW,SAAS,MAAM,MAAM;QACzC,8CAA8C;QAC9C,IAAI,IAAI,MAAM;QACd,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,SAAS,MAAM;QACnB,OAAO,QAAQ;QACf,IAAI,QAAQ;QACZ,IAAI,aAAa;QAEjB,MAAO,MAAO;YACZ,MAAM,CAAC,MAAM,GAAG;YAChB,IAAI,CAAC,MAAM,OAAO,aAAa;YAC/B,QAAQ,MAAM;YACd,SAAS;QACX;QAEA,OAAO,aAAa;QACpB,8BAAQ,QAAQ,OAAO,MAAM,MAAM,QAAQ,QAAQ,IAAI,OAAO,SAAS,oEAAoE;QAC3I,oCAAoC;QAEpC,MAAM;QACN,MAAM,sBAAsB;QAE5B,IAAI,OAAO,MAAM;YACf,MAAM,qBAAqB,OAAO;YAClC,OAAO,OAAO;QAChB,OACE,MAAM,qBAAqB,IAAI,oCAAc;QAG/C,MAAM,uBAAuB;IAC/B,OAAO;QACL,qCAAqC;QACrC,MAAO,MAAO;YACZ,IAAI,QAAQ,MAAM;YAClB,IAAI,WAAW,MAAM;YACrB,IAAI,KAAK,MAAM;YACf,IAAI,MAAM,MAAM,aAAa,IAAI,MAAM;YACvC,8BAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;YACpD,QAAQ,MAAM;YACd,MAAM,wBAAwB,kDAAkD;YAChF,+CAA+C;YAC/C,uDAAuD;YACvD,yDAAyD;YAEzD,IAAI,MAAM,SACR;QAEJ;QAEA,IAAI,UAAU,MAAM,MAAM,sBAAsB;IAClD;IAEA,MAAM,kBAAkB;IACxB,MAAM,mBAAmB;AAC3B;AAEA,+BAAS,UAAU,SAAS,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACvD,GAAG,IAAI,iDAA2B;AACpC;AAEA,+BAAS,UAAU,UAAU;AAE7B,+BAAS,UAAU,MAAM,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACpD,IAAI,QAAQ,IAAI,CAAC;IAEjB,IAAI,OAAO,UAAU,YAAY;QAC/B,KAAK;QACL,QAAQ;QACR,WAAW;IACb,OAAO,IAAI,OAAO,aAAa,YAAY;QACzC,KAAK;QACL,WAAW;IACb;IAEA,IAAI,UAAU,QAAQ,UAAU,WAAW,IAAI,CAAC,MAAM,OAAO,WAAW,uBAAuB;IAE/F,IAAI,MAAM,QAAQ;QAChB,MAAM,SAAS;QACf,IAAI,CAAC;IACP,EAAE,kCAAkC;IAGpC,IAAI,CAAC,MAAM,QAAQ,kCAAY,IAAI,EAAE,OAAO;IAC5C,OAAO,IAAI;AACb;AAEA,OAAO,eAAe,+BAAS,WAAW,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF;AAEA,SAAS,iCAAW,KAAK;IACvB,OAAO,MAAM,UAAU,MAAM,WAAW,KAAK,MAAM,oBAAoB,QAAQ,CAAC,MAAM,YAAY,CAAC,MAAM;AAC3G;AAEA,SAAS,gCAAU,MAAM,EAAE,KAAK;IAC9B,OAAO,OAAO,SAAU,GAAG;QACzB,MAAM;QAEN,IAAI,KACF,qCAAe,QAAQ;QAGzB,MAAM,cAAc;QACpB,OAAO,KAAK;QACZ,kCAAY,QAAQ;IACtB;AACF;AAEA,SAAS,gCAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,CAAC,MAAM,eAAe,CAAC,MAAM;QAC/B,IAAI,OAAO,OAAO,WAAW,cAAc,CAAC,MAAM,WAAW;YAC3D,MAAM;YACN,MAAM,cAAc;YACpB,QAAQ,SAAS,iCAAW,QAAQ;QACtC,OAAO;YACL,MAAM,cAAc;YACpB,OAAO,KAAK;QACd;;AAEJ;AAEA,SAAS,kCAAY,MAAM,EAAE,KAAK;IAChC,IAAI,OAAO,iCAAW;IAEtB,IAAI,MAAM;QACR,gCAAU,QAAQ;QAElB,IAAI,MAAM,cAAc,GAAG;YACzB,MAAM,WAAW;YACjB,OAAO,KAAK;YAEZ,IAAI,MAAM,aAAa;gBACrB,oDAAoD;gBACpD,wDAAwD;gBACxD,IAAI,SAAS,OAAO;gBAEpB,IAAI,CAAC,UAAU,OAAO,eAAe,OAAO,YAC1C,OAAO;YAEX;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kCAAY,MAAM,EAAE,KAAK,EAAE,EAAE;IACpC,MAAM,SAAS;IACf,kCAAY,QAAQ;IAEpB,IAAI;QACF,IAAI,MAAM,UAAU,QAAQ,SAAS;aAAS,OAAO,KAAK,UAAU;;IAGtE,MAAM,QAAQ;IACd,OAAO,WAAW;AACpB;AAEA,SAAS,qCAAe,OAAO,EAAE,KAAK,EAAE,GAAG;IACzC,IAAI,QAAQ,QAAQ;IACpB,QAAQ,QAAQ;IAEhB,MAAO,MAAO;QACZ,IAAI,KAAK,MAAM;QACf,MAAM;QACN,GAAG;QACH,QAAQ,MAAM;IAChB,EAAE,0BAA0B;IAG5B,MAAM,mBAAmB,OAAO;AAClC;AAEA,OAAO,eAAe,+BAAS,WAAW,aAAa;IACrD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK,SAAS;QACZ,IAAI,IAAI,CAAC,mBAAmB,WAC1B,OAAO;QAGT,OAAO,IAAI,CAAC,eAAe;IAC7B;IACA,KAAK,SAAS,IAAI,KAAK;QACrB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,gBACR;SACA,iDAAiD;QACnD,qBAAqB;QAGrB,IAAI,CAAC,eAAe,YAAY;IAClC;AACF;AACA,+BAAS,UAAU,UAAU;AAC7B,+BAAS,UAAU,aAAa;AAEhC,+BAAS,UAAU,WAAW,SAAU,GAAG,EAAE,EAAE;IAC7C,GAAG;AACL;;;;ACvrBA;;CAEC;AAED,iBAAiB;;;;;;;;;ACLjB,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAuDzC,4EAA4E;AAC5E,wEAAwE;AACxE,cAAc;AACd,IAAA;AAxDA;;;uCAII;AACJ,gBAAgB,GAEhB,IAAI,mCAAa,iCAAO,cAAc,SAAU,QAAQ;IACtD,WAAW,KAAK;IAChB,OAAQ,YAAY,SAAS;QAC3B,KAAK;QAAM,KAAK;QAAO,KAAK;QAAQ,KAAK;QAAQ,KAAK;QAAS,KAAK;QAAS,KAAK;QAAO,KAAK;QAAQ,KAAK;QAAU,KAAK;QAAW,KAAK;YACxI,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,yCAAmB,GAAG;IAC7B,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI;IACJ,MAAO,KACL,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,IAAI,SAAS,QAAQ,YAAY;YACjC,MAAM,AAAC,CAAA,KAAK,GAAE,EAAG;YACjB,UAAU;IACd;AAEJ;AAEA,wEAAwE;AACxE,0DAA0D;AAC1D,SAAS,wCAAkB,GAAG;IAC5B,IAAI,OAAO,yCAAmB;IAC9B,IAAI,OAAO,SAAS,YAAa,CAAA,iCAAO,eAAe,oCAAc,CAAC,iCAAW,IAAG,GAAI,MAAM,IAAI,MAAM,uBAAuB;IAC/H,OAAO,QAAQ;AACjB;AAKA,4CAAwB;AACxB,SAAS,oCAAc,QAAQ;IAC7B,IAAI,CAAC,WAAW,wCAAkB;IAClC,IAAI;IACJ,OAAQ,IAAI,CAAC;QACX,KAAK;YACH,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,MAAM;YACX,KAAK;YACL;QACF,KAAK;YACH,IAAI,CAAC,WAAW;YAChB,KAAK;YACL;QACF,KAAK;YACH,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,MAAM;YACX,KAAK;YACL;QACF;YACE,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,MAAM;YACX;IACJ;IACA,IAAI,CAAC,WAAW;IAChB,IAAI,CAAC,YAAY;IACjB,IAAI,CAAC,WAAW,iCAAO,YAAY;AACrC;AAEA,oCAAc,UAAU,QAAQ,SAAU,GAAG;IAC3C,IAAI,IAAI,WAAW,GAAG,OAAO;IAC7B,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI,CAAC,UAAU;QACjB,IAAI,IAAI,CAAC,SAAS;QAClB,IAAI,MAAM,WAAW,OAAO;QAC5B,IAAI,IAAI,CAAC;QACT,IAAI,CAAC,WAAW;IAClB,OACE,IAAI;IAEN,IAAI,IAAI,IAAI,QAAQ,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK;IACtE,OAAO,KAAK;AACd;AAEA,oCAAc,UAAU,MAAM;AAE9B,+CAA+C;AAC/C,oCAAc,UAAU,OAAO;AAE/B,+EAA+E;AAC/E,oCAAc,UAAU,WAAW,SAAU,GAAG;IAC9C,IAAI,IAAI,CAAC,YAAY,IAAI,QAAQ;QAC/B,IAAI,KAAK,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAChE,OAAO,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvD;IACA,IAAI,KAAK,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,GAAG,IAAI;IAC/D,IAAI,CAAC,YAAY,IAAI;AACvB;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,SAAS,oCAAc,IAAI;IACzB,IAAI,QAAQ,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;IAC3I,OAAO,QAAQ,MAAM,OAAO,KAAK;AACnC;AAEA,sEAAsE;AACtE,gFAAgF;AAChF,uEAAuE;AACvE,SAAS,0CAAoB,IAAI,EAAE,GAAG,EAAE,CAAC;IACvC,IAAI,IAAI,IAAI,SAAS;IACrB,IAAI,IAAI,GAAG,OAAO;IAClB,IAAI,KAAK,oCAAc,GAAG,CAAC,EAAE;IAC7B,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG,KAAK,WAAW,KAAK;QACjC,OAAO;IACT;IACA,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,OAAO;IACjC,KAAK,oCAAc,GAAG,CAAC,EAAE;IACzB,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG,KAAK,WAAW,KAAK;QACjC,OAAO;IACT;IACA,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,OAAO;IACjC,KAAK,oCAAc,GAAG,CAAC,EAAE;IACzB,IAAI,MAAM,GAAG;QACX,IAAI,KAAK;YACP,IAAI,OAAO,GAAG,KAAK;iBAAO,KAAK,WAAW,KAAK;;QAEjD,OAAO;IACT;IACA,OAAO;AACT;AAEA,2EAA2E;AAC3E,6EAA6E;AAC7E,4EAA4E;AAC5E,gFAAgF;AAChF,4EAA4E;AAC5E,gFAAgF;AAChF,+EAA+E;AAC/E,QAAQ;AACR,SAAS,0CAAoB,IAAI,EAAE,GAAG,EAAE,CAAC;IACvC,IAAI,AAAC,CAAA,GAAG,CAAC,EAAE,GAAG,IAAG,MAAO,MAAM;QAC5B,KAAK,WAAW;QAChB,OAAO;IACT;IACA,IAAI,KAAK,WAAW,KAAK,IAAI,SAAS,GAAG;QACvC,IAAI,AAAC,CAAA,GAAG,CAAC,EAAE,GAAG,IAAG,MAAO,MAAM;YAC5B,KAAK,WAAW;YAChB,OAAO;QACT;QACA,IAAI,KAAK,WAAW,KAAK,IAAI,SAAS,GACpC;YAAA,IAAI,AAAC,CAAA,GAAG,CAAC,EAAE,GAAG,IAAG,MAAO,MAAM;gBAC5B,KAAK,WAAW;gBAChB,OAAO;YACT;QAAA;IAEJ;AACF;AAEA,+EAA+E;AAC/E,SAAS,mCAAa,GAAG;IACvB,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC;IAC9B,IAAI,IAAI,0CAAoB,IAAI,EAAE,KAAK;IACvC,IAAI,MAAM,WAAW,OAAO;IAC5B,IAAI,IAAI,CAAC,YAAY,IAAI,QAAQ;QAC/B,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,GAAG,IAAI,CAAC;QACnC,OAAO,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvD;IACA,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,GAAG,IAAI;IAClC,IAAI,CAAC,YAAY,IAAI;AACvB;AAEA,8EAA8E;AAC9E,2EAA2E;AAC3E,iCAAiC;AACjC,SAAS,+BAAS,GAAG,EAAE,CAAC;IACtB,IAAI,QAAQ,0CAAoB,IAAI,EAAE,KAAK;IAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,OAAO,IAAI,SAAS,QAAQ;IAChD,IAAI,CAAC,YAAY;IACjB,IAAI,MAAM,IAAI,SAAU,CAAA,QAAQ,IAAI,CAAC,QAAO;IAC5C,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG;IAC3B,OAAO,IAAI,SAAS,QAAQ,GAAG;AACjC;AAEA,uEAAuE;AACvE,aAAa;AACb,SAAS,8BAAQ,GAAG;IAClB,IAAI,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO;IAC9C,IAAI,IAAI,CAAC,UAAU,OAAO,IAAI;IAC9B,OAAO;AACT;AAEA,gFAAgF;AAChF,0EAA0E;AAC1E,8EAA8E;AAC9E,sCAAsC;AACtC,SAAS,gCAAU,GAAG,EAAE,CAAC;IACvB,IAAI,AAAC,CAAA,IAAI,SAAS,CAAA,IAAK,MAAM,GAAG;QAC9B,IAAI,IAAI,IAAI,SAAS,WAAW;QAChC,IAAI,GAAG;YACL,IAAI,IAAI,EAAE,WAAW,EAAE,SAAS;YAChC,IAAI,KAAK,UAAU,KAAK,QAAQ;gBAC9B,IAAI,CAAC,WAAW;gBAChB,IAAI,CAAC,YAAY;gBACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,EAAE;gBACtC,OAAO,EAAE,MAAM,GAAG;YACpB;QACF;QACA,OAAO;IACT;IACA,IAAI,CAAC,WAAW;IAChB,IAAI,CAAC,YAAY;IACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,EAAE;IACtC,OAAO,IAAI,SAAS,WAAW,GAAG,IAAI,SAAS;AACjD;AAEA,gFAAgF;AAChF,4DAA4D;AAC5D,SAAS,+BAAS,GAAG;IACnB,IAAI,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO;IAC9C,IAAI,IAAI,CAAC,UAAU;QACjB,IAAI,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC;QAChC,OAAO,IAAI,IAAI,CAAC,SAAS,SAAS,WAAW,GAAG;IAClD;IACA,OAAO;AACT;AAEA,SAAS,iCAAW,GAAG,EAAE,CAAC;IACxB,IAAI,IAAI,AAAC,CAAA,IAAI,SAAS,CAAA,IAAK;IAC3B,IAAI,MAAM,GAAG,OAAO,IAAI,SAAS,UAAU;IAC3C,IAAI,CAAC,WAAW,IAAI;IACpB,IAAI,CAAC,YAAY;IACjB,IAAI,MAAM,GACR,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,EAAE;SACjC;QACL,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,EAAE;QACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,EAAE;IACxC;IACA,OAAO,IAAI,SAAS,UAAU,GAAG,IAAI,SAAS;AAChD;AAEA,SAAS,gCAAU,GAAG;IACpB,IAAI,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO;IAC9C,IAAI,IAAI,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC,SAAS,SAAS,UAAU,GAAG,IAAI,IAAI,CAAC;IAC3E,OAAO;AACT;AAEA,4EAA4E;AAC5E,SAAS,kCAAY,GAAG;IACtB,OAAO,IAAI,SAAS,IAAI,CAAC;AAC3B;AAEA,SAAS,gCAAU,GAAG;IACpB,OAAO,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO;AAC/C;;;;ACvSA,kFAAkF,GAClF,yCAAyC;AAEzC,IAAI,+BAAS,cAAO;AAEpB,oDAAoD;AACpD,SAAS,gCAAW,GAAG,EAAE,GAAG;IAC1B,IAAK,IAAI,OAAO,IACd,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;AAEvB;AACA,IAAI,6BAAO,QAAQ,6BAAO,SAAS,6BAAO,eAAe,6BAAO,iBAC9D,iBAAiB;KACZ;IACL,yCAAyC;IACzC,gCAAU,eAAQ;IAClB,eAAQ,SAAS;AACnB;AAEA,SAAS,iCAAY,GAAG,EAAE,gBAAgB,EAAE,MAAM;IAChD,OAAO,6BAAO,KAAK,kBAAkB;AACvC;AAEA,iCAAW,YAAY,OAAO,OAAO,6BAAO;AAE5C,kCAAkC;AAClC,gCAAU,8BAAQ;AAElB,iCAAW,OAAO,SAAU,GAAG,EAAE,gBAAgB,EAAE,MAAM;IACvD,IAAI,OAAO,QAAQ,UACjB,MAAM,IAAI,UAAU;IAEtB,OAAO,6BAAO,KAAK,kBAAkB;AACvC;AAEA,iCAAW,QAAQ,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/C,IAAI,OAAO,SAAS,UAClB,MAAM,IAAI,UAAU;IAEtB,IAAI,MAAM,6BAAO;IACjB,IAAI,SAAS;QACX,IAAI,OAAO,aAAa,UACtB,IAAI,KAAK,MAAM;aAEf,IAAI,KAAK;WAGX,IAAI,KAAK;IAEX,OAAO;AACT;AAEA,iCAAW,cAAc,SAAU,IAAI;IACrC,IAAI,OAAO,SAAS,UAClB,MAAM,IAAI,UAAU;IAEtB,OAAO,6BAAO;AAChB;AAEA,iCAAW,kBAAkB,SAAU,IAAI;IACzC,IAAI,OAAO,SAAS,UAClB,MAAM,IAAI,UAAU;IAEtB,OAAO,cAAO,WAAW;AAC3B;;;;;;AChEA;AAEA,IAAI;AAEJ,SAAS,sCAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,IAAI,OAAO,KAAO,OAAO,eAAe,KAAK,KAAK;QAAE,OAAO;QAAO,YAAY;QAAM,cAAc;QAAM,UAAU;IAAK;SAAa,GAAG,CAAC,IAAI,GAAG;IAAS,OAAO;AAAK;;;AAIhN,IAAI,qCAAe,OAAO;AAC1B,IAAI,oCAAc,OAAO;AACzB,IAAI,+BAAS,OAAO;AACpB,IAAI,+BAAS,OAAO;AACpB,IAAI,qCAAe,OAAO;AAC1B,IAAI,uCAAiB,OAAO;AAC5B,IAAI,gCAAU,OAAO;AAErB,SAAS,uCAAiB,KAAK,EAAE,IAAI;IACnC,OAAO;QACL,OAAO;QACP,MAAM;IACR;AACF;AAEA,SAAS,qCAAe,IAAI;IAC1B,IAAI,UAAU,IAAI,CAAC,mCAAa;IAEhC,IAAI,YAAY,MAAM;QACpB,IAAI,OAAO,IAAI,CAAC,8BAAQ,CAAC,QAAQ,2BAA2B;QAC5D,sCAAsC;QACtC,UAAU;QAEV,IAAI,SAAS,MAAM;YACjB,IAAI,CAAC,mCAAa,GAAG;YACrB,IAAI,CAAC,mCAAa,GAAG;YACrB,IAAI,CAAC,kCAAY,GAAG;YACpB,QAAQ,uCAAiB,MAAM;QACjC;IACF;AACF;AAEA,SAAS,iCAAW,IAAI;IACtB,8CAA8C;IAC9C,sCAAsC;IACtC,QAAQ,SAAS,sCAAgB;AACnC;AAEA,SAAS,kCAAY,WAAW,EAAE,IAAI;IACpC,OAAO,SAAU,OAAO,EAAE,MAAM;QAC9B,YAAY,KAAK;YACf,IAAI,IAAI,CAAC,6BAAO,EAAE;gBAChB,QAAQ,uCAAiB,WAAW;gBACpC;YACF;YAEA,IAAI,CAAC,qCAAe,CAAC,SAAS;QAChC,GAAG;IACL;AACF;AAEA,IAAI,+CAAyB,OAAO,eAAe,YAAa;AAChE,IAAI,6DAAuC,OAAO,eAAgB,CAAA,8CAAwB;IACxF,IAAI,UAAS;QACX,OAAO,IAAI,CAAC,8BAAQ;IACtB;IAEA,MAAM,SAAS;QACb,IAAI,QAAQ,IAAI;QAEhB,gDAAgD;QAChD,uBAAuB;QACvB,IAAI,QAAQ,IAAI,CAAC,6BAAO;QAExB,IAAI,UAAU,MACZ,OAAO,QAAQ,OAAO;QAGxB,IAAI,IAAI,CAAC,6BAAO,EACd,OAAO,QAAQ,QAAQ,uCAAiB,WAAW;QAGrD,IAAI,IAAI,CAAC,8BAAQ,CAAC,WAChB,4DAA4D;QAC5D,sDAAsD;QACtD,8DAA8D;QAC9D,yBAAyB;QACzB,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,QAAQ,SAAS;gBACf,IAAI,KAAK,CAAC,6BAAO,EACf,OAAO,KAAK,CAAC,6BAAO;qBAEpB,QAAQ,uCAAiB,WAAW;YAExC;QACF;SACA,mCAAmC;QACrC,kDAAkD;QAClD,sDAAsD;QACtD,6CAA6C;QAG7C,IAAI,cAAc,IAAI,CAAC,mCAAa;QACpC,IAAI;QAEJ,IAAI,aACF,UAAU,IAAI,QAAQ,kCAAY,aAAa,IAAI;aAC9C;YACL,mDAAmD;YACnD,sCAAsC;YACtC,IAAI,OAAO,IAAI,CAAC,8BAAQ,CAAC;YAEzB,IAAI,SAAS,MACX,OAAO,QAAQ,QAAQ,uCAAiB,MAAM;YAGhD,UAAU,IAAI,QAAQ,IAAI,CAAC,qCAAe;QAC5C;QAEA,IAAI,CAAC,mCAAa,GAAG;QACrB,OAAO;IACT;AACF,GAAG,sCAAgB,6CAAuB,OAAO,eAAe;IAC9D,OAAO,IAAI;AACb,IAAI,sCAAgB,6CAAuB,UAAU,SAAS;IAC5D,IAAI,SAAS,IAAI;IAEjB,oCAAoC;IACpC,6DAA6D;IAC7D,qCAAqC;IACrC,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC1C,MAAM,CAAC,8BAAQ,CAAC,QAAQ,MAAM,SAAU,GAAG;YACzC,IAAI,KAAK;gBACP,OAAO;gBACP;YACF;YAEA,QAAQ,uCAAiB,WAAW;QACtC;IACF;AACF,IAAI,2CAAoB,GAAI;AAE5B,IAAI,0DAAoC,SAAS,kCAAkC,MAAM;IACvF,IAAI;IAEJ,IAAI,WAAW,OAAO,OAAO,4DAAuC,CAAA,iBAAiB,CAAC,GAAG,sCAAgB,gBAAgB,+BAAS;QAChI,OAAO;QACP,UAAU;IACZ,IAAI,sCAAgB,gBAAgB,oCAAc;QAChD,OAAO;QACP,UAAU;IACZ,IAAI,sCAAgB,gBAAgB,mCAAa;QAC/C,OAAO;QACP,UAAU;IACZ,IAAI,sCAAgB,gBAAgB,8BAAQ;QAC1C,OAAO;QACP,UAAU;IACZ,IAAI,sCAAgB,gBAAgB,8BAAQ;QAC1C,OAAO,OAAO,eAAe;QAC7B,UAAU;IACZ,IAAI,sCAAgB,gBAAgB,sCAAgB;QAClD,OAAO,SAAS,MAAM,OAAO,EAAE,MAAM;YACnC,IAAI,OAAO,QAAQ,CAAC,8BAAQ,CAAC;YAE7B,IAAI,MAAM;gBACR,QAAQ,CAAC,mCAAa,GAAG;gBACzB,QAAQ,CAAC,mCAAa,GAAG;gBACzB,QAAQ,CAAC,kCAAY,GAAG;gBACxB,QAAQ,uCAAiB,MAAM;YACjC,OAAO;gBACL,QAAQ,CAAC,mCAAa,GAAG;gBACzB,QAAQ,CAAC,kCAAY,GAAG;YAC1B;QACF;QACA,UAAU;IACZ,IAAI,cAAa;IACjB,QAAQ,CAAC,mCAAa,GAAG;IACzB,OAAS,QAAQ,SAAU,GAAG;QAC5B,IAAI,OAAO,IAAI,SAAS,8BAA8B;YACpD,IAAI,SAAS,QAAQ,CAAC,kCAAY,EAAE,mDAAmD;YACvF,yCAAyC;YAEzC,IAAI,WAAW,MAAM;gBACnB,QAAQ,CAAC,mCAAa,GAAG;gBACzB,QAAQ,CAAC,mCAAa,GAAG;gBACzB,QAAQ,CAAC,kCAAY,GAAG;gBACxB,OAAO;YACT;YAEA,QAAQ,CAAC,6BAAO,GAAG;YACnB;QACF;QAEA,IAAI,UAAU,QAAQ,CAAC,mCAAa;QAEpC,IAAI,YAAY,MAAM;YACpB,QAAQ,CAAC,mCAAa,GAAG;YACzB,QAAQ,CAAC,mCAAa,GAAG;YACzB,QAAQ,CAAC,kCAAY,GAAG;YACxB,QAAQ,uCAAiB,WAAW;QACtC;QAEA,QAAQ,CAAC,6BAAO,GAAG;IACrB;IACA,OAAO,GAAG,YAAY,iCAAW,KAAK,MAAM;IAC5C,OAAO;AACT;AAEA,iBAAiB;;;;AC9MjB,8DAA8D;AAC9D,yDAAyD;AACzD;;;AAEA,IAAI,mDAA6B,aAAiC;AAElE,SAAS,2BAAK,QAAQ;IACpB,IAAI,SAAS;IACb,OAAO;QACL,IAAI,QAAQ;QACZ,SAAS;QAET,IAAK,IAAI,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAC/E,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAG9B,SAAS,MAAM,IAAI,EAAE;IACvB;AACF;AAEA,SAAS,8BAAQ;AAEjB,SAAS,gCAAU,MAAM;IACvB,OAAO,OAAO,aAAa,OAAO,OAAO,UAAU;AACrD;AAEA,SAAS,0BAAI,MAAM,EAAE,IAAI,EAAE,QAAQ;IACjC,IAAI,OAAO,SAAS,YAAY,OAAO,0BAAI,QAAQ,MAAM;IACzD,IAAI,CAAC,MAAM,OAAO,CAAC;IACnB,WAAW,2BAAK,YAAY;IAC5B,IAAI,WAAW,KAAK,YAAY,KAAK,aAAa,SAAS,OAAO;IAClE,IAAI,WAAW,KAAK,YAAY,KAAK,aAAa,SAAS,OAAO;IAElE,IAAI,iBAAiB,SAAS;QAC5B,IAAI,CAAC,OAAO,UAAU;IACxB;IAEA,IAAI,gBAAgB,OAAO,kBAAkB,OAAO,eAAe;IAEnE,IAAI,WAAW,SAAS;QACtB,WAAW;QACX,gBAAgB;QAChB,IAAI,CAAC,UAAU,SAAS,KAAK;IAC/B;IAEA,IAAI,gBAAgB,OAAO,kBAAkB,OAAO,eAAe;IAEnE,IAAI,QAAQ,SAAS;QACnB,WAAW;QACX,gBAAgB;QAChB,IAAI,CAAC,UAAU,SAAS,KAAK;IAC/B;IAEA,IAAI,UAAU,SAAS,QAAQ,GAAG;QAChC,SAAS,KAAK,QAAQ;IACxB;IAEA,IAAI,UAAU,SAAS;QACrB,IAAI;QAEJ,IAAI,YAAY,CAAC,eAAe;YAC9B,IAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,eAAe,OAAO,MAAM,IAAI;YACtE,OAAO,SAAS,KAAK,QAAQ;QAC/B;QAEA,IAAI,YAAY,CAAC,eAAe;YAC9B,IAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,eAAe,OAAO,MAAM,IAAI;YACtE,OAAO,SAAS,KAAK,QAAQ;QAC/B;IACF;IAEA,IAAI,YAAY,SAAS;QACvB,OAAO,IAAI,GAAG,UAAU;IAC1B;IAEA,IAAI,gCAAU,SAAS;QACrB,OAAO,GAAG,YAAY;QACtB,OAAO,GAAG,SAAS;QACnB,IAAI,OAAO,KAAK;aAAiB,OAAO,GAAG,WAAW;IACxD,OAAO,IAAI,YAAY,CAAC,OAAO,gBAAgB;QAC7C,iBAAiB;QACjB,OAAO,GAAG,OAAO;QACjB,OAAO,GAAG,SAAS;IACrB;IAEA,OAAO,GAAG,OAAO;IACjB,OAAO,GAAG,UAAU;IACpB,IAAI,KAAK,UAAU,OAAO,OAAO,GAAG,SAAS;IAC7C,OAAO,GAAG,SAAS;IACnB,OAAO;QACL,OAAO,eAAe,YAAY;QAClC,OAAO,eAAe,SAAS;QAC/B,OAAO,eAAe,WAAW;QACjC,IAAI,OAAO,KAAK,OAAO,IAAI,eAAe,UAAU;QACpD,OAAO,eAAe,OAAO;QAC7B,OAAO,eAAe,SAAS;QAC/B,OAAO,eAAe,UAAU;QAChC,OAAO,eAAe,OAAO;QAC7B,OAAO,eAAe,SAAS;QAC/B,OAAO,eAAe,SAAS;IACjC;AACF;AAEA,iBAAiB;;;;;;ACvGjB;AAEA,SAAS,yCAAmB,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG;IAAI,IAAI;QAAE,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC;QAAM,IAAI,QAAQ,KAAK;IAAO,EAAE,OAAO,OAAO;QAAE,OAAO;QAAQ;IAAQ;IAAE,IAAI,KAAK,MAAQ,QAAQ;SAAiB,QAAQ,QAAQ,OAAO,KAAK,OAAO;AAAW;AAExQ,SAAS,wCAAkB,EAAE;IAAI,OAAO;QAAc,IAAI,OAAO,IAAI,EAAE,OAAO;QAAW,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAAI,IAAI,MAAM,GAAG,MAAM,MAAM;YAAO,SAAS,MAAM,KAAK;gBAAI,yCAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,QAAQ;YAAQ;YAAE,SAAS,OAAO,GAAG;gBAAI,yCAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,SAAS;YAAM;YAAE,MAAM;QAAY;IAAI;AAAG;AAEpY,SAAS,8BAAQ,MAAM,EAAE,cAAc;IAAI,IAAI,OAAO,OAAO,KAAK;IAAS,IAAI,OAAO,uBAAuB;QAAE,IAAI,UAAU,OAAO,sBAAsB;QAAS,IAAI,gBAAgB,UAAU,QAAQ,OAAO,SAAU,GAAG;YAAI,OAAO,OAAO,yBAAyB,QAAQ,KAAK;QAAY;QAAI,KAAK,KAAK,MAAM,MAAM;IAAU;IAAE,OAAO;AAAM;AAEpV,SAAS,oCAAc,MAAM;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK;QAAE,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,GAAK,8BAAQ,OAAO,SAAS,MAAM,QAAQ,SAAU,GAAG;YAAI,sCAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;QAAG;aAAW,IAAI,OAAO,2BAA6B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B;aAAmB,8BAAQ,OAAO,SAAS,QAAQ,SAAU,GAAG;YAAI,OAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ;QAAO;IAAM;IAAE,OAAO;AAAQ;AAErhB,SAAS,sCAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,IAAI,OAAO,KAAO,OAAO,eAAe,KAAK,KAAK;QAAE,OAAO;QAAO,YAAY;QAAM,cAAc;QAAM,UAAU;IAAK;SAAa,GAAG,CAAC,IAAI,GAAG;IAAS,OAAO;AAAK;;;AAEhN,IAAI,6CAAuB,aAAiC;AAE5D,SAAS,2BAAK,QAAQ,EAAE,QAAQ,EAAE,IAAI;IACpC,IAAI;IAEJ,IAAI,YAAY,OAAO,SAAS,SAAS,YACvC,WAAW;SACN,IAAI,YAAY,QAAQ,CAAC,OAAO,cAAc,EAAE,WAAW,QAAQ,CAAC,OAAO,cAAc;SAAQ,IAAI,YAAY,QAAQ,CAAC,OAAO,SAAS,EAAE,WAAW,QAAQ,CAAC,OAAO,SAAS;SAAQ,MAAM,IAAI,2CAAqB,YAAY;QAAC;KAAW,EAAE;IAExP,IAAI,WAAW,IAAI,SAAS,oCAAc;QACxC,YAAY;IACd,GAAG,QAAQ,2CAA2C;IACtD,iDAAiD;IAEjD,IAAI,UAAU;IAEd,SAAS,QAAQ;QACf,IAAI,CAAC,SAAS;YACZ,UAAU;YACV;QACF;IACF;IAEA,SAAS;QACP,OAAO,OAAO,MAAM,IAAI,EAAE;IAC5B;IAEA,SAAS;QACP,SAAS,wCAAkB;YACzB,IAAI;gBACF,IAAI,OAAO,MAAM,SAAS,QACtB,QAAQ,KAAK,OACb,OAAO,KAAK;gBAEhB,IAAI,MACF,SAAS,KAAK;qBACT,IAAI,SAAS,KAAM,CAAA,MAAM,KAAI,IAClC;qBAEA,UAAU;YAEd,EAAE,OAAO,KAAK;gBACZ,SAAS,QAAQ;YACnB;QACF;QACA,OAAO,OAAO,MAAM,IAAI,EAAE;IAC5B;IAEA,OAAO;AACT;AAEA,iBAAiB;;;;;;AC/DjB,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,gEAAgE;AAChE,8DAA8D;AAC9D,uEAAuE;AACvE,sEAAsE;AACtE,iDAAiD;AACjD,EAAE;AACF,gEAAgE;AAChE,qEAAqE;AACrE,kEAAkE;AAClE,0DAA0D;AAC1D,EAAE;AACF,yBAAyB;AACzB,EAAE;AACF,wEAAwE;AACxE,sEAAsE;AACtE,mEAAmE;AACnE,gEAAgE;AAChE,oDAAoD;AACpD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,qEAAqE;AACrE,uEAAuE;AACvE,qEAAqE;AACrE,sEAAsE;AACtE,EAAE;AACF,sEAAsE;AACtE,0EAA0E;AAC1E,yEAAyE;AACzE,oEAAoE;AACpE,sEAAsE;AACtE,sEAAsE;AACtE,wEAAwE;AACxE,sEAAsE;AACtE,qEAAqE;AACrE,oEAAoE;AACpE,yCAAyC;AACzC,EAAE;AACF,yEAAyE;AACzE,yEAAyE;AACzE,+DAA+D;AAC/D;AAEA,iBAAiB;;;+CAEb;AAAJ,IACI,mDAA6B,yCAAe,4BAC5C,8CAAwB,yCAAe,uBACvC,2DAAqC,yCAAe,oCACpD,oDAA8B,yCAAe;;;;AAIjD,yBAAoB,iCAAW;AAE/B,SAAS,qCAAe,EAAE,EAAE,IAAI;IAC9B,IAAI,KAAK,IAAI,CAAC;IACd,GAAG,eAAe;IAClB,IAAI,KAAK,GAAG;IAEZ,IAAI,OAAO,MACT,OAAO,IAAI,CAAC,KAAK,SAAS,IAAI;IAGhC,GAAG,aAAa;IAChB,GAAG,UAAU;IACb,IAAI,QAAQ,MACV,IAAI,CAAC,KAAK;IACZ,GAAG;IACH,IAAI,KAAK,IAAI,CAAC;IACd,GAAG,UAAU;IAEb,IAAI,GAAG,gBAAgB,GAAG,SAAS,GAAG,eACpC,IAAI,CAAC,MAAM,GAAG;AAElB;AAEA,SAAS,gCAAU,OAAO;IACxB,IAAI,CAAE,CAAA,IAAI,YAAY,+BAAQ,GAAI,OAAO,IAAI,gCAAU;IACvD,OAAO,KAAK,IAAI,EAAE;IAClB,IAAI,CAAC,kBAAkB;QACrB,gBAAgB,qCAAe,KAAK,IAAI;QACxC,eAAe;QACf,cAAc;QACd,SAAS;QACT,YAAY;QACZ,eAAe;IACjB,GAAG,kEAAkE;IAErE,IAAI,CAAC,eAAe,eAAe,MAAM,kEAAkE;IAC3G,gEAAgE;IAChE,mBAAmB;IAEnB,IAAI,CAAC,eAAe,OAAO;IAE3B,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,cAAc,YAAY,IAAI,CAAC,aAAa,QAAQ;QACvE,IAAI,OAAO,QAAQ,UAAU,YAAY,IAAI,CAAC,SAAS,QAAQ;IACjE,EAAE,sEAAsE;IAGxE,IAAI,CAAC,GAAG,aAAa;AACvB;AAEA,SAAS;IACP,IAAI,QAAQ,IAAI;IAEhB,IAAI,OAAO,IAAI,CAAC,WAAW,cAAc,CAAC,IAAI,CAAC,eAAe,WAC5D,IAAI,CAAC,OAAO,SAAU,EAAE,EAAE,IAAI;QAC5B,2BAAK,OAAO,IAAI;IAClB;SAEA,2BAAK,IAAI,EAAE,MAAM;AAErB;AAEA,gCAAU,UAAU,OAAO,SAAU,KAAK,EAAE,QAAQ;IAClD,IAAI,CAAC,gBAAgB,gBAAgB;IACrC,OAAO,OAAO,UAAU,KAAK,KAAK,IAAI,EAAE,OAAO;AACjD,GAAG,uCAAuC;AAC1C,oDAAoD;AACpD,6BAA6B;AAC7B,EAAE;AACF,yDAAyD;AACzD,iEAAiE;AACjE,EAAE;AACF,iEAAiE;AACjE,sEAAsE;AACtE,wDAAwD;AAGxD,gCAAU,UAAU,aAAa,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC5D,GAAG,IAAI,iDAA2B;AACpC;AAEA,gCAAU,UAAU,SAAS,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACxD,IAAI,KAAK,IAAI,CAAC;IACd,GAAG,UAAU;IACb,GAAG,aAAa;IAChB,GAAG,gBAAgB;IAEnB,IAAI,CAAC,GAAG,cAAc;QACpB,IAAI,KAAK,IAAI,CAAC;QACd,IAAI,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,SAAS,GAAG,eAAe,IAAI,CAAC,MAAM,GAAG;IACzF;AACF,GAAG,yCAAyC;AAC5C,gCAAgC;AAChC,iEAAiE;AAGjE,gCAAU,UAAU,QAAQ,SAAU,CAAC;IACrC,IAAI,KAAK,IAAI,CAAC;IAEd,IAAI,GAAG,eAAe,QAAQ,CAAC,GAAG,cAAc;QAC9C,GAAG,eAAe;QAElB,IAAI,CAAC,WAAW,GAAG,YAAY,GAAG,eAAe,GAAG;IACtD,OACE,gEAAgE;IAChE,mDAAmD;IACnD,GAAG,gBAAgB;AAEvB;AAEA,gCAAU,UAAU,WAAW,SAAU,GAAG,EAAE,EAAE;IAC9C,OAAO,UAAU,SAAS,KAAK,IAAI,EAAE,KAAK,SAAU,IAAI;QACtD,GAAG;IACL;AACF;AAEA,SAAS,2BAAK,MAAM,EAAE,EAAE,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,OAAO,KAAK,SAAS;IACpC,IAAI,QAAQ,MACV,OAAO,KAAK,OAAO,yDAAyD;IAC9E,0DAA0D;IAC1D,0CAA0C;IAE1C,IAAI,OAAO,eAAe,QAAQ,MAAM,IAAI;IAC5C,IAAI,OAAO,gBAAgB,cAAc,MAAM,IAAI;IACnD,OAAO,OAAO,KAAK;AACrB;;;;;ACxMA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,wBAAwB;AACxB,4DAA4D;AAC5D,yCAAyC;AACzC;AAEA,iBAAiB;;;;AAIjB,yBAAoB,mCAAa;AAEjC,SAAS,kCAAY,OAAO;IAC1B,IAAI,CAAE,CAAA,IAAI,YAAY,iCAAU,GAAI,OAAO,IAAI,kCAAY;IAC3D,OAAU,KAAK,IAAI,EAAE;AACvB;AAEA,kCAAY,UAAU,aAAa,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,GAAG,MAAM;AACX;;;;;ACtCA,qDAAqD;AACrD,yDAAyD;AACzD;AAEA,IAAI;AAEJ,SAAS,2BAAK,QAAQ;IACpB,IAAI,SAAS;IACb,OAAO;QACL,IAAI,QAAQ;QACZ,SAAS;QACT,SAAS,MAAM,KAAK,GAAG;IACzB;AACF;;;+CAEI;AAAJ,IACI,yCAAmB,yCAAe,kBAClC,6CAAuB,yCAAe;AAE1C,SAAS,2BAAK,GAAG;IACf,wDAAwD;IACxD,IAAI,KAAK,MAAM;AACjB;AAEA,SAAS,gCAAU,MAAM;IACvB,OAAO,OAAO,aAAa,OAAO,OAAO,UAAU;AACrD;;AAEA,SAAS,gCAAU,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;IACnD,WAAW,2BAAK;IAChB,IAAI,SAAS;IACb,OAAO,GAAG,SAAS;QACjB,SAAS;IACX;IACA,IAAI,8BAAQ,WAAW,4BAAM;IAC7B,0BAAI,QAAQ;QACV,UAAU;QACV,UAAU;IACZ,GAAG,SAAU,GAAG;QACd,IAAI,KAAK,OAAO,SAAS;QACzB,SAAS;QACT;IACF;IACA,IAAI,YAAY;IAChB,OAAO,SAAU,GAAG;QAClB,IAAI,QAAQ;QACZ,IAAI,WAAW;QACf,YAAY,MAAM,wDAAwD;QAE1E,IAAI,gCAAU,SAAS,OAAO,OAAO;QACrC,IAAI,OAAO,OAAO,YAAY,YAAY,OAAO,OAAO;QACxD,SAAS,OAAO,IAAI,2CAAqB;IAC3C;AACF;AAEA,SAAS,2BAAK,EAAE;IACd;AACF;AAEA,SAAS,2BAAK,IAAI,EAAE,EAAE;IACpB,OAAO,KAAK,KAAK;AACnB;AAEA,SAAS,kCAAY,OAAO;IAC1B,IAAI,CAAC,QAAQ,QAAQ,OAAO;IAC5B,IAAI,OAAO,OAAO,CAAC,QAAQ,SAAS,EAAE,KAAK,YAAY,OAAO;IAC9D,OAAO,QAAQ;AACjB;AAEA,SAAS;IACP,IAAK,IAAI,OAAO,UAAU,QAAQ,UAAU,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAClF,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAGjC,IAAI,WAAW,kCAAY;IAC3B,IAAI,MAAM,QAAQ,OAAO,CAAC,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE;IAEnD,IAAI,QAAQ,SAAS,GACnB,MAAM,IAAI,uCAAiB;IAG7B,IAAI;IACJ,IAAI,WAAW,QAAQ,IAAI,SAAU,MAAM,EAAE,CAAC;QAC5C,IAAI,UAAU,IAAI,QAAQ,SAAS;QACnC,IAAI,UAAU,IAAI;QAClB,OAAO,gCAAU,QAAQ,SAAS,SAAS,SAAU,GAAG;YACtD,IAAI,CAAC,OAAO,QAAQ;YACpB,IAAI,KAAK,SAAS,QAAQ;YAC1B,IAAI,SAAS;YACb,SAAS,QAAQ;YACjB,SAAS;QACX;IACF;IACA,OAAO,QAAQ,OAAO;AACxB;AAEA,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9DjB,mDAAA;AACA,MAAMmB,OAAO;AACb,MAAMC,SAAS;AACf,MAAMC,UACJF,OACC,CAAA,AACDG,CAAAA,GAAAA,QAAQC,IAAIC,iBAAAA,IACRJ,SACA,CAAA;AAEN,sGAAA;AACA,gGAAA;AACA,MAAMK,QAAQ,IAAIC;AAClB,MAAMC,WAAW,IAAID;AACrB,MAAME,qBAAqB,IAAIF;AAOxB,MAAMG;IAMXC,YACEC,EAAc,EACdC,WAAqB,EACrBC,SAA6B,CAC7B;QACA,IAAI,CAACF,KAAKA;QACV,IAAI,CAACC,cAAcA;QACnB,IAAI,CAACC,YAAYA;IACnB;IAEAC,kBAAgC;QAC9B,OAAO,IAAIjB,CAAAA,GAAAA,0CAAAA,EAAa,IAAI,CAACe,aAAa;YACxCD,IACE,IAAI,CAACA,cAAczB,CAAAA,GAAAA,sBAAAA,KAAUgB,QAAQa,SAASC,OAAO,OACjDC,YACA;gBACEC,cAAc7B,CAAAA,OAAQ,IAAI,CAACsB,GAAGQ,aAAa9B;gBAC3C+B,MAAM/B,CAAAA,OAAQ,IAAI,CAACsB,GAAGU,aAAahC;gBACnCiC,QAAQjC,CAAAA,OAAQ,IAAI,CAACsB,GAAGY,SAASlC,MAAMiC;gBACvCE,OAAOnC,CAAAA,OAAQ,IAAI,CAACsB,GAAGY,SAASlC,MAAMoC;YACxC;YACNC,MAAM;YACNC,SAAS1B;YACT2B,gBAAgB;YAChBC,mBACE3B,QAAQa,SAASC,OAAO,OACpB,CAACc,SAAQC;gBACP,2BAAA;gBACA,IAAIf,MAAM5B,CAAAA,GAAAA,uCAAAA,EAAO4C,WAAW3C,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;gBAEzC,OAAOf,IAAIkB,qBACT,kDAAA;gBACAJ,UAAS,KACTC;YAEJ,IACAd;QACR;IACF;IAEA,OAAOkB,YAAYC,IAAS,EAAsB;QAChD,OAAO,IAAI3B,mBAAmB2B,KAAKzB,IAAIyB,KAAKxB,aAAawB,KAAKvB;IAChE;IAEAwB,YAKG;QACD,OAAO;YACLC,OAAO;YACP3B,IAAI,IAAI,CAACA;YACTC,aAAa,IAAI,CAACA;YAClBC,WAAW,IAAI,CAACA;QAClB;IACF;IAEA,MAAM0B,QACJC,IAAyB,EACzBT,IAAc,EACdK,IAIE,EACY;QACd,IAAI,EAACK,QAAQ,EAAEC,IAAAA,EAAK,GAAG,MAAM,IAAI,CAACC,QAAQH,MAAMT,MAAMK;QACtD,IAAIM,SAAS,GAAG;YACdnD,CAAAA,GAAAA,6CAAAA,EAAOqD,KAAK;gBACVC,SAAS;gBACTC,QAAQ;gBACRC,YAAY;oBACV;wBACEC,UAAUP;wBACVQ,gBAAgB,EAAhBA;oBACF;iBAAA;YAEJ;YAEA,4DAAA;YACA,IAAI/C,QAAQgD,aAAa,WAAW7D,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWV,WAClDA,WAAW3C,CAAAA,GAAAA,wBAAAA,EAAc2C;YAG3B,aAAA;YACA,OAAO,MAAM,CAACA;QAChB;QACA,OAAO,IAAI,CAACW,KAAKX,UAAUV;IAC7B;IAEAsB,YAAYb,IAAyB,EAAET,IAAc,EAAO;QAC1D,IAAI,EAACU,QAAAA,EAAS,GAAG,IAAI,CAACa,YAAYd,MAAMT;QACxC,OAAO,IAAI,CAACqB,KAAKX,UAAUV;IAC7B;IAEAqB,KAAKJ,QAAkB,EAAEjB,IAAc,EAAO;QAC5C,IAAI,CAAC1C,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWH,WACnB,sBAAA;QACA,aAAA;QACA,OAAOT,QAAQS;QAGjB,2BAAA;QACA,MAAMO,eAAenE,CAAAA,GAAAA,uCAAAA,EAAOoE,MAAM,CAACR,SAAS;QAC5C,IAAIO,iBAAiBtC,WACnB,OAAOsC,aAAaE;QAGtB,aAAA;QACA,IAAIC,IAAI,IAAItE,CAAAA,GAAAA,uCAAAA,EAAO4D,UAAU5D,CAAAA,GAAAA,uCAAAA,EAAOoE,MAAM,CAACzB,KAAK,IAAID,OAAO6B;QAC3D,2BAAA;QACAvE,CAAAA,GAAAA,uCAAAA,EAAOoE,MAAM,CAACR,SAAS,GAAGU;QAE1B,kEAAA;QACAA,EAAEnB,UAAUqB,CAAAA;YACV,OAAO,IAAI,CAACP,YAAYO,IAAIZ;QAC9B;QAEA,8EAAA;QACA,IAAI,EAAC3B,YAAY,EAAEE,QAAAA,EAAS,GAAGpC,CAAAA,GAAAA,mCAAAA;QAC/B,aAAA;QACAA,CAAAA,GAAAA,mCAAAA,EAASkC,eAAe,CAACwC,UAAUC;YACjC,OAAO,IAAI,CAACnD,GAAGU,aAAawC,UAAUC;QACxC;QAEA,aAAA;QACA3E,CAAAA,GAAAA,mCAAAA,EAASoC,WAAWsC,CAAAA;YAClB,OAAO,IAAI,CAAClD,GAAGY,SAASsC;QAC1B;QAEA,IAAI;YACFH,EAAEN,KAAKJ;QACT,EAAE,OAAOe,KAAK;YACZ,2BAAA;YACA,OAAO3E,CAAAA,GAAAA,uCAAAA,EAAOoE,MAAM,CAACR,SAAS;YAC9B,MAAMe;QACR,SAAU;YACR,aAAA;YACA5E,CAAAA,GAAAA,mCAAAA,EAASkC,eAAeA;YACxB,aAAA;YACAlC,CAAAA,GAAAA,mCAAAA,EAASoC,WAAWA;QACtB;QAEA,OAAOmC,EAAED;IACX;IAEA,MAAMd,QACJiB,EAAuB,EACvB7B,IAAc,EACdiC,OAIE,EACsB;QACxB,IAAIC,UAAU5E,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;QAC3B,IAAImC,MAAMD,UAAU,MAAML;QAC1B,IAAInB,WAAWpC,MAAM8D,IAAID;QACzB,IAAI,CAACzB,UAAU;YACb,IAAI,CAACD,KAAK,GAAG/C,CAAAA,GAAAA,iCAAAA,EAAemE;YAC5B,IAAI;gBACFnB,WAAW,IAAI,CAAC2B,gBAAgBR,IAAI7B;YACtC,EAAE,OAAOsC,GAAG;gBACV,IACEA,EAAEC,SAAS,sBACXN,CAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASO,iBAAAA,MAAsB,MAC/B;oBACA,IACEF,EAAEC,SAAS,sBACXN,CAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASO,iBAAAA,MAAsB,MAC/B;wBACA,IAAIR,MAAM,IAAIlF,CAAAA,GAAAA,iDAAAA,EAAoB;4BAChC2F,YAAY;gCACV3B,SAAS9D,CAAAA,GAAAA,sCAAAA,EAAesF,EAAExB;gCAC1B4B,OAAO;oCACL;iCADFA;4BAGF;wBACF;wBACA,2CAAA;wBACAV,IAAIO,OAAO;wBACX,MAAMP;oBACR,OACE,MAAMM;gBAEV;gBAEA,IAAIK,YAAY,MAAMhF,CAAAA,GAAAA,sCAAAA,EACpB,IAAI,CAACiB,IACL6B,MACAT,MACA,IAAI,CAACnB;gBAGP,IAAI8D,aAAa,MAAM;oBACrB,IAAI,CAACC,WAAWf,IAAI7B;wBAETiC;oBADX,MAAM,IAAI,CAACY,QAAQ;wBAAC;kCAACpC;4BAAMqC,OAAOb,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASa;wBAAK;qBAAE,EAAE9C,MAAM;wBACxD+C,SAASd,CAAAA,mBAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASc,qBAATd,8BAAAA,mBAAoB;oBAC/B;oBAEA,OAAO,IAAI,CAACrB,QAAQiB,IAAI7B,MAAM;wBAC5B,GAAGiC,OAAO;wBACVO,mBAAmB;oBACrB;gBACF;gBAEA,MAAM,IAAI1F,CAAAA,GAAAA,iDAAAA,EAAoB;oBAC5B2F,YAAYE,UAAUK,OAAOC,IAAIC,CAAAA,QAAU,CAAA;4BACzCpC,SAAS5D,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,uBAAA,EAAyBuD,KAAK,qEAAA,CAAsE;4BAChHM,QAAQ;4BACRC,YAAY;gCACV;oCACEC,UAAU0B,UAAU1B;oCACpBkC,UAAU;oCACVZ,MAAMI,UAAUS;oCAChBlC,gBAAgBjE,CAAAA,GAAAA,kDAAAA,EAA2B0F,UAAUS,MAAM;wCACzD;4CACEjB,KAAM,CAAA,CAAA,EAAGe,MAAM,CAAA,EAAGnG,CAAAA,GAAAA,8CAAAA,EAAuB0D,MAAM,CAAC;4CAChDE,MAAM;4CACNG,SAAS;wCACX;qCACD;gCACH;6BAAA;wBAEJ,CAAA;gBACF;YACF;YAEA,IAAIgC,QAAQb,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASa;YACrB,IAAIA,SAAS,MAAM;gBACjB,IAAIjF,MAAM6C,SAAS7C;gBACnB,IAAIA,OAAO,QAAQ,CAACN,CAAAA,GAAAA,uCAAAA,EAAO8F,UAAUxF,IAAIyF,SAASR,QAAQ;oBACxD,IAAIH,YAAY,MAAMhF,CAAAA,GAAAA,sCAAAA,EACpB,IAAI,CAACiB,IACL6B,MACAT,MACA,IAAI,CAACnB;oBAGP,IAAI8D,aAAa,QAAQV,CAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASO,iBAAAA,MAAsB,MAAM;wBAC5D,IAAI,CAACI,WAAWf,IAAI7B;wBACpB,MAAM,IAAI,CAAC6C,QAAQ;4BAAC;sCAACpC;uCAAMqC;4BAAK;yBAAE,EAAE9C;wBACpC,OAAO,IAAI,CAACY,QAAQiB,IAAI7B,MAAM;4BAC5B,GAAGiC,OAAO;4BACVO,mBAAmB;wBACrB;oBACF,OAAO,IAAIG,aAAa,MACtB,MAAM,IAAI7F,CAAAA,GAAAA,iDAAAA,EAAoB;wBAC5B2F,YAAY;4BACV3B,SAAS5D,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,uBAAA,EAAyBuD,KAAK,aAAA,EAAeqC,MAAM,CAAA,CAAE;4BACjE/B,QAAQ;4BACRC,YAAY;gCACV;oCACEC,UAAU0B,UAAU1B;oCACpBkC,UAAU;oCACVZ,MAAMI,UAAUS;oCAChBlC,gBAAgBjE,CAAAA,GAAAA,kDAAAA,EACd0F,UAAUS,MACVT,UAAUK,OAAOC,IAAIC,CAAAA,QAAU,CAAA;4CAC7Bf,KAAM,CAAA,CAAA,EAAGe,MAAM,CAAA,EAAGnG,CAAAA,GAAAA,8CAAAA,EAAuB0D,MAAM,CAAC;4CAChDE,MAAM;4CACNG,SAAS;wCACX,CAAA;gCAEJ;6BAAA;wBAEJ;oBACF;oBAGF,IAAIwC,UAAUzF,gBAAAA,iBAAAA,KAAAA,IAAAA,IAAKyF;oBACnB,IAAIxC,UAAU5D,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,2BAAA,EAA6BuD,KAAK,iBAAA,EAAmBqC,MAAM,CAAA,CAAE;oBAC9E,IAAIQ,WAAW,MACbxC,WAAW5D,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,OAAA,EAASoG,QAAQ,CAAA,CAAE;oBAGnC,MAAM,IAAIxG,CAAAA,GAAAA,iDAAAA,EAAoB;wBAC5B2F,YAAY;qCACV3B;4BACA4B,OAAO;gCACL;6BADFA;wBAGF;oBACF;gBACF;YACF;YAEApE,MAAMiF,IAAIpB,KAAKzB;YACfjC,mBAAmB+E;YAEnB,qDAAA;YACA,sGAAA;YACA,2HAAA;YACA,IAAI,CAAClG,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWX,OAAO;gBAC1B,IAAIgD,iBAAiBjF,SAAS4D,IAAIpC;gBAClC,IAAI,CAACyD,gBAAgB;oBACnBA,iBAAiB,IAAIC;oBACrBlF,SAAS+E,IAAIvD,MAAMyD;gBACrB;gBAEAA,eAAeE,IAAIlD;YACrB;QACF;QAEA,OAAOC;IACT;IAEAa,YAAYd,IAAyB,EAAET,IAAc,EAAiB;QACpE,IAAIkC,UAAU5E,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;QAC3B,IAAImC,MAAMD,UAAU,MAAMzB;QAC1B,IAAIC,WAAWpC,MAAM8D,IAAID;QACzB,IAAI,CAACzB,UAAU;YACbA,WAAW,IAAI,CAAC2B,gBAAgB5B,MAAMT;YACtC1B,MAAMiF,IAAIpB,KAAKzB;YACfjC,mBAAmB+E;YAEnB,IAAI,CAAClG,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWX,OAAO;gBAC1B,IAAIgD,iBAAiBjF,SAAS4D,IAAIpC;gBAClC,IAAI,CAACyD,gBAAgB;oBACnBA,iBAAiB,IAAIC;oBACrBlF,SAAS+E,IAAIvD,MAAMyD;gBACrB;gBAEAA,eAAeE,IAAIlD;YACrB;QACF;QAEA,OAAOC;IACT;IAEA,MAAMmC,QACJe,OAA6B,EAC7B5D,IAAc,EACdK,IAAqB,EACrB;QACA,MAAMzC,CAAAA,GAAAA,qBAAAA,EAAe,IAAI,CAACgB,IAAI,IAAI,EAAEgF,SAAS5D,MAAM,IAAI,CAACnB,aAAa;YACnEgF,kBAAkB,IAAI,CAAC/E;YACvB,GAAGuB,IAAH;QACF;IACF;IAEAyD,iBAAiBrD,IAAyB,EAAET,IAAc,EAAiB;QACzE,IAAIkC,UAAU5E,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;QAE3B,IAAIU,WAAWpC,MAAM8D,IADNF,UAAU,MAAMzB;QAG/B,IAAIC,YAAYpD,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWV,SAASA,WAAW;YAClD,IAAIqD,SAAStF,mBAAmB2D,IAAI1B,SAASA;YAC7C,IAAIqD,UAAU,MACZ,OAAOA;YAGT,IAAIC,MAAM;gBACRC,wBAAwB,EAAE;gBAC1BC,wBAAwB,IAAIR;gBAC5BS,qBAAqB;YACvB;YAEA,IAAIC,OAAO,IAAIV;YACf,IAAIW,SAASA,CAAC5D,MAAMT;gBAClB,IAAIkC,UAAU5E,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;gBAC3B,IAAImC,MAAMD,UAAU,MAAMzB;gBAC1B,IAAI2D,KAAKE,IAAInC,MACX;gBAGFiC,KAAKT,IAAIxB;gBACT,IAAIzB,WAAWpC,MAAM8D,IAAID;gBACzB,IAAI,CAACzB,YAAY,CAACpD,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWV,SAASA,WACzC;gBAGFsD,IAAIC,uBAAuBM,QAAQ7D,SAASuD;gBAC5CD,IAAIE,uBAAuBP,IAAIjD,SAASA;gBAExC,KAAK,IAAI8D,QAAQ9D,SAASwD,uBACxBF,IAAIE,uBAAuBP,IAAIa;gBAGjC,IAAIf,iBAAiBjF,SAAS4D,IAAI1B,SAASA;gBAC3C,IAAI+C,gBACF,KAAK,IAAIgB,aAAahB,eACpBY,OAAOI,WAAW/D,SAASA;YAGjC;YAEA2D,OAAO5D,MAAMT;YAEb,2FAAA;YACA,uGAAA;YACA,IAAIU,SAASC,SAAS,GAAG;oBAQvBqD;gBAPA,IAAIU,gBAAgB,IAAI,CAACC,SAASb,iBAAiBpD,SAASA;gBAC5DgE,cAAcR,uBAAuBU,QAAQC,CAAAA,IAC3Cb,IAAIE,uBAAuBP,IAAIkB;gBAEjCH,cAAcT,uBAAuBW,QAAQC,CAAAA,IAC3Cb,IAAIC,uBAAuBM,KAAKM;gBAElCb,CAAAA,OAAAA,KAAIG,wBAAJH,KAAIG,sBAAwBO,cAAcP;gBAC1C,IAAIH,IAAIG,qBACN3G,CAAAA,GAAAA,6CAAAA,EAAOqD,KAAK;oBACVC,SAAS5D,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,EAAEI,CAAAA,GAAAA,qCAAAA,EAAKwH,SACjB,IAAI,CAACjG,aACL6B,SAASA,UACT,4HAAA,CAA6H;oBAC/HK,QAAQ;gBACV;YAEJ;YAEAtC,mBAAmB8E,IAAI7C,SAASA,UAAUsD;YAC1C,OAAOA;QACT;QAEA,OAAO;YACLC,wBAAwB,EAAE;YAC1BC,wBAAwB,IAAIR;YAC5BS,qBAAqB;QACvB;IACF;IAEAvB,WAAWnC,IAAyB,EAAET,IAAc,EAAE;QACpD,IAAIoE,OAAO,IAAIV;QAEf,IAAId,aAAaA,CAACnC,MAAMT;YACtB,IAAIkC,UAAU5E,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;YAC3B,IAAImC,MAAMD,UAAU,MAAMzB;YAC1B,IAAI2D,KAAKE,IAAInC,MACX;YAGFiC,KAAKT,IAAIxB;YACT,IAAIzB,WAAWpC,MAAM8D,IAAID;YACzB,IAAI,CAACzB,YAAY,CAACpD,CAAAA,GAAAA,qCAAAA,EAAK8D,WAAWV,SAASA,WACzC;YAGFjC,mBAAmBsG,OAAOrE,SAASA;YAEnC,aAAA;YACA,IAAIX,UAAS1C,CAAAA,GAAAA,uCAAAA,EAAOoE,MAAM,CAACf,SAASA,SAAS;YAC7C,IAAIX,SACF,aAAA;YACA,OAAO1C,CAAAA,GAAAA,uCAAAA,EAAOoE,MAAM,CAACf,SAASA,SAAS;YAGzC,IAAI+C,iBAAiBjF,SAAS4D,IAAI1B,SAASA;YAC3C,IAAI+C,gBACF,KAAK,IAAIgB,aAAahB,eACpBb,WAAW6B,WAAW/D,SAASA;YAInClC,SAASuG,OAAOrE,SAASA;YACzBpC,MAAMyG,OAAO5C;QACf;QAEAS,WAAWnC,MAAMT;QACjB,IAAI,CAAC2E,WAAW,IAAI,CAAC5F;IACvB;IAEAsD,gBAAgB5B,IAAY,EAAET,IAAY,EAAiB;QACzD,IAAI,IAAI,CAAC2E,YAAY,MACnB,IAAI,CAACA,WAAW,IAAI,CAAC5F;QAGvB,IAAIiF,MAAM,IAAI,CAACW,SAAS/D,QAAQ;YAC9BkB,UAAUrB;YACVuE,eAAe;YACfpD,QAAQ5B;QACV;QAEA,gDAAA;QACA,8DAAA;QACA,IAAI7B,QAAQa,SAASC,OAAO,QAAQ+E,IAAIE,wBAAwB;YAC9D,2BAAA;YACA,IAAIjF,MAAM5B,CAAAA,GAAAA,uCAAAA,EAAO4C,WAAW3C,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQF;YACzCgE,IAAIE,uBAAuBK,KAAKtF,IAAIkB,qBAAqB,UAAU;QACrE;QAEA,IAAI6D,IAAIiB,OAAO;YACb,IAAI3C,IAAI,IAAI4C,MAAO,CAAA,0BAAA,EAA4BzE,KAAK,QAAA,EAAUT,KAAK,CAAA,CAAE;YACrE,aAAA;YACAsC,EAAEC,OAAO;YACT,MAAMD;QACR;QACA,IAAI6C;QACJ,OAAQnB,IAAIoB,WAAWzE;YACrB,KAAK;gBACHwE,SAASA;oBACP,IAAIE,UAAU,IAAI,CAACzG,GAAG0G,iBACpB;wBAAC;qBAAe,EAChB7H,CAAAA,GAAAA,6CAAAA,EAAWuG,IAAIoB,WAAWG,QAC1B,IAAI,CAAC1G;oBAEP,OAAOwG,UACHG,KAAKC,MAAM,IAAI,CAAC7G,GAAGU,aAAa+F,SAAS,WACzC;gBACN;YACF,cAAA;YACA,KAAK;gBACH,OAAO;oBACL3E,UAAUsD,IAAIoB,WAAWG;oBACzBrB,wBAAwB,IAAIR,IAAIM,IAAIE;oBACpCD,wBAAwBD,IAAIC;oBAC5BtD,MAAMqD,IAAI0B;oBACV,IAAI7H,OAAM;wBACR,OAAOsH;oBACT;gBACF;YACF;gBACE,MAAM,IAAID,MAAM;QAA2B;IAEjD;AACF;AAEArI,CAAAA,GAAAA,2CAAAA,EACG,CAAA,EAAEgB,CAAAA,GAAAA,6CAAAA,EAAIyF,QAAQ,mBAAA,CAAoB,EACnC5E;;;;ACrkBF;AAEA,SAAS,iCAAW,CAAC,EAAE,OAAO;IAC5B,IAAI,KAAK,MACP,OAAO;IAET,IAAI,QAAQ,IAAI,MAAM,YAAY,YAAY,UAAU,oBAAoB;IAC5E,MAAM,cAAc,GAAG,qCAAqC;IAC5D,MAAM;AACR;AAEA,iBAAiB;AACjB,eAAe,UAAU;AAEzB,OAAO,eAAe,gBAAgB,cAAc;IAAC,OAAO;AAAI;;;;;;;;;;;;;;;ACFzD,MAAMmH,4CAIXE,CAAAA,GAAAA,qBAAAA,EAAUD,CAAAA,GAAAA,yBAAAA;AAEL,SAASE,0CACdC,aAA4B;IAE5B,OAAOA,cAAcnD,SAAS,OAC1B;QAACmD,cAAcxF;QAAMwF,cAAcnD;KAAM,CAACoD,KAAK,OAC/CD,cAAcxF;AACpB;AAEO,SAAS0F,0CAAgCC,aAE9C;IACA,OAAOC,OAAOzG,QAAQwG,eAAenD,IAAI,CAAC,CAACxC,MAAMqC,MAAM;QACrD6C,CAAAA,GAAAA,uCAAAA,EAAU,OAAO7C,UAAU;QAC3B,OAAO;kBACLrC;mBACAqC;QACF;IACF;AACF;AAEO,eAAenF,0CACpBiB,EAAc,EACd6B,IAAY,EACZ6F,KAAe,EACfzH,WAAqB;IAErB,IAAIwG,UAAU,MAAMO,CAAAA,GAAAA,gCAAAA,EAAchH,IAAI0H,OAAO;QAAC;KAAe,EAAEzH;IAC/D,IAAIwG,WAAW,MACb;IAGF,IAAIkB,SAAS,MAAM3H,GAAG4H,SAASnB,SAAS;IACxC,IAAIxH;IACJ,IAAI;QACFA,MAAM2H,KAAKC,MAAMc;IACnB,EAAE,OAAOjE,GAAG;QACV,kBAAA;QACA,MAAM,IAAIxF,CAAAA,GAAAA,iDAAAA,EAAoB;YAC5B2F,YAAY;gBACV3B,SAAS;gBACTC,QAAQ;YACV;QACF;IACF;IAEA,IAAI,OAAOlD,QAAQ,YAAYA,OAAO,MACpC,kBAAA;IACA,MAAM,IAAIf,CAAAA,GAAAA,iDAAAA,EAAoB;QAC5B2F,YAAY;YACV3B,SAAS;YACTC,QAAQ;QACV;IACF;IAGF,IAAIiC,SAAS,EAAE;IACf,KAAK,IAAIE,SAAS;QAAC;QAAgB;QAAmB;KAAmB,CACvE,IACE,OAAOrF,GAAG,CAACqF,MAAM,KAAK,YACtBrF,GAAG,CAACqF,MAAM,IAAI,QACdrF,GAAG,CAACqF,MAAM,CAACzC,KAAK,IAAI,MAEpBuC,OAAOuB,KAAKrB;IAIhB,IAAIF,OAAOyD,SAAS,GAClB,OAAO;QACLxF,UAAUoE;QACVjC,MAAMmD;gBACNvD;IACF;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DA,eAAeH,8BACbjE,EAAc,EACdqI,cAA8B,EAC9BrD,OAA6B,EAC7B5D,IAAc,EACdnB,WAAqB,EACrBoD,UAA0B,CAAC,CAAC;IAE5B,IAAI,gBAACiF,eAAe,eAAMnE,UAAU,wBAAMc,gBAAAA,EAAiB,GAAG5B;IAC9D,IAAIkF,cAAcvD,QAAQX,IAAItB,CAAAA,IAAKA,EAAElB,MAAMyF,KAAK;IAEhD1I,CAAAA,GAAAA,6CAAAA,EAAO4J,SAAU,CAAA,WAAA,EAAaD,YAAY,GAAA,CAAI;IAE9C,IAAIE,cAAc,MAAMzB,CAAAA,GAAAA,gCAAAA,EACtBhH,IACAoB,MACA;QAAC;KAAe,EAChBnB;IAEF,IAAIyI,MAAMD,cAAc/J,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQmH,eAAezI,GAAG0I;IAEvD,IAAI,CAACzD,kBACHA,mBAAmB,MAAM0D,gDAA0B3I,IAAIoB,MAAMnB;IAG/D,IAAI;QACF,MAAMgF,iBAAiBhB,QAAQ;qBAC7Be;qBACAb;iBACAuE;YACAE,aAAaH;gBACbzI;QACF;IACF,EAAE,OAAOoD,KAAK;QACZ,MAAM,IAAIkD,MAAO,CAAA,kBAAA,EAAoBiC,YAAY,EAAA,EAAInF,IAAIlB,QAAQ,CAAC;IACpE;IAEA,IAAIoG,cACF,MAAMO,QAAQC,IACZ9D,QAAQX,IAAItB,CAAAA,IACVgG,8CACE/I,IACAqI,gBACAtF,GACA3B,MACAnB,aACAoD;AAKV;AAEA,eAAe0F,8CACb/I,EAAc,EACdqI,cAA8B,EAC9BlH,MAAqB,EACrBC,IAAc,EACdnB,WAAqB,EACrBoD,OAAO;IAEP,MAAM,YAACvB,QAAAA,EAAS,GAAG,MAAMuG,eAAerG,QAAQb,OAAOU,MAAMT;IAC7D,MAAM4H,YAAyBnK,CAAAA,GAAAA,6CAAAA,EAC7B,MAAMiJ,CAAAA,GAAAA,6BAAAA,EAAW9H,IAAI8B,UAAU;QAAC;KAAe,EAAE7B,cACjDgJ;IACF,MAAMC,QAAQF,UAAUG,oBAAoB,CAAC;IAE7C,IAAInE,UAAgC,EAAE;IACtC,KAAK,IAAI,CAACnD,MAAMqC,MAAM,IAAIuD,OAAOzG,QAAQkI,OAAQ;QAC/CnC,CAAAA,GAAAA,uCAAAA,EAAU,OAAO7C,UAAU;QAE3B,IAAIH,YAAY,MAAMhF,CAAAA,GAAAA,sCAAAA,EACpBiB,IACA6B,MACAT,MACAnB;QAEF,IAAI8D,WAAW;YACb,IAAI,OAAC9E,GAAAA,EAAI,GAAG,MAAMoJ,eAAerG,QAAQH,MAAMT;YAC/C2F,CAAAA,GAAAA,uCAAAA,EAAU9H;YACV,IAAI,CAACN,CAAAA,GAAAA,uCAAAA,EAAO8F,UAAUxF,IAAIyF,SAASR,QACjC,MAAM,IAAIhG,CAAAA,GAAAA,iDAAAA,EAAoB;gBAC5B2F,YAAY;oBACV3B,SAAS5D,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,uCAAA,EAAyCuD,KAAK,OAAA,EAASV,OAAOU,KAAK,qBAAA,EAAuB5C,IAAIyF,QAAQ,sBAAA,EAAwBR,MAAM,CAAC;oBACjJ/B,QAAQ;oBACRC,YAAY;wBACV;4BACEC,UAAU0B,UAAU1B;4BACpBkC,UAAU;4BACVZ,MAAMI,UAAUS;4BAChBlC,gBAAgBjE,CAAAA,GAAAA,kDAAAA,EACd0F,UAAUS,MACVT,UAAUK,OAAOC,IAAIC,CAAAA,QAAU,CAAA;oCAC7Bf,KAAM,CAAA,CAAA,EAAGe,MAAM,CAAA,EAAGnG,CAAAA,GAAAA,8CAAAA,EAAuB0D,MAAM,CAAC;oCAChDE,MAAM;oCACNG,SAAS;gCACX,CAAA;wBAEJ;qBAAA;gBAEJ;YACF;YAGF;QACF;QACA8C,QAAQW,KAAK;kBAAC9D;mBAAMqC;QAAK;IAC3B;IAEA,IAAIc,QAAQ6C,QACV,MAAM5D,8BACJjE,IACAqI,gBACArD,SACA5D,MACAnB,aACAwH,OAAO2B,OAAO,CAAC,GAAG/F,SAAS;QAACiF,cAAc;IAAK;AAGrD;AAEA,eAAeK,gDACb3I,EAAc,EACdqJ,QAAkB,EAClBpJ,WAAqB;IAErB,IAAIqJ,aAAa,MAAMtC,CAAAA,GAAAA,gCAAAA,EACrBhH,IACAqJ,UACA;QAAC;QAAqB;QAAkB;KAAY,EACpDpJ;IAGF,IAAIsJ,aAAaD,cAAc5K,CAAAA,GAAAA,qCAAAA,EAAK8K,SAASF;IAE7C,uEAAA;IACA,yEAAA;IACA,IAAIC,eAAe,qBACjB,OAAO,IAAItB,CAAAA,GAAAA,UAAAA;SACN,IAAIsB,eAAe,kBACxB,OAAO,IAAIpB,CAAAA,GAAAA,WAAAA;SACN,IAAIoB,eAAe,aACxB,OAAO,IAAIrB,CAAAA,GAAAA,WAAAA;IAGb,IAAI,MAAMA,CAAAA,GAAAA,WAAAA,EAAKuB,UACb,OAAO,IAAIvB,CAAAA,GAAAA,WAAAA;SACN,IAAI,MAAMC,CAAAA,GAAAA,WAAAA,EAAKsB,UACpB,OAAO,IAAItB,CAAAA,GAAAA,WAAAA;SAEX,OAAO,IAAIF,CAAAA,GAAAA,UAAAA;AAEf;AAEA,IAAIyB,8BAAQ,IAAI3B,CAAAA,GAAAA,+BAAAA,EAAa;IAAC4B,eAAe;AAAC;AAC9C,IAAIC,0CAAiC,IAAI9E;AAKlC,SAAS+E,0CACd7J,EAAc,EACdqI,cAA8B,EAC9BrD,OAA6B,EAC7B3C,QAAkB,EAClBpC,WAAqB,EACrBoD,OAAwB;IAExB2B,UAAUA,QAAQX,IAAIyF,CAAAA,UAAY,CAAA;YAChCjI,MAAMuG,CAAAA,GAAAA,cAAAA,EAAwB0B,QAAQjI;YACtCqC,OAAO4F,QAAQ5F;QACjB,CAAA;IAEA,qEAAA;IACA,gFAAA;IACA,gBAAA;IACA,IAAI6F,mBAAmB/E,QAAQgF,OAC7BjH,CAAAA,IAAK,CAAC6G,wCAAkBlE,IAAIuE,0CAAoBlH;IAElD,IAAIgH,iBAAiBlC,QAAQ;QAC3B,KAAK,IAAI9E,KAAKgH,iBACZH,wCAAkB7E,IAAIkF,0CAAoBlH;QAG5C2G,4BACG3E,IAAI,IACHd,8BACEjE,IACAqI,gBACA0B,kBACA1H,UACApC,aACAoD,SACA6G,KAAK;gBACL,KAAK,IAAInH,KAAKgH,iBACZH,wCAAkBzD,OAAO8D,0CAAoBlH;YAEjD,IAEDmH,KACC,KAAO,GACP,KAAO;IAEb;IAEA,OAAOR,4BAAMS;AACf;AAEO,SAASnL,0CACdgB,EAAc,EACdqI,cAA8B,EAC9BrD,OAA6B,EAC7B3C,QAAkB,EAClBpC,WAAqB,EACrBoD,OAAwB;IAExB,IAAI2E,CAAAA,GAAAA,8CAAAA,EAAWoC,YAAY;QACzB,IAAIC,YAAYrC,CAAAA,GAAAA,8CAAAA,EAAWsC;QAC3B,qEAAA;QACA,IAAIC,aACF,AACA,CAAChL,QAAQC,IAAIC,oBACTf,CAAAA,GAAAA,qCAAAA,EAAK4I,KAAKkD,yCAAW,MAAM,kBAC3BC;QACN,OAAOJ,UAAUK,WAAW;YAC1BC,UAAUJ;YACVK,MAAM;gBAAC5K;gBAAIqI;gBAAgBrD;gBAAS3C;gBAAUpC;gBAAaoD;aAAQ;YACnEwH,QAAQ;QACV;IACF;IAEA,OAAOhB,0CACL7J,IACAqI,gBACArD,SACA3C,UACApC,aACAoD;AAEJ;AAEA,SAAS4G,0CAAoB5C,aAA4B;IACvD,OAAO;QAACA,cAAcxF;QAAMwF,cAAcnD;KAAM,CAACoD,KAAK;AACxD;;;;;;;;;;;;;;;;;ACnQA,MAAM0D,gCAAU;AAET,MAAM/C;IACX,MAAMhE,QAAQ,WACZe,OAAO,OACP0D,GAAG,MACH1I,EAAE,eACF4I,WAAW,WACXzE,UAAU,MACO,EAAiB;QAClC,0DAAA;QACA,oCAAA;QACA,IAAIyE,eAAe,MACjB,MAAM5I,GAAGiL,UAAUvM,CAAAA,GAAAA,qCAAAA,EAAK4I,KAAKoB,KAAK,iBAAiB;QAGrD,IAAIkC,OAAO;YAAC;YAAW;YAAUzG,UAAU,eAAe;SAAS,CAAC+G,OAClElG,QAAQX,IAAI+C,CAAAA,GAAAA,oCAAAA;QAGd,+FAAA;QACA,4FAAA;QACA,4CAAA;QACA,IAAI5H,MAAM,CAAC;QACX,IAAK,IAAI+D,OAAOhE,QAAQC,IACtB,IAAI,CAAC+D,IAAI4H,WAAW,WAAW5H,QAAQ,cAAcA,QAAQ,YAC3D/D,GAAG,CAAC+D,IAAI,GAAGhE,QAAQC,GAAG,CAAC+D,IAAI;QAI/B,IAAI6H,iBAAiBN,CAAAA,GAAAA,6CAAAA,EAAME,+BAASJ,MAAM;iBAAClC;iBAAKlJ;QAAG;QACnD,IAAI6L,SAAS;QACbD,eAAeC,OAAOC,GAAG,QAASC,CAAAA;YAChCF,UAAUE,IAAIC;QAChB;QAEA,IAAIC,SAAS,EAAE;QACfL,eAAeK,OAAOH,GAAG,QAASC,CAAAA;YAChCE,OAAO9F,KAAK4F,IAAIC,WAAWE;QAC7B;QAEA,IAAI;YACF,MAAMX,CAAAA,GAAAA,cAAAA,EAAmBK;YAEzB,IAAIO,UAAsB/E,KAAKC,MAAMwE;YACrC,IAAIO,aAAaD,QAAQE,MAAMhE;YAC/B,IAAI+D,aAAa,GACfhN,CAAAA,GAAAA,6CAAAA,EAAOkN,IAAI;gBACT3J,QAAQ;gBACRD,SAAU,CAAA,MAAA,EAAQ0J,WAAlB1J,iBAAAA,CAAAA;YACF;YAGF,wEAAA;YACA,yEAAA;YACA,+BAAA;YACA,KAAK,IAAIA,WAAWuJ,OAClB,IAAIvJ,QAAQ2F,SAAS,GACnBjJ,CAAAA,GAAAA,6CAAAA,EAAOkN,IAAI;gBACT3J,QAAQ;yBACRD;YACF;QAGN,EAAE,OAAOwB,GAAG;YACV,MAAM,IAAI4C,MACR,oCACE5C,EAAExB,UACF,QACAuJ,OAAOnE,KAAK;QAElB;IACF;AACF;AAMArJ,CAAAA,GAAAA,2CAAAA,EAA2B,CAAA,EAAEgB,CAAAA,GAAAA,6CAAAA,EAAIyF,QAAQ,IAAA,CAAK,EAAEuD;;;;AC7FhD;;;;;;AAMA,SAAS,4BAAM,OAAO,EAAE,IAAI,EAAE,OAAO;IACjC,sBAAsB;IACtB,MAAM,SAAS,OAAM,SAAS,MAAM;IAEpC,0BAA0B;IAC1B,MAAM,UAAU,2BAAS,OAAO,SAAS,OAAO,MAAM,OAAO;IAE7D,uEAAuE;IACvE,mFAAmF;IACnF,OAAO,iBAAiB,SAAS;IAEjC,OAAO;AACX;AAEA,SAAS,gCAAU,OAAO,EAAE,IAAI,EAAE,OAAO;IACrC,sBAAsB;IACtB,MAAM,SAAS,OAAM,SAAS,MAAM;IAEpC,0BAA0B;IAC1B,MAAM,SAAS,+BAAa,OAAO,SAAS,OAAO,MAAM,OAAO;IAEhE,yGAAyG;IACzG,OAAO,QAAQ,OAAO,SAAS,OAAO,iBAAiB,OAAO,QAAQ;IAEtE,OAAO;AACX;AAEA,iBAAiB;AACjB,eAAe,QAAQ;AACvB,eAAe,OAAO;AAEtB,eAAe,SAAS;AACxB,eAAe,UAAU;;;;ACtCzB;;;;;;;;;;;AASA,MAAM,8BAAQ,QAAQ,aAAa;AACnC,MAAM,2CAAqB;AAC3B,MAAM,wCAAkB;AAExB,mEAAmE;AACnE,MAAM,4CAAsB,OAAQ,IAAM,wBAAiB,QAAQ,SAAS,gCAAgC,UAAU;AAEtH,SAAS,oCAAc,MAAM;IACzB,OAAO,OAAO,OAAe;IAE7B,MAAM,UAAU,OAAO,QAAQ,OAAY,OAAO;IAElD,IAAI,SAAS;QACT,OAAO,KAAK,QAAQ,OAAO;QAC3B,OAAO,UAAU;QAEjB,OAAO,OAAe;IAC1B;IAEA,OAAO,OAAO;AAClB;AAEA,SAAS,oCAAc,MAAM;IACzB,IAAI,CAAC,6BACD,OAAO;IAGX,oCAAoC;IACpC,MAAM,cAAc,oCAAc;IAElC,iEAAiE;IACjE,MAAM,aAAa,CAAC,yCAAmB,KAAK;IAE5C,qFAAqF;IACrF,gEAAgE;IAChE,IAAI,OAAO,QAAQ,cAAc,YAAY;QACzC,gGAAgG;QAChG,4FAA4F;QAC5F,4FAA4F;QAC5F,gCAAgC;QAChC,MAAM,6BAA6B,sCAAgB,KAAK;QAExD,4EAA4E;QAC5E,6EAA6E;QAC7E,OAAO,UAAU,sBAAe,OAAO;QAEvC,6BAA6B;QAC7B,OAAO,UAAU,eAAe,OAAO;QACvC,OAAO,OAAO,OAAO,KAAK,IAAI,CAAC,MAAQ,gBAAgB,KAAK;QAE5D,MAAM,eAAe;YAAC,OAAO;SAAQ,CAAC,OAAO,OAAO,MAAM,KAAK;QAE/D,OAAO,OAAO;YAAC;YAAM;YAAM;YAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;SAAC;QACrD,OAAO,UAAU,QAAQ,IAAI,WAAW;QACxC,OAAO,QAAQ,2BAA2B,MAAM,2DAA2D;IAC/G;IAEA,OAAO;AACX;AAEA,SAAS,iCAAW,MAAM;IACtB,2EAA2E;IAC3E,IAAI,2CACA,OAAO;IAGX,0BAA0B;IAC1B,6GAA6G;IAC7G,MAAM,eAAe;QAAC,OAAO;KAAQ,CAAC,OAAO,OAAO,MAAM,KAAK;IAE/D,IAAI,6BAAO;QACP,OAAO,UAAU,OAAO,OAAO,QAAQ,UAAU,WAAW,OAAO,QAAQ,QAAQ,QAAQ,IAAI,WAAW;QAC1G,OAAO,OAAO;YAAC;YAAM;YAAM;YAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;SAAC;QACrD,OAAO,QAAQ,2BAA2B,MAAM,2DAA2D;IAC/G,OAAO;QACH,IAAI,OAAO,OAAO,QAAQ,UAAU,UAChC,OAAO,UAAU,OAAO,QAAQ;aAC7B,IAAI,QAAQ,aAAa,WAC5B,OAAO,UAAU;aAEjB,OAAO,UAAU;QAGrB,OAAO,OAAO;YAAC;YAAM;SAAa;IACtC;IAEA,OAAO;AACX;AAEA,SAAS,4BAAM,OAAO,EAAE,IAAI,EAAE,OAAO;IACjC,yCAAyC;IACzC,IAAI,QAAQ,CAAC,MAAM,QAAQ,OAAO;QAC9B,UAAU;QACV,OAAO;IACX;IAEA,OAAO,OAAO,KAAK,MAAM,KAAK,EAAE,EAAE,6CAA6C;IAC/E,UAAU,OAAO,OAAO,CAAC,GAAG,UAAU,8CAA8C;IAEpF,0BAA0B;IAC1B,MAAM,SAAS;iBACX;cACA;iBACA;QACA,MAAM;QACN,UAAU;qBACN;kBACA;QACJ;IACJ;IAEA,iDAAiD;IACjD,OAAO,QAAQ,QAAQ,iCAAW,UAAU,oCAAc;AAC9D;AAEA,iBAAiB;;;;AC5HjB;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,EAAE;IAE3B,IAAI;QAAE,OAAO;IAAK,EAAE,OAAO,GAAG,CAAC;AAEhC;;;;;ACXA;;;;;AAIA,MAAM,gCAAU;AAEhB,SAAS,4CAAsB,MAAM,EAAE,cAAc;IACjD,MAAM,MAAM,QAAQ;IACpB,MAAM,eAAe,OAAO,QAAQ,OAAO;IAE3C,qEAAqE;IACrE,uEAAuE;IACvE,IAAI,cACA,IAAI;QACA,QAAQ,MAAM,OAAO,QAAQ;IACjC,EAAE,OAAO,KAAK;IACV,SAAS,GACb;IAGJ,IAAI;IAEJ,IAAI;QACA,WAAW,YAAW,OAAO,SAAS;YAClC,MAAM,AAAC,CAAA,OAAO,QAAQ,OAAO,QAAQ,GAAE,CAAE,CAAC,8BAAQ;YAClD,SAAS,iBAAiB,wBAAiB;QAC/C;IACJ,EAAE,OAAO,GAAG;IACR,SAAS,GACb,SAAU;QACN,QAAQ,MAAM;IAClB;IAEA,wEAAwE;IACxE,6FAA6F;IAC7F,IAAI,UACA,WAAW,oBAAa,eAAe,OAAO,QAAQ,MAAM,IAAI;IAGpE,OAAO;AACX;AAEA,SAAS,qCAAe,MAAM;IAC1B,OAAO,4CAAsB,WAAW,4CAAsB,QAAQ;AAC1E;AAEA,iBAAiB;;;;AC9CjB,iBAAiB;AACjB,4BAAM,OAAO;AAEb,IAAI,kCAAY,QAAQ,aAAa,WACjC,QAAQ,IAAI,WAAW,YACvB,QAAQ,IAAI,WAAW;;AAG3B,IAAI,8BAAQ,kCAAY,MAAM;;;AAG9B,SAAS,uCAAkB,GAAG;IAC5B,IAAI,KAAK,IAAI,MAAM,gBAAgB;IACnC,GAAG,OAAO;IAEV,OAAO;AACT;AAEA,SAAS,kCAAa,GAAG,EAAE,GAAG;IAC5B,IAAI,QAAQ,IAAI,SAAS;IACzB,IAAI,UAAU,IAAI,QAAQ,QAAQ,IAAI,QAAQ;IAC9C,IAAI,UAAU;QAAC;KAAG;IAElB,UAAU,QAAQ,MAAM;IAExB,IAAI,aAAa;IACjB,IAAI,iCAAW;QACb,QAAQ,QAAQ,QAAQ;QACxB,aAAc,IAAI,WAAW,QAAQ,IAAI,WAAW;QACpD,UAAU,WAAW,MAAM;QAG3B,mEAAmE;QACnE,iCAAiC;QACjC,IAAI,IAAI,QAAQ,SAAS,MAAM,OAAO,CAAC,EAAE,KAAK,IAC5C,QAAQ,QAAQ;IACpB;IAEA,iEAAiE;IACjE,6CAA6C;IAC7C,IAAI,IAAI,MAAM,SAAS,mCAAa,IAAI,MAAM,OAC5C,UAAU;QAAC;KAAG;IAEhB,OAAO;QACL,KAAK;QACL,KAAK;QACL,QAAQ;IACV;AACF;AAEA,SAAS,4BAAO,GAAG,EAAE,GAAG,EAAE,EAAE;IAC1B,IAAI,OAAO,QAAQ,YAAY;QAC7B,KAAK;QACL,MAAM,CAAC;IACT;IAEA,IAAI,OAAO,kCAAY,KAAK;IAC5B,IAAI,UAAU,KAAK;IACnB,IAAI,UAAU,KAAK;IACnB,IAAI,aAAa,KAAK;IACtB,IAAI,QAAQ,EAAE;IAEZ,CAAA,SAAS,EAAG,CAAC,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG;YACX,IAAI,IAAI,OAAO,MAAM,QACnB,OAAO,GAAG,MAAM;iBAEhB,OAAO,GAAG,uCAAiB;QAC/B;QAEA,IAAI,WAAW,OAAO,CAAC,EAAE;QACzB,IAAI,SAAS,OAAO,OAAO,OAAO,SAAS,MAAM,QAAQ,KACvD,WAAW,SAAS,MAAM,GAAG;QAE/B,IAAI,IAAI,iBAAU,UAAU;QAC5B,IAAI,CAAC,YAAY,AAAC,YAAa,KAAK,MAClC,IAAI,IAAI,MAAM,GAAG,KAAK;QAEtB,CAAA,SAAS,EAAG,EAAE,EAAE,EAAE;YAClB,IAAI,OAAO,IAAI,OAAO,EAAE,IAAI,GAAG;YAC/B,IAAI,MAAM,OAAO,CAAC,GAAG;YACrB,OAAM,IAAI,KAAK;gBAAE,SAAS;YAAW,GAAG,SAAU,EAAE,EAAE,EAAE;gBACtD,IAAI,CAAC,MAAM,IAAI;oBACb,IAAI,IAAI,KACN,MAAM,KAAK,IAAI;yBAEf,OAAO,GAAG,MAAM,IAAI;gBACxB;gBACA,OAAO,EAAE,KAAK,GAAG;YACnB;QACF,CAAA,EAAG,GAAG,QAAQ;IAChB,CAAA,EAAG,GAAG,QAAQ;AAChB;AAEA,SAAS,gCAAW,GAAG,EAAE,GAAG;IAC1B,MAAM,OAAO,CAAC;IAEd,IAAI,OAAO,kCAAY,KAAK;IAC5B,IAAI,UAAU,KAAK;IACnB,IAAI,UAAU,KAAK;IACnB,IAAI,aAAa,KAAK;IACtB,IAAI,QAAQ,EAAE;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,IAAM;QAC/C,IAAI,WAAW,OAAO,CAAC,EAAE;QACzB,IAAI,SAAS,OAAO,OAAO,OAAO,SAAS,MAAM,QAAQ,KACvD,WAAW,SAAS,MAAM,GAAG;QAE/B,IAAI,IAAI,iBAAU,UAAU;QAC5B,IAAI,CAAC,YAAY,YAAY,KAAK,MAChC,IAAI,IAAI,MAAM,GAAG,KAAK;QAExB,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,IAAM;YACjD,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE;YACxB,IAAI;YACJ,IAAI;gBACF,KAAK,OAAM,KAAK,KAAK;oBAAE,SAAS;gBAAW;gBAC3C,IAAI,IAAI;oBACN,IAAI,IAAI,KACN,MAAM,KAAK;yBAEX,OAAO;gBACX;YACF,EAAE,OAAO,IAAI,CAAC;QAChB;IACF;IAEA,IAAI,IAAI,OAAO,MAAM,QACnB,OAAO;IAET,IAAI,IAAI,SACN,OAAO;IAET,MAAM,uCAAiB;AACzB;;;;;ACrIA,IAAI;;;AACJ,IAAI,QAAQ,aAAa,WAAW,eAAO,iBACzC,6BAAO;KAEP,6BAAO;AAGT,iBAAiB;AACjB,4BAAM,OAAO;AAEb,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE;IAC/B,IAAI,OAAO,YAAY,YAAY;QACjC,KAAK;QACL,UAAU,CAAC;IACb;IAEA,IAAI,CAAC,IAAI;QACP,IAAI,OAAO,YAAY,YACrB,MAAM,IAAI,UAAU;QAGtB,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,4BAAM,MAAM,WAAW,CAAC,GAAG,SAAU,EAAE,EAAE,EAAE;gBACzC,IAAI,IACF,OAAO;qBAEP,QAAQ;YAEZ;QACF;IACF;IAEA,2BAAK,MAAM,WAAW,CAAC,GAAG,SAAU,EAAE,EAAE,EAAE;QACxC,oEAAoE;QACpE,IAAI,IACF;YAAA,IAAI,GAAG,SAAS,YAAY,WAAW,QAAQ,cAAc;gBAC3D,KAAK;gBACL,KAAK;YACP;QAAA;QAEF,GAAG,IAAI;IACT;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO;IAC1B,kCAAkC;IAClC,IAAI;QACF,OAAO,2BAAK,KAAK,MAAM,WAAW,CAAC;IACrC,EAAE,OAAO,IAAI;QACX,IAAI,WAAW,QAAQ,gBAAgB,GAAG,SAAS,UACjD,OAAO;aAEP,MAAM;IAEV;AACF;;;;;;;;;;ACbA,IAAA;AACA,IAAA;AA5CA;AAEA,oDAAoD;AACpD,MAAM,wCAAkB;AAExB,SAAS,oCAAc,GAAG;IACtB,oBAAoB;IACpB,MAAM,IAAI,QAAQ,uCAAiB;IAEnC,OAAO;AACX;AAEA,SAAS,qCAAe,GAAG,EAAE,qBAAqB;IAC9C,oBAAoB;IACpB,MAAM,CAAC,EAAE,IAAI,CAAC;IAEd,mDAAmD;IAEnD,sDAAsD;IACtD,4DAA4D;IAC5D,MAAM,IAAI,QAAQ,WAAW;IAE7B,4DAA4D;IAC5D,4CAA4C;IAC5C,gCAAgC;IAChC,MAAM,IAAI,QAAQ,UAAU;IAE5B,wCAAwC;IAExC,yBAAyB;IACzB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAEhB,oBAAoB;IACpB,MAAM,IAAI,QAAQ,uCAAiB;IAEnC,wCAAwC;IACxC,IAAI,uBACA,MAAM,IAAI,QAAQ,uCAAiB;IAGvC,OAAO;AACX;AAEA,4CAAyB;AACzB,4CAA0B;;;;;AC5C1B;;;;AAKA,SAAS,kCAAY,OAAO;IACxB,yCAAyC;IACzC,MAAM,OAAO;IACb,IAAI;IAEJ,IAAI,OAAO,OACP,yBAAyB;IACzB,SAAS,OAAO,MAAM;SACnB;QACH,kBAAkB;QAClB,SAAS,IAAI,OAAO;QACpB,OAAO,KAAK,IAAI,YAAY;IAChC;IAEA,IAAI;IAEJ,IAAI;QACA,KAAK,mBAAY,SAAS;QAC1B,mBAAY,IAAI,QAAQ,GAAG,MAAM;QACjC,oBAAa;IACjB,EAAE,OAAO,GAAG,CAAc;IAE1B,iEAAiE;IACjE,OAAO,OAAe,OAAO;AACjC;AAEA,iBAAiB;;;;AC/BjB;;;AAGA,iBAAiB,SAAU,GAAG;IAC7B,IAAI,QAAQ,IAAI,MAAM;IAEtB,IAAI,CAAC,OACJ,OAAO;IAGR,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,QAAQ,QAAQ,IAAI,MAAM;IAC7C,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK;IAC5B,IAAI,MAAM,GAAG,CAAC,EAAE;IAEhB,OAAQ,QAAQ,QACf,MACA,MAAO,CAAA,MAAM,MAAM,MAAM,EAAC;AAE5B;;;;AClBA;AACA,iBAAiB;;;;;;;;ACDjB;AAEA,MAAM,8BAAQ,QAAQ,aAAa;AAEnC,SAAS,oCAAc,QAAQ,EAAE,OAAO;IACpC,OAAO,OAAO,OAAO,IAAI,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,QAAQ,OAAO,CAAC,GAAG;QACrE,MAAM;QACN,OAAO;QACP,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC;QACzC,MAAM,SAAS;QACf,WAAW,SAAS;IACxB;AACJ;AAEA,SAAS,uCAAiB,EAAE,EAAE,MAAM;IAChC,IAAI,CAAC,6BACD;IAGJ,MAAM,eAAe,GAAG;IAExB,GAAG,OAAO,SAAU,IAAI,EAAE,IAAI;QAC1B,mEAAmE;QACnE,iDAAiD;QACjD,iEAAiE;QACjE,IAAI,SAAS,QAAQ;YACjB,MAAM,MAAM,mCAAa,MAAM,QAAQ;YAEvC,IAAI,KACA,OAAO,aAAa,KAAK,IAAI,SAAS;QAE9C;QAEA,OAAO,aAAa,MAAM,IAAI,YAAY,yCAAyC;IACvF;AACJ;AAEA,SAAS,mCAAa,MAAM,EAAE,MAAM;IAChC,IAAI,+BAAS,WAAW,KAAK,CAAC,OAAO,MACjC,OAAO,oCAAc,OAAO,UAAU;IAG1C,OAAO;AACX;AAEA,SAAS,uCAAiB,MAAM,EAAE,MAAM;IACpC,IAAI,+BAAS,WAAW,KAAK,CAAC,OAAO,MACjC,OAAO,oCAAc,OAAO,UAAU;IAG1C,OAAO;AACX;AAEA,iBAAiB;sBACb;kBACA;sBACA;mBACA;AACJ;;;;;;;;ACtDe,kDACb8D,YAA0B;IAE1B,OAAO,IAAIlD,QAAQ,CAAC7G,SAASgK;QAC3BD,aAAaT,GAAG,SAASU;QACzBD,aAAaT,GAAG,SAAS3H,CAAAA;YACvB,IAAIA,SAAS,GAAG;gBACdqI,OAAO,IAAI1F,MAAM;gBACjB;YACF;YAEAtE;QACF;IACF;AACF;;;;;AClBAb,iBAAiByF,KAAKC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;ACgB5B,MAAMuF,iCAAW;AAoBjB,IAAIC;AACJ,IAAIC;AAEG,MAAMpE;IACX,aAAauB,SAA2B;QACtC,IAAI4C,iCAAW,MACb,OAAOA;QAGT,IAAI;YACFA,gCAAUE,QAAQ,MAAMN,CAAAA,GAAAA,6CAAAA,EAAc;QACxC,EAAE,OAAO7I,KAAK;YACZiJ,gCAAU;QACZ;QAEA,OAAOA;IACT;IAEA,MAAMpI,QAAQ,WACZe,OAAO,OACP0D,GAAG,WACHvE,UAAU,MACO,EAAiB;QAClC,IAAImI,qCAAe,MAAM;YACvB,IAAI5H,UAAU,MAAMuC,CAAAA,GAAAA,WAAAA,EAAK;YACzBqF,oCAAcE,SAAS9H,QAAQ2G,QAAQ;QACzC;QAEA,IAAIT,OAAO;YAAC;YAAO;SAAS,CAACM,OAC3BlG,QAAQX,IAAI+C,CAAAA,GAAAA,oCAAAA;QAGd,IAAIjD,SAAS;YACXyG,KAAKjF,KAAK;YACV,IAAI2G,oCAAc,GAChB1B,KAAKjF,KAAK;QAEd;QAEA,gGAAA;QACA,8FAAA;QACA,4CAAA;QACA,IAAInG,MAAM,CAAC;QACX,IAAK,IAAI+D,OAAOhE,QAAQC,IACtB,IACE,CAAC+D,IAAI4H,WAAW,WAChB5H,QAAQ,sBACRA,QAAQ,cACRA,QAAQ,YAER/D,GAAG,CAAC+D,IAAI,GAAGhE,QAAQC,GAAG,CAAC+D,IAAI;QAI/B,IAAI6H,iBAAiBN,CAAAA,GAAAA,6CAAAA,EAAMsB,gCAAUxB,MAAM;iBAAClC;iBAAKlJ;QAAG;QACpD4L,eAAeC,MACb,+EAAA;SACCoB,KAAKP,CAAAA,GAAAA,6CAAAA,KACLO,KAAK,IAAIN,CAAAA,GAAAA,cAAAA,KACTb,GAAG,SAAS5H,CAAAA;YACX9E,CAAAA,GAAAA,6CAAAA,EAAOyH,MAAM3C,GAAG;QAClB,GACC4H,GAAG,QAASpJ,CAAAA;YACX,OAAQA,QAAQH;gBACd,KAAK;oBACHnD,CAAAA,GAAAA,6CAAAA,EAAO4J,SACLkE,6BACG,CAAA,CAAA,EAAGxK,QAAQyK,KAAKC,QAAQ,CAAA,EAAG1K,QAAQyK,KAAKE,MAAM,EAAA,EAAI3K,QAAQyK,KAAKzK,QAAQ,CAAC;oBAG7E;gBACF,KAAK;gBACL,KAAK;oBACHtD,CAAAA,GAAAA,6CAAAA,EAAOkO,KAAK;wBACV3K,QAAQ;wBACRD,SAASwK,6BAAOxK,QAAQyK;oBAC1B;oBACA;gBACF;YACA;QAEJ;QAEFvB,eAAeK,OACZgB,KAAKP,CAAAA,GAAAA,6CAAAA,KACLO,KAAK,IAAIN,CAAAA,GAAAA,cAAAA,KACTb,GAAG,SAAS5H,CAAAA;YACX9E,CAAAA,GAAAA,6CAAAA,EAAOyH,MAAM3C,GAAG;QAClB,GACC4H,GAAG,QAASpJ,CAAAA;YACX,OAAQA,QAAQH;gBACd,KAAK;oBACHnD,CAAAA,GAAAA,6CAAAA,EAAOqD,KAAK;wBACVE,QAAQ;wBACRD,SAASwK,6BAAOxK,QAAQyK;oBAC1B;oBACA;gBACF,KAAK;oBACH/N,CAAAA,GAAAA,6CAAAA,EAAOyH,MAAM;wBACXlE,QAAQ;wBACRD,SAASwK,6BAAOxK,QAAQyK;oBAC1B;oBACA;gBACF;YACA;QAEJ;QAEF,IAAI;YACF,OAAO,MAAM5B,CAAAA,GAAAA,cAAAA,EAAmBK;QAClC,EAAE,OAAO1H,GAAG;YACV,MAAM,IAAI4C,MAAM,oCAAoC5C,EAAExB;QACxD;IACF;AACF;AAEA,SAASwK,6BAAOxK,OAAe;IAC7B,OAAO,WAAWA;AACpB;AAEAjE,CAAAA,GAAAA,2CAAAA,EAA2B,CAAA,EAAEgB,CAAAA,GAAAA,6CAAAA,EAAIyF,QAAQ,KAAA,CAAM,EAAEwD;;;;;AC5JjD,iBAAiB;;;;;ACAjB;;;;;;;;;;;;;;AAcA,GAEA;;;0CAEM;;8CACA;AACN,MAAM,8BAAQ,OAAO;AACrB,MAAM,iCAAW,OAAO;AAExB,SAAS,gCAAW,KAAK,EAAE,GAAG,EAAE,EAAE;IAChC,IAAI;IACJ,IAAI,IAAI,CAAC,UAAU;QACjB,IAAI,MAAM,IAAI,CAAC,+BAAS,CAAC,MAAM;QAC/B,OAAO,IAAI,MAAM,IAAI,CAAC;QAEtB,IAAI,KAAK,WAAW,GAAG,OAAO,KAAK,+CAA+C;;QAElF,0FAA0F;QAC1F,KAAK;QACL,IAAI,CAAC,WAAW;IAClB,OAAO;QACL,IAAI,CAAC,4BAAM,IAAI,IAAI,CAAC,+BAAS,CAAC,MAAM;QACpC,OAAO,IAAI,CAAC,4BAAM,CAAC,MAAM,IAAI,CAAC;IAChC;IAEA,IAAI,CAAC,4BAAM,GAAG,KAAK;IAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,IAC/B,IAAI;QACF,2BAAK,IAAI,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,OAAO,GAAG;IACZ;IAGF,IAAI,CAAC,WAAW,IAAI,CAAC,4BAAM,CAAC,SAAS,IAAI,CAAC;IAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,OAAO,GAAG,IAAI,MAAM;IAE7D;AACF;AAEA,SAAS,4BAAO,EAAE;IAChB,sCAAsC;IACtC,IAAI,CAAC,4BAAM,IAAI,IAAI,CAAC,+BAAS,CAAC;IAE9B,IAAI,IAAI,CAAC,4BAAM,EACb,IAAI;QACF,2BAAK,IAAI,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,4BAAM;IACpC,EAAE,OAAO,OAAO;QACd,OAAO,GAAG;IACZ;IAGF;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,GAAG;IACtB,IAAI,QAAQ,WACV,KAAK,KAAK;AAEd;AAEA,SAAS,2BAAM,QAAQ;IACrB,OAAO;AACT;AAEA,SAAS,4BAAO,OAAO,EAAE,MAAM,EAAE,OAAO;IACtC,+CAA+C;IAC/C,UAAU,WAAW;IACrB,SAAS,UAAU;IACnB,UAAU,WAAW,CAAC;IAEtB,6BAA6B;IAC7B,OAAQ,UAAU;QAChB,KAAK;YACH,8BAA8B;YAC9B,IAAI,OAAO,YAAY,YAAY;gBACjC,SAAS;gBACT,UAAU;YACZ,+BAA+B;YAC/B,OAAO,IAAI,OAAO,YAAY,YAAY,CAAE,CAAA,mBAAmB,MAAK,GAAI;gBACtE,UAAU;gBACV,UAAU;YACZ;YACA;QAEF,KAAK;YACH,uCAAuC;YACvC,IAAI,OAAO,YAAY,YAAY;gBACjC,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ,wCAAwC;YACxC,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,UAAU;gBACV,SAAS;YACX;IACJ;IAEA,UAAU,OAAO,OAAO,CAAC,GAAG;IAC5B,QAAQ,YAAY;IACpB,QAAQ,QAAQ;IAChB,QAAQ,qBAAqB;IAE7B,MAAM,SAAS,wCAAc;IAE7B,MAAM,CAAC,4BAAM,GAAG;IAChB,MAAM,CAAC,+BAAS,GAAG,4CAAkB;IACrC,OAAO,UAAU;IACjB,OAAO,SAAS;IAChB,OAAO,YAAY,QAAQ;IAC3B,OAAO,eAAe,QAAQ;IAC9B,OAAO,WAAW;IAElB,OAAO;AACT;AAEA,iBAAiB;;;;;;;;;AC1HF,uDAA8B6E,CAAAA,GAAAA,uBAAAA;IAC3ChN,YAAYsD,OAAc,CAAE;QAC1B,KAAK,CAAC;YAAC,GAAGA,OAAO;YAAE2J,YAAY;QAAI;IACrC;IAEA,oEAAA;IACAC,WACEC,KAAsB,EACtB/J,QAAgB,EAChBgK,QAAqD,EACrD;QACA,IAAI;YACF,IAAIC;YACJ,IAAI;gBACFA,SAASxG,KAAKC,MAAMqG,MAAM1B;YAC5B,EAAE,OAAO9H,GAAG;gBACV,8DAAA;gBACA,mDAAA;gBACA9E,CAAAA,GAAAA,6CAAAA,EAAOyO,QAAQ;oBACbnL,SAAS,mCAAmCgL,MAAM1B;oBAClDrJ,QAAQ;gBACV;gBACA;YACF;YACAgL,SAAS,MAAMC;QACjB,EAAE,OAAOhK,KAAK;YACZ+J,SAAS/J;QACX;IACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBA,MAAMkK,iCAAW;AA+CjB,IAAIC;AACJ,IAAIC;AAEG,MAAMrF;IACX,aAAasB,SAA2B;QACtC,IAAI8D,iCAAW,MACb,OAAOA;QAGT,IAAI;YACFA,gCAAUhB,QAAQ,MAAMN,CAAAA,GAAAA,6CAAAA,EAAc;QACxC,EAAE,OAAO7I,KAAK;YACZmK,gCAAU;QACZ;QAEA,OAAOA;IACT;IAEA,MAAMtJ,QAAQ,WACZe,OAAO,OACP0D,GAAG,WACHvE,UAAU,MACO,EAAiB;QAClC,IAAIqJ,qCAAe,MAAM;YACvB,IAAI9I,UAAU,MAAMuC,CAAAA,GAAAA,WAAAA,EAAK;YACzBuG,oCAAchB,SAAS9H,QAAQ2G,QAAQ;QACzC;QAEA,IAAIT,OAAO;YAAC;YAAO;YAAc;SAAS;QAC1C,IAAIzG,SACFyG,KAAKjF,KAAK;QAEZ,IAAI6H,qCAAe,GACjB;YAAA,IAAIxN,CAAAA,GAAAA,mCAAAA,EAAGyN,WAAW/O,CAAAA,GAAAA,qCAAAA,EAAK4I,KAAKoB,KAAK,yBAC/B,iDAAA;YACAkC,KAAKjF,KAAK;QACZ,OAEA,+BAAA;QACAiF,KAAKjF,KAAK;QAEZiF,OAAOA,KAAKM,OAAOlG,QAAQX,IAAI+C,CAAAA,GAAAA,oCAAAA;QAE/B,IAAI5H,MAAM,CAAC;QACX,IAAK,IAAI+D,OAAOhE,QAAQC,IACtB,IAAI,CAAC+D,IAAI4H,WAAW,WAAW5H,QAAQ,cAAcA,QAAQ,YAC3D/D,GAAG,CAAC+D,IAAI,GAAGhE,QAAQC,GAAG,CAAC+D,IAAI;QAI/B,IAAIqI,aAAa,GACf8B,eAAe;QAEjB,IAAItC,iBAAiBN,CAAAA,GAAAA,6CAAAA,EAAMwC,gCAAU1C,MAAM;iBACzClC;iBACAlJ;QACF;QACA4L,eAAeC,OACZoB,KAAKP,CAAAA,GAAAA,6CAAAA,KACLO,KAAK,IAAIN,CAAAA,GAAAA,cAAAA,KACTb,GAAG,SAAS5H,CAAAA;YACX9E,CAAAA,GAAAA,6CAAAA,EAAOqD,KAAK;gBACVE,QAAQ;gBACRD,SAASwB,EAAEwJ;gBACXS,OAAOjK,EAAEiK;YACX;QACF,GACCrC,GAAG,QAAS9G,CAAAA;YACX,IAAIA,KAAKoJ,UAAU,SACjBhP,CAAAA,GAAAA,6CAAAA,EAAOyH,MAAM;gBACXlE,QAAQ;gBACRD,SAASsC,KAAKpB,IAAIlB;gBAClByL,OAAOnJ,KAAKpB,IAAIuK;YAClB;iBACK,IAAInJ,KAAKoJ,UAAU,UAAU,OAAOpJ,KAAKtC,YAAY,UAC1DtD,CAAAA,GAAAA,6CAAAA,EAAOkO,KAAK;gBACV3K,QAAQ;gBACRD,SAASwK,6BAAOlI,KAAKtC;YACvB;iBACK,IAAIsC,KAAK3C,SAAS,cAAc;oBACvB2C;gBAAdoH,cAAcpH,CAAAA,cAAAA,KAAKqH,mBAALrH,yBAAAA,cAAc;oBACZA;gBAAhBkJ,gBAAgBlJ,CAAAA,gBAAAA,KAAKqJ,qBAALrJ,2BAAAA,gBAAgB;YAClC;QACF;QAEF,IAAIiH,SAAS,EAAE;QACfL,eAAeK,OACZH,GAAG,QAAQwC,CAAAA;YACVrC,OAAO9F,KAAKmI,IAAItC;QAClB,GACCF,GAAG,SAAS5H,CAAAA;YACX9E,CAAAA,GAAAA,6CAAAA,EAAOqD,KAAK;gBACVE,QAAQ;gBACRD,SAASwB,EAAExB;YACb;QACF;QAEF,IAAI;YACF,MAAM6I,CAAAA,GAAAA,cAAAA,EAAmBK;YAEzB,IAAIQ,aAAa,KAAK8B,eAAe,GACnC9O,CAAAA,GAAAA,6CAAAA,EAAOkN,IAAI;gBACT3J,QAAQ;gBACRD,SAAU,CAAA,MAAA,EAAQ0J,WAAW,CAAA,EAC3B8B,eAAe,IAAK,CAAA,YAAA,EAAcA,aAAa,CAAA,CAAE,GAAG,GADtDxL,iBAAAA,CAAAA;YAGF;YAGF,wEAAA;YACA,yEAAA;YACA,+BAAA;YACA,KAAK,IAAIA,WAAWuJ,OAClB7M,CAAAA,GAAAA,6CAAAA,EAAOkN,IAAI;gBACT3J,QAAQ;yBACRD;YACF;QAEJ,EAAE,OAAOwB,GAAG;YACV,MAAM,IAAI4C,MAAM;QAClB;IACF;AACF;AAEA,SAASoG,6BAAOxK,OAAe;IAC7B,OAAO,WAAWA;AACpB;AAEAjE,CAAAA,GAAAA,2CAAAA,EAA2B,CAAA,EAAEgB,CAAAA,GAAAA,6CAAAA,EAAIyF,QAAQ,KAAA,CAAM,EAAEyD;;;;;;;AC/LjD,MAAM4F,qCAAe;AAEN,kDAAiCC,UAAkB;IAChE,IAAIC,UAAUF,mCAAa9G,KAAK+G;IAChC,IAAIC,SACF,OAAOA,OAAO,CAAC,EAAE;IAGnB,OAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;AEQO,MAAME;IACXC,WAAiC,IAAIzO,MAArCyO;IAEAC,SAASC,WAAmB,EAAEtO,EAAc,EAAE4I,WAAqB,EAAE;QACnE,IAAI,CAACwF,SAASzJ,IAAI2J,aAAa;gBAACtO;yBAAI4I;QAAW;IACjD;IAEA,MAAM3E,QAAQ,WACZe,OAAO,MACPhF,EAAE,OACF0I,GAAG,eACHE,WAAW,WACXzE,UAAU,MACO,EAAiB;QAClC,IAAIyE,eAAe,MAAM;YACvBA,cAAclK,CAAAA,GAAAA,qCAAAA,EAAK4I,KAAKoB,KAAK;YAC7B,MAAM1I,GAAGiL,UAAUrC,aAAa;QAClC;QAEA,IAAI3J,MAAM2H,KAAKC,MAAM,MAAM7G,GAAG4H,SAASgB,aAAa;QACpD,IAAIrF,MAAMY,UAAU,oBAAoB;QAExC,IAAI,CAAClF,GAAG,CAACsE,IAAI,EACXtE,GAAG,CAACsE,IAAI,GAAG,CAAC;QAGd,KAAK,IAAIpC,UAAU6D,QACjB/F,GAAG,CAACsE,IAAI,CAACpC,OAAOU,KAAK,GACnB,MAAO,MAAM,IAAI,CAAC7C,eAAemC,QAAQnB,IAAI4I;QAGjD,MAAM5I,GAAGiL,UAAUrC,aAAahC,KAAK2H,UAAUtP;IACjD;IAEA,MAAMD,eACJqI,aAA4B,EAC5BrH,EAAc,EACd4I,WAAqB,EACP;QACd,IAAI3J,MAAM,IAAI,CAACmP,SAAS5K,IAAI6D,cAAcxF;QAC1C,IAAI,CAAC5C,KACH,MAAM,IAAIqH,MAAM,qBAAqBe,cAAcxF;QAGrD,IAAI2M,OAAO9P,CAAAA,GAAAA,qCAAAA,EAAK4I,KACd5I,CAAAA,GAAAA,qCAAAA,EAAK4C,QAAQsH,cACb,gBACAvB,cAAcxF;QAEhB,MAAMqM,CAAAA,GAAAA,mBAAAA,EAAIjP,IAAIe,IAAIf,IAAI2J,aAAa5I,IAAIwO;QAEvC,IAAIC,cAAc7H,KAAKC,MACrB,MAAM7G,GAAG4H,SAASlJ,CAAAA,GAAAA,qCAAAA,EAAK4I,KAAKkH,MAAM,iBAAiB;QAGrD,IAAIC,YAAYC,gBAAgB,MAC9B,KAAK,IAAIC,OAAOpH,CAAAA,GAAAA,sCAAAA,EACdkH,YAAYC,cAEZ,MAAM,IAAI,CAAC1P,eAAe2P,KAAK3O,IAAI4I;QAIvC,OAAO6F,YAAY/J;IACrB;AACF;AAEAzG,CAAAA,GAAAA,2CAAAA,EACG,CAAA,EAAEgB,CAAAA,GAAAA,6CAAAA,EAAIyF,QAAQ,qBAAA,CAAsB,EACrCyJ;;;;;", "sources": ["node_modules/isexe/windows.js", "node_modules/isexe/mode.js", "node_modules/path-key/index.js", "node_modules/command-exists/lib/command-exists.js", "node_modules/split2/node_modules/readable-stream/readable.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_readable.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/stream.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/buffer_list.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/destroy.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/state.js", "node_modules/split2/node_modules/readable-stream/errors.js", "node_modules/inherits/inherits.js", "node_modules/inherits/inherits_browser.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_duplex.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_writable.js", "node_modules/util-deprecate/node.js", "node_modules/string_decoder/lib/string_decoder.js", "node_modules/safe-buffer/index.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/async_iterator.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/end-of-stream.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/from.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_transform.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_passthrough.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/pipeline.js", "packages/core/package-manager/src/NodePackageManager.js", "node_modules/nullthrows/nullthrows.js", "packages/core/package-manager/src/utils.js", "packages/core/package-manager/src/installPackage.js", "packages/core/package-manager/src/Npm.js", "node_modules/cross-spawn/index.js", "node_modules/cross-spawn/lib/parse.js", "node_modules/nice-try/src/index.js", "node_modules/cross-spawn/lib/util/resolveCommand.js", "node_modules/which/which.js", "node_modules/isexe/index.js", "node_modules/cross-spawn/lib/util/escape.js", "node_modules/cross-spawn/lib/util/readShebang.js", "node_modules/shebang-command/index.js", "node_modules/shebang-regex/index.js", "node_modules/cross-spawn/lib/enoent.js", "packages/core/package-manager/src/promiseFromProcess.js", "packages/core/package-manager/package.json", "packages/core/package-manager/src/Yarn.js", "node_modules/command-exists/index.js", "node_modules/split2/index.js", "packages/core/package-manager/src/JSONParseStream.js", "packages/core/package-manager/src/Pnpm.js", "packages/core/package-manager/src/validateModuleSpecifier.js", "packages/core/package-manager/src/index.js", "packages/core/package-manager/src/MockPackageInstaller.js"], "sourcesContent": ["module.exports = isexe\nisexe.sync = sync\n\nvar fs = require('fs')\n\nfunction checkPathExt (path, options) {\n  var pathext = options.pathExt !== undefined ?\n    options.pathExt : process.env.PATHEXT\n\n  if (!pathext) {\n    return true\n  }\n\n  pathext = pathext.split(';')\n  if (pathext.indexOf('') !== -1) {\n    return true\n  }\n  for (var i = 0; i < pathext.length; i++) {\n    var p = pathext[i].toLowerCase()\n    if (p && path.substr(-p.length).toLowerCase() === p) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction checkStat (stat, path, options) {\n  if (!stat.isSymbolicLink() && !stat.isFile()) {\n    return false\n  }\n  return checkPathExt(path, options)\n}\n\nfunction isexe (path, options, cb) {\n  fs.stat(path, function (er, stat) {\n    cb(er, er ? false : checkStat(stat, path, options))\n  })\n}\n\nfunction sync (path, options) {\n  return checkStat(fs.statSync(path), path, options)\n}\n", "module.exports = isexe\nisexe.sync = sync\n\nvar fs = require('fs')\n\nfunction isexe (path, options, cb) {\n  fs.stat(path, function (er, stat) {\n    cb(er, er ? false : checkStat(stat, options))\n  })\n}\n\nfunction sync (path, options) {\n  return checkStat(fs.statSync(path), options)\n}\n\nfunction checkStat (stat, options) {\n  return stat.isFile() && checkMode(stat, options)\n}\n\nfunction checkMode (stat, options) {\n  var mod = stat.mode\n  var uid = stat.uid\n  var gid = stat.gid\n\n  var myUid = options.uid !== undefined ?\n    options.uid : process.getuid && process.getuid()\n  var myGid = options.gid !== undefined ?\n    options.gid : process.getgid && process.getgid()\n\n  var u = parseInt('100', 8)\n  var g = parseInt('010', 8)\n  var o = parseInt('001', 8)\n  var ug = u | g\n\n  var ret = (mod & o) ||\n    (mod & g) && gid === myGid ||\n    (mod & u) && uid === myUid ||\n    (mod & ug) && myUid === 0\n\n  return ret\n}\n", "'use strict';\nmodule.exports = opts => {\n\topts = opts || {};\n\n\tconst env = opts.env || process.env;\n\tconst platform = opts.platform || process.platform;\n\n\tif (platform !== 'win32') {\n\t\treturn 'PATH';\n\t}\n\n\treturn Object.keys(env).find(x => x.toUpperCase() === 'PATH') || 'Path';\n};\n", "'use strict';\n\nvar exec = require('child_process').exec;\nvar execSync = require('child_process').execSync;\nvar fs = require('fs');\nvar path = require('path');\nvar access = fs.access;\nvar accessSync = fs.accessSync;\nvar constants = fs.constants || fs;\n\nvar isUsingWindows = process.platform == 'win32'\n\nvar fileNotExists = function(commandName, callback){\n    access(commandName, constants.F_OK,\n    function(err){\n        callback(!err);\n    });\n};\n\nvar fileNotExistsSync = function(commandName){\n    try{\n        accessSync(commandName, constants.F_OK);\n        return false;\n    }catch(e){\n        return true;\n    }\n};\n\nvar localExecutable = function(commandName, callback){\n    access(commandName, constants.F_OK | constants.X_OK,\n        function(err){\n        callback(null, !err);\n    });\n};\n\nvar localExecutableSync = function(commandName){\n    try{\n        accessSync(commandName, constants.F_OK | constants.X_OK);\n        return true;\n    }catch(e){\n        return false;\n    }\n}\n\nvar commandExistsUnix = function(commandName, cleanedCommandName, callback) {\n\n    fileNotExists(commandName, function(isFile){\n\n        if(!isFile){\n            var child = exec('command -v ' + cleanedCommandName +\n                  ' 2>/dev/null' +\n                  ' && { echo >&1 ' + cleanedCommandName + '; exit 0; }',\n                  function (error, stdout, stderr) {\n                      callback(null, !!stdout);\n                  });\n            return;\n        }\n\n        localExecutable(commandName, callback);\n    });\n\n}\n\nvar commandExistsWindows = function(commandName, cleanedCommandName, callback) {\n  // Regex from Julio from: https://stackoverflow.com/questions/51494579/regex-windows-path-validator\n  if (!(/^(?!(?:.*\\s|.*\\.|\\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:\"\\|\\?\\*\\n])+(?:\\/\\/|\\/|\\\\\\\\|\\\\)?)+$/m.test(commandName))) {\n    callback(null, false);\n    return;\n  }\n  var child = exec('where ' + cleanedCommandName,\n    function (error) {\n      if (error !== null){\n        callback(null, false);\n      } else {\n        callback(null, true);\n      }\n    }\n  )\n}\n\nvar commandExistsUnixSync = function(commandName, cleanedCommandName) {\n  if(fileNotExistsSync(commandName)){\n      try {\n        var stdout = execSync('command -v ' + cleanedCommandName +\n              ' 2>/dev/null' +\n              ' && { echo >&1 ' + cleanedCommandName + '; exit 0; }'\n              );\n        return !!stdout;\n      } catch (error) {\n        return false;\n      }\n  }\n  return localExecutableSync(commandName);\n}\n\nvar commandExistsWindowsSync = function(commandName, cleanedCommandName, callback) {\n  // Regex from Julio from: https://stackoverflow.com/questions/51494579/regex-windows-path-validator\n  if (!(/^(?!(?:.*\\s|.*\\.|\\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:\"\\|\\?\\*\\n])+(?:\\/\\/|\\/|\\\\\\\\|\\\\)?)+$/m.test(commandName))) {\n    return false;\n  }\n  try {\n      var stdout = execSync('where ' + cleanedCommandName, {stdio: []});\n      return !!stdout;\n  } catch (error) {\n      return false;\n  }\n}\n\nvar cleanInput = function(s) {\n  if (/[^A-Za-z0-9_\\/:=-]/.test(s)) {\n    s = \"'\"+s.replace(/'/g,\"'\\\\''\")+\"'\";\n    s = s.replace(/^(?:'')+/g, '') // unduplicate single-quote at the beginning\n      .replace(/\\\\'''/g, \"\\\\'\" ); // remove non-escaped single-quote if there are enclosed between 2 escaped\n  }\n  return s;\n}\n\nif (isUsingWindows) {\n  cleanInput = function(s) {\n    var isPathName = /[\\\\]/.test(s);\n    if (isPathName) {\n      var dirname = '\"' + path.dirname(s) + '\"';\n      var basename = '\"' + path.basename(s) + '\"';\n      return dirname + ':' + basename;\n    }\n    return '\"' + s + '\"';\n  }\n}\n\nmodule.exports = function commandExists(commandName, callback) {\n  var cleanedCommandName = cleanInput(commandName);\n  if (!callback && typeof Promise !== 'undefined') {\n    return new Promise(function(resolve, reject){\n      commandExists(commandName, function(error, output) {\n        if (output) {\n          resolve(commandName);\n        } else {\n          reject(error);\n        }\n      });\n    });\n  }\n  if (isUsingWindows) {\n    commandExistsWindows(commandName, cleanedCommandName, callback);\n  } else {\n    commandExistsUnix(commandName, cleanedCommandName, callback);\n  }\n};\n\nmodule.exports.sync = function(commandName) {\n  var cleanedCommandName = cleanInput(commandName);\n  if (isUsingWindows) {\n    return commandExistsWindowsSync(commandName, cleanedCommandName);\n  } else {\n    return commandExistsUnixSync(commandName, cleanedCommandName);\n  }\n};\n", "var Stream = require('stream');\nif (process.env.READABLE_STREAM === 'disable' && Stream) {\n  module.exports = Stream.Readable;\n  Object.assign(module.exports, Stream);\n  module.exports.Stream = Stream;\n} else {\n  exports = module.exports = require('./lib/_stream_readable.js');\n  exports.Stream = Stream || exports;\n  exports.Readable = exports;\n  exports.Writable = require('./lib/_stream_writable.js');\n  exports.Duplex = require('./lib/_stream_duplex.js');\n  exports.Transform = require('./lib/_stream_transform.js');\n  exports.PassThrough = require('./lib/_stream_passthrough.js');\n  exports.finished = require('./lib/internal/streams/end-of-stream.js');\n  exports.pipeline = require('./lib/internal/streams/pipeline.js');\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n'use strict';\n\nmodule.exports = Readable;\n/*<replacement>*/\n\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n/*<replacement>*/\n\nvar EE = require('events').EventEmitter;\n\nvar EElistenerCount = function EElistenerCount(emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\n\n\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n\nvar Buffer = require('buffer').Buffer;\n\nvar OurUint8Array = global.Uint8Array || function () {};\n\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\n\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n/*<replacement>*/\n\n\nvar debugUtil = require('util');\n\nvar debug;\n\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function debug() {};\n}\n/*</replacement>*/\n\n\nvar BufferList = require('./internal/streams/buffer_list');\n\nvar destroyImpl = require('./internal/streams/destroy');\n\nvar _require = require('./internal/streams/state'),\n    getHighWaterMark = _require.getHighWaterMark;\n\nvar _require$codes = require('../errors').codes,\n    ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n    ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF,\n    ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT; // Lazy loaded to improve the startup performance.\n\n\nvar StringDecoder;\nvar createReadableStreamAsyncIterator;\nvar from;\n\nrequire('inherits')(Readable, Stream);\n\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\n\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn); // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\n\nfunction ReadableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {}; // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex; // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode; // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n\n  this.highWaterMark = getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex); // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false; // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n\n  this.sync = true; // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n  this.paused = true; // Should close be emitted on destroy. Defaults to true.\n\n  this.emitClose = options.emitClose !== false; // Should .destroy() be called after 'end' (and potentially 'finish')\n\n  this.autoDestroy = !!options.autoDestroy; // has it been destroyed\n\n  this.destroyed = false; // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n\n  this.defaultEncoding = options.defaultEncoding || 'utf8'; // the number of writers that are awaiting a drain event in .pipe()s\n\n  this.awaitDrain = 0; // if true, a maybeReadMore has been scheduled\n\n  this.readingMore = false;\n  this.decoder = null;\n  this.encoding = null;\n\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\n\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n  if (!(this instanceof Readable)) return new Readable(options); // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the ReadableState constructor, at least with V8 6.5\n\n  var isDuplex = this instanceof Duplex;\n  this._readableState = new ReadableState(options, this, isDuplex); // legacy\n\n  this.readable = true;\n\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n\n  Stream.call(this);\n}\n\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined) {\n      return false;\n    }\n\n    return this._readableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    } // backward compatibility, the user is explicitly\n    // managing destroyed\n\n\n    this._readableState.destroyed = value;\n  }\n});\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\n\nReadable.prototype._destroy = function (err, cb) {\n  cb(err);\n}; // Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\n\n\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n}; // Unshift should *always* be something directly out of read()\n\n\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\n\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  debug('readableAddChunk', chunk);\n  var state = stream._readableState;\n\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n\n    if (er) {\n      errorOrDestroy(stream, er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n\n      if (addToFront) {\n        if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT());else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF());\n      } else if (state.destroyed) {\n        return false;\n      } else {\n        state.reading = false;\n\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n      maybeReadMore(stream, state);\n    }\n  } // We can push more data if we are below the highWaterMark.\n  // Also, if we have no data yet, we can stand some more bytes.\n  // This is to work around cases where hwm=0, such as the repl.\n\n\n  return !state.ended && (state.length < state.highWaterMark || state.length === 0);\n}\n\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    state.awaitDrain = 0;\n    stream.emit('data', chunk);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n    if (state.needReadable) emitReadable(stream);\n  }\n\n  maybeReadMore(stream, state);\n}\n\nfunction chunkInvalid(state, chunk) {\n  var er;\n\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk);\n  }\n\n  return er;\n}\n\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n}; // backwards compatibility.\n\n\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  var decoder = new StringDecoder(enc);\n  this._readableState.decoder = decoder; // If setEncoding(null), decoder.encoding equals utf8\n\n  this._readableState.encoding = this._readableState.decoder.encoding; // Iterate over current buffer to convert already stored Buffers:\n\n  var p = this._readableState.buffer.head;\n  var content = '';\n\n  while (p !== null) {\n    content += decoder.write(p.data);\n    p = p.next;\n  }\n\n  this._readableState.buffer.clear();\n\n  if (content !== '') this._readableState.buffer.push(content);\n  this._readableState.length = content.length;\n  return this;\n}; // Don't raise the hwm > 1GB\n\n\nvar MAX_HWM = 0x40000000;\n\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    // TODO(ronag): Throw ERR_VALUE_OUT_OF_RANGE.\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n\n  return n;\n} // This function is designed to be inlinable, so please take care when making\n// changes to the function body.\n\n\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  } // If we're asking for more than the current hwm, then raise the hwm.\n\n\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n; // Don't have enough\n\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n\n  return state.length;\n} // you can override either this method, or the async _read(n) below.\n\n\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n  if (n !== 0) state.emittedReadable = false; // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n\n  if (n === 0 && state.needReadable && ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n\n  n = howMuchToRead(n, state); // if we've ended, and we're now clear, then finish it up.\n\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  } // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n  // if we need a readable event, then we need to do some reading.\n\n\n  var doRead = state.needReadable;\n  debug('need readable', doRead); // if we currently have less than the highWaterMark, then also read some\n\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  } // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n\n\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true; // if the length is currently zero, then we *need* a readable event.\n\n    if (state.length === 0) state.needReadable = true; // call internal read method\n\n    this._read(state.highWaterMark);\n\n    state.sync = false; // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n\n  if (ret === null) {\n    state.needReadable = state.length <= state.highWaterMark;\n    n = 0;\n  } else {\n    state.length -= n;\n    state.awaitDrain = 0;\n  }\n\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true; // If we tried to read() past the EOF, then emit end on the next tick.\n\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n\n  if (ret !== null) this.emit('data', ret);\n  return ret;\n};\n\nfunction onEofChunk(stream, state) {\n  debug('onEofChunk');\n  if (state.ended) return;\n\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n\n  state.ended = true;\n\n  if (state.sync) {\n    // if we are sync, wait until next tick to emit the data.\n    // Otherwise we risk emitting data in the flow()\n    // the readable code triggers during a read() call\n    emitReadable(stream);\n  } else {\n    // emit 'readable' now to make sure it gets picked up.\n    state.needReadable = false;\n\n    if (!state.emittedReadable) {\n      state.emittedReadable = true;\n      emitReadable_(stream);\n    }\n  }\n} // Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\n\n\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  debug('emitReadable', state.needReadable, state.emittedReadable);\n  state.needReadable = false;\n\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    process.nextTick(emitReadable_, stream);\n  }\n}\n\nfunction emitReadable_(stream) {\n  var state = stream._readableState;\n  debug('emitReadable_', state.destroyed, state.length, state.ended);\n\n  if (!state.destroyed && (state.length || state.ended)) {\n    stream.emit('readable');\n    state.emittedReadable = false;\n  } // The stream needs another readable event if\n  // 1. It is not flowing, as the flow mechanism will take\n  //    care of it.\n  // 2. It is not ended.\n  // 3. It is below the highWaterMark, so we can schedule\n  //    another readable later.\n\n\n  state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark;\n  flow(stream);\n} // at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\n\n\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    process.nextTick(maybeReadMore_, stream, state);\n  }\n}\n\nfunction maybeReadMore_(stream, state) {\n  // Attempt to read more data if we should.\n  //\n  // The conditions for reading more data are (one of):\n  // - Not enough data buffered (state.length < state.highWaterMark). The loop\n  //   is responsible for filling the buffer with enough data if such data\n  //   is available. If highWaterMark is 0 and we are not in the flowing mode\n  //   we should _not_ attempt to buffer any extra data. We'll get more data\n  //   when the stream consumer calls read() instead.\n  // - No data in the buffer, and the stream is in flowing mode. In this mode\n  //   the loop below is responsible for ensuring read() is called. Failing to\n  //   call read here would abort the flow and there's no other mechanism for\n  //   continuing the flow if the stream consumer has just subscribed to the\n  //   'data' event.\n  //\n  // In addition to the above conditions to keep reading data, the following\n  // conditions prevent the data from being read:\n  // - The stream has ended (state.ended).\n  // - There is already a pending 'read' operation (state.reading). This is a\n  //   case where the the stream has called the implementation defined _read()\n  //   method, but they are processing the call asynchronously and have _not_\n  //   called push() with new data. In this case we skip performing more\n  //   read()s. The execution ends in this method again after the _read() ends\n  //   up calling push() with more data.\n  while (!state.reading && !state.ended && (state.length < state.highWaterMark || state.flowing && state.length === 0)) {\n    var len = state.length;\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length) // didn't get any data, stop spinning.\n      break;\n  }\n\n  state.readingMore = false;\n} // abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\n\n\nReadable.prototype._read = function (n) {\n  errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'));\n};\n\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) process.nextTick(endFn);else src.once('end', endFn);\n  dest.on('unpipe', onunpipe);\n\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n\n  function onend() {\n    debug('onend');\n    dest.end();\n  } // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n\n\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n  var cleanedUp = false;\n\n  function cleanup() {\n    debug('cleanup'); // cleanup event handlers once the pipe is broken\n\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n    cleanedUp = true; // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n\n  src.on('data', ondata);\n\n  function ondata(chunk) {\n    debug('ondata');\n    var ret = dest.write(chunk);\n    debug('dest.write', ret);\n\n    if (ret === false) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n      }\n\n      src.pause();\n    }\n  } // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n\n\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er);\n  } // Make sure our error handler is attached before userland ones.\n\n\n  prependListener(dest, 'error', onerror); // Both close and finish should trigger unpipe, but only once.\n\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n\n  dest.once('close', onclose);\n\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n\n  dest.once('finish', onfinish);\n\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  } // tell the dest that it's being piped to\n\n\n  dest.emit('pipe', src); // start the flow if it hasn't been started already.\n\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n\n  return dest;\n};\n\nfunction pipeOnDrain(src) {\n  return function pipeOnDrainFunctionResult() {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\n\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = {\n    hasUnpiped: false\n  }; // if we're not piping anywhere, then do nothing.\n\n  if (state.pipesCount === 0) return this; // just one destination.  most common case.\n\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n    if (!dest) dest = state.pipes; // got a match.\n\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  } // slow case. multiple pipe destinations.\n\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n\n    for (var i = 0; i < len; i++) {\n      dests[i].emit('unpipe', this, {\n        hasUnpiped: false\n      });\n    }\n\n    return this;\n  } // try to find the right one.\n\n\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n  dest.emit('unpipe', this, unpipeInfo);\n  return this;\n}; // set up data events if they are asked for\n// Ensure readable listeners eventually get something\n\n\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n  var state = this._readableState;\n\n  if (ev === 'data') {\n    // update readableListening so that resume() may be a no-op\n    // a few lines down. This is needed to support once('readable').\n    state.readableListening = this.listenerCount('readable') > 0; // Try start flowing on next tick if stream isn't explicitly paused\n\n    if (state.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.flowing = false;\n      state.emittedReadable = false;\n      debug('on readable', state.length, state.reading);\n\n      if (state.length) {\n        emitReadable(this);\n      } else if (!state.reading) {\n        process.nextTick(nReadingNextTick, this);\n      }\n    }\n  }\n\n  return res;\n};\n\nReadable.prototype.addListener = Readable.prototype.on;\n\nReadable.prototype.removeListener = function (ev, fn) {\n  var res = Stream.prototype.removeListener.call(this, ev, fn);\n\n  if (ev === 'readable') {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n\n  return res;\n};\n\nReadable.prototype.removeAllListeners = function (ev) {\n  var res = Stream.prototype.removeAllListeners.apply(this, arguments);\n\n  if (ev === 'readable' || ev === undefined) {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n\n  return res;\n};\n\nfunction updateReadableListening(self) {\n  var state = self._readableState;\n  state.readableListening = self.listenerCount('readable') > 0;\n\n  if (state.resumeScheduled && !state.paused) {\n    // flowing needs to be set to true now, otherwise\n    // the upcoming resume will not flow.\n    state.flowing = true; // crude way to check if we should resume\n  } else if (self.listenerCount('data') > 0) {\n    self.resume();\n  }\n}\n\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n} // pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\n\n\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n\n  if (!state.flowing) {\n    debug('resume'); // we flow only if there is no one listening\n    // for readable, but we still have to call\n    // resume()\n\n    state.flowing = !state.readableListening;\n    resume(this, state);\n  }\n\n  state.paused = false;\n  return this;\n};\n\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    process.nextTick(resume_, stream, state);\n  }\n}\n\nfunction resume_(stream, state) {\n  debug('resume', state.reading);\n\n  if (!state.reading) {\n    stream.read(0);\n  }\n\n  state.resumeScheduled = false;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\n\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n\n  if (this._readableState.flowing !== false) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n\n  this._readableState.paused = true;\n  return this;\n};\n\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n\n  while (state.flowing && stream.read() !== null) {\n    ;\n  }\n} // wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\n\n\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n\n  var state = this._readableState;\n  var paused = false;\n  stream.on('end', function () {\n    debug('wrapped end');\n\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n\n    _this.push(null);\n  });\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk); // don't skip over falsy values in objectMode\n\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n\n    var ret = _this.push(chunk);\n\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  }); // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function methodWrap(method) {\n        return function methodWrapReturnFunction() {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  } // proxy certain important events.\n\n\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  } // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n\n\n  this._read = function (n) {\n    debug('wrapped _read', n);\n\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n\n  return this;\n};\n\nif (typeof Symbol === 'function') {\n  Readable.prototype[Symbol.asyncIterator] = function () {\n    if (createReadableStreamAsyncIterator === undefined) {\n      createReadableStreamAsyncIterator = require('./internal/streams/async_iterator');\n    }\n\n    return createReadableStreamAsyncIterator(this);\n  };\n}\n\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.highWaterMark;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState && this._readableState.buffer;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableFlowing', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.flowing;\n  },\n  set: function set(state) {\n    if (this._readableState) {\n      this._readableState.flowing = state;\n    }\n  }\n}); // exposed for testing purposes only.\n\nReadable._fromList = fromList;\nObject.defineProperty(Readable.prototype, 'readableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.length;\n  }\n}); // Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\n\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.first();else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = state.buffer.consume(n, state.decoder);\n  }\n  return ret;\n}\n\nfunction endReadable(stream) {\n  var state = stream._readableState;\n  debug('endReadable', state.endEmitted);\n\n  if (!state.endEmitted) {\n    state.ended = true;\n    process.nextTick(endReadableNT, state, stream);\n  }\n}\n\nfunction endReadableNT(state, stream) {\n  debug('endReadableNT', state.endEmitted, state.length); // Check that we didn't get one last unshift.\n\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n\n    if (state.autoDestroy) {\n      // In case of duplex streams we need a way to detect\n      // if the writable side is ready for autoDestroy as well\n      var wState = stream._writableState;\n\n      if (!wState || wState.autoDestroy && wState.finished) {\n        stream.destroy();\n      }\n    }\n  }\n}\n\nif (typeof Symbol === 'function') {\n  Readable.from = function (iterable, opts) {\n    if (from === undefined) {\n      from = require('./internal/streams/from');\n    }\n\n    return from(Readable, iterable, opts);\n  };\n}\n\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n\n  return -1;\n}", "module.exports = require('stream');\n", "'use strict';\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nvar _require = require('buffer'),\n    Buffer = _require.Buffer;\n\nvar _require2 = require('util'),\n    inspect = _require2.inspect;\n\nvar custom = inspect && inspect.custom || 'inspect';\n\nfunction copyBuffer(src, target, offset) {\n  Buffer.prototype.copy.call(src, target, offset);\n}\n\nmodule.exports =\n/*#__PURE__*/\nfunction () {\n  function BufferList() {\n    _classCallCheck(this, BufferList);\n\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  _createClass(BufferList, [{\n    key: \"push\",\n    value: function push(v) {\n      var entry = {\n        data: v,\n        next: null\n      };\n      if (this.length > 0) this.tail.next = entry;else this.head = entry;\n      this.tail = entry;\n      ++this.length;\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(v) {\n      var entry = {\n        data: v,\n        next: this.head\n      };\n      if (this.length === 0) this.tail = entry;\n      this.head = entry;\n      ++this.length;\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      if (this.length === 0) return;\n      var ret = this.head.data;\n      if (this.length === 1) this.head = this.tail = null;else this.head = this.head.next;\n      --this.length;\n      return ret;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = this.tail = null;\n      this.length = 0;\n    }\n  }, {\n    key: \"join\",\n    value: function join(s) {\n      if (this.length === 0) return '';\n      var p = this.head;\n      var ret = '' + p.data;\n\n      while (p = p.next) {\n        ret += s + p.data;\n      }\n\n      return ret;\n    }\n  }, {\n    key: \"concat\",\n    value: function concat(n) {\n      if (this.length === 0) return Buffer.alloc(0);\n      var ret = Buffer.allocUnsafe(n >>> 0);\n      var p = this.head;\n      var i = 0;\n\n      while (p) {\n        copyBuffer(p.data, ret, i);\n        i += p.data.length;\n        p = p.next;\n      }\n\n      return ret;\n    } // Consumes a specified amount of bytes or characters from the buffered data.\n\n  }, {\n    key: \"consume\",\n    value: function consume(n, hasStrings) {\n      var ret;\n\n      if (n < this.head.data.length) {\n        // `slice` is the same for buffers and strings.\n        ret = this.head.data.slice(0, n);\n        this.head.data = this.head.data.slice(n);\n      } else if (n === this.head.data.length) {\n        // First chunk is a perfect match.\n        ret = this.shift();\n      } else {\n        // Result spans more than one buffer.\n        ret = hasStrings ? this._getString(n) : this._getBuffer(n);\n      }\n\n      return ret;\n    }\n  }, {\n    key: \"first\",\n    value: function first() {\n      return this.head.data;\n    } // Consumes a specified amount of characters from the buffered data.\n\n  }, {\n    key: \"_getString\",\n    value: function _getString(n) {\n      var p = this.head;\n      var c = 1;\n      var ret = p.data;\n      n -= ret.length;\n\n      while (p = p.next) {\n        var str = p.data;\n        var nb = n > str.length ? str.length : n;\n        if (nb === str.length) ret += str;else ret += str.slice(0, n);\n        n -= nb;\n\n        if (n === 0) {\n          if (nb === str.length) {\n            ++c;\n            if (p.next) this.head = p.next;else this.head = this.tail = null;\n          } else {\n            this.head = p;\n            p.data = str.slice(nb);\n          }\n\n          break;\n        }\n\n        ++c;\n      }\n\n      this.length -= c;\n      return ret;\n    } // Consumes a specified amount of bytes from the buffered data.\n\n  }, {\n    key: \"_getBuffer\",\n    value: function _getBuffer(n) {\n      var ret = Buffer.allocUnsafe(n);\n      var p = this.head;\n      var c = 1;\n      p.data.copy(ret);\n      n -= p.data.length;\n\n      while (p = p.next) {\n        var buf = p.data;\n        var nb = n > buf.length ? buf.length : n;\n        buf.copy(ret, ret.length - n, 0, nb);\n        n -= nb;\n\n        if (n === 0) {\n          if (nb === buf.length) {\n            ++c;\n            if (p.next) this.head = p.next;else this.head = this.tail = null;\n          } else {\n            this.head = p;\n            p.data = buf.slice(nb);\n          }\n\n          break;\n        }\n\n        ++c;\n      }\n\n      this.length -= c;\n      return ret;\n    } // Make sure the linked list only shows the minimal necessary information.\n\n  }, {\n    key: custom,\n    value: function value(_, options) {\n      return inspect(this, _objectSpread({}, options, {\n        // Only inspect one level.\n        depth: 0,\n        // It should not recurse.\n        customInspect: false\n      }));\n    }\n  }]);\n\n  return BufferList;\n}();", "'use strict'; // undocumented cb() API, needed for core, not for public API\n\nfunction destroy(err, cb) {\n  var _this = this;\n\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        process.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorNT, this, err);\n      }\n    }\n\n    return this;\n  } // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  } // if this is a duplex stream mark the writable part as destroyed as well\n\n\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else {\n        process.nextTick(emitCloseNT, _this);\n      }\n    } else if (cb) {\n      process.nextTick(emitCloseNT, _this);\n      cb(err);\n    } else {\n      process.nextTick(emitCloseNT, _this);\n    }\n  });\n\n  return this;\n}\n\nfunction emitErrorAndCloseNT(self, err) {\n  emitErrorNT(self, err);\n  emitCloseNT(self);\n}\n\nfunction emitCloseNT(self) {\n  if (self._writableState && !self._writableState.emitClose) return;\n  if (self._readableState && !self._readableState.emitClose) return;\n  self.emit('close');\n}\n\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\n\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\n\nfunction errorOrDestroy(stream, err) {\n  // We have tests that rely on errors being emitted\n  // in the same tick, so changing this is semver major.\n  // For now when you opt-in to autoDestroy we allow\n  // the error to be emitted nextTick. In a future\n  // semver major update we should change the default to this.\n  var rState = stream._readableState;\n  var wState = stream._writableState;\n  if (rState && rState.autoDestroy || wState && wState.autoDestroy) stream.destroy(err);else stream.emit('error', err);\n}\n\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy,\n  errorOrDestroy: errorOrDestroy\n};", "'use strict';\n\nvar ERR_INVALID_OPT_VALUE = require('../../../errors').codes.ERR_INVALID_OPT_VALUE;\n\nfunction highWaterMarkFrom(options, isDuplex, duplexKey) {\n  return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null;\n}\n\nfunction getHighWaterMark(state, options, duplexKey, isDuplex) {\n  var hwm = highWaterMarkFrom(options, isDuplex, duplexKey);\n\n  if (hwm != null) {\n    if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {\n      var name = isDuplex ? duplexKey : 'highWaterMark';\n      throw new ERR_INVALID_OPT_VALUE(name, hwm);\n    }\n\n    return Math.floor(hwm);\n  } // Default value\n\n\n  return state.objectMode ? 16 : 16 * 1024;\n}\n\nmodule.exports = {\n  getHighWaterMark: getHighWaterMark\n};", "'use strict';\n\nconst codes = {};\n\nfunction createErrorType(code, message, Base) {\n  if (!Base) {\n    Base = Error\n  }\n\n  function getMessage (arg1, arg2, arg3) {\n    if (typeof message === 'string') {\n      return message\n    } else {\n      return message(arg1, arg2, arg3)\n    }\n  }\n\n  class NodeError extends Base {\n    constructor (arg1, arg2, arg3) {\n      super(getMessage(arg1, arg2, arg3));\n    }\n  }\n\n  NodeError.prototype.name = Base.name;\n  NodeError.prototype.code = code;\n\n  codes[code] = NodeError;\n}\n\n// https://github.com/nodejs/node/blob/v10.8.0/lib/internal/errors.js\nfunction oneOf(expected, thing) {\n  if (Array.isArray(expected)) {\n    const len = expected.length;\n    expected = expected.map((i) => String(i));\n    if (len > 2) {\n      return `one of ${thing} ${expected.slice(0, len - 1).join(', ')}, or ` +\n             expected[len - 1];\n    } else if (len === 2) {\n      return `one of ${thing} ${expected[0]} or ${expected[1]}`;\n    } else {\n      return `of ${thing} ${expected[0]}`;\n    }\n  } else {\n    return `of ${thing} ${String(expected)}`;\n  }\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith\nfunction startsWith(str, search, pos) {\n\treturn str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/endsWith\nfunction endsWith(str, search, this_len) {\n\tif (this_len === undefined || this_len > str.length) {\n\t\tthis_len = str.length;\n\t}\n\treturn str.substring(this_len - search.length, this_len) === search;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/includes\nfunction includes(str, search, start) {\n  if (typeof start !== 'number') {\n    start = 0;\n  }\n\n  if (start + search.length > str.length) {\n    return false;\n  } else {\n    return str.indexOf(search, start) !== -1;\n  }\n}\n\ncreateErrorType('ERR_INVALID_OPT_VALUE', function (name, value) {\n  return 'The value \"' + value + '\" is invalid for option \"' + name + '\"'\n}, TypeError);\ncreateErrorType('ERR_INVALID_ARG_TYPE', function (name, expected, actual) {\n  // determiner: 'must be' or 'must not be'\n  let determiner;\n  if (typeof expected === 'string' && startsWith(expected, 'not ')) {\n    determiner = 'must not be';\n    expected = expected.replace(/^not /, '');\n  } else {\n    determiner = 'must be';\n  }\n\n  let msg;\n  if (endsWith(name, ' argument')) {\n    // For cases like 'first argument'\n    msg = `The ${name} ${determiner} ${oneOf(expected, 'type')}`;\n  } else {\n    const type = includes(name, '.') ? 'property' : 'argument';\n    msg = `The \"${name}\" ${type} ${determiner} ${oneOf(expected, 'type')}`;\n  }\n\n  msg += `. Received type ${typeof actual}`;\n  return msg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF');\ncreateErrorType('ERR_METHOD_NOT_IMPLEMENTED', function (name) {\n  return 'The ' + name + ' method is not implemented'\n});\ncreateErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close');\ncreateErrorType('ERR_STREAM_DESTROYED', function (name) {\n  return 'Cannot call ' + name + ' after a stream was destroyed';\n});\ncreateErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times');\ncreateErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable');\ncreateErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end');\ncreateErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError);\ncreateErrorType('ERR_UNKNOWN_ENCODING', function (arg) {\n  return 'Unknown encoding: ' + arg\n}, TypeError);\ncreateErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event');\n\nmodule.exports.codes = codes;\n", "try {\n  var util = require('util');\n  /* istanbul ignore next */\n  if (typeof util.inherits !== 'function') throw '';\n  module.exports = util.inherits;\n} catch (e) {\n  /* istanbul ignore next */\n  module.exports = require('./inherits_browser.js');\n}\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n'use strict';\n/*<replacement>*/\n\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n\n  for (var key in obj) {\n    keys.push(key);\n  }\n\n  return keys;\n};\n/*</replacement>*/\n\n\nmodule.exports = Duplex;\n\nvar Readable = require('./_stream_readable');\n\nvar Writable = require('./_stream_writable');\n\nrequire('inherits')(Duplex, Readable);\n\n{\n  // Allow the keys array to be GC'ed.\n  var keys = objectKeys(Writable.prototype);\n\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\n\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n  Readable.call(this, options);\n  Writable.call(this, options);\n  this.allowHalfOpen = true;\n\n  if (options) {\n    if (options.readable === false) this.readable = false;\n    if (options.writable === false) this.writable = false;\n\n    if (options.allowHalfOpen === false) {\n      this.allowHalfOpen = false;\n      this.once('end', onend);\n    }\n  }\n}\n\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n}); // the no-half-open enforcer\n\nfunction onend() {\n  // If the writable side ended, then we're ok.\n  if (this._writableState.ended) return; // no more data can be written.\n  // But allow more writes to happen in this tick.\n\n  process.nextTick(onEndNT, this);\n}\n\nfunction onEndNT(self) {\n  self.end();\n}\n\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    } // backward compatibility, the user is explicitly\n    // managing destroyed\n\n\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n'use strict';\n\nmodule.exports = Writable;\n/* <replacement> */\n\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n} // It seems a linked list but it is not\n// there will be only 2 of these for each stream\n\n\nfunction CorkedRequest(state) {\n  var _this = this;\n\n  this.next = null;\n  this.entry = null;\n\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\n\n\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n/*<replacement>*/\n\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\n\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n\nvar Buffer = require('buffer').Buffer;\n\nvar OurUint8Array = global.Uint8Array || function () {};\n\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\n\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\nvar destroyImpl = require('./internal/streams/destroy');\n\nvar _require = require('./internal/streams/state'),\n    getHighWaterMark = _require.getHighWaterMark;\n\nvar _require$codes = require('../errors').codes,\n    ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n    ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n    ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE,\n    ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED,\n    ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES,\n    ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END,\n    ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING;\n\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\n\nrequire('inherits')(Writable, Stream);\n\nfunction nop() {}\n\nfunction WritableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {}; // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream,\n  // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.\n\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex; // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode; // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n\n  this.highWaterMark = getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex); // if _final has been called\n\n  this.finalCalled = false; // drain event flag.\n\n  this.needDrain = false; // at the start of calling end()\n\n  this.ending = false; // when end() has been called, and returned\n\n  this.ended = false; // when 'finish' is emitted\n\n  this.finished = false; // has it been destroyed\n\n  this.destroyed = false; // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode; // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n\n  this.defaultEncoding = options.defaultEncoding || 'utf8'; // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n\n  this.length = 0; // a flag to see when we're in the middle of a write.\n\n  this.writing = false; // when true all writes will be buffered until .uncork() call\n\n  this.corked = 0; // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n\n  this.sync = true; // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n\n  this.bufferProcessing = false; // the callback that's passed to _write(chunk,cb)\n\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  }; // the callback that the user supplies to write(chunk,encoding,cb)\n\n\n  this.writecb = null; // the amount that is being written when _write is called.\n\n  this.writelen = 0;\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null; // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n\n  this.pendingcb = 0; // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n\n  this.prefinished = false; // True if the error was already emitted and should not be thrown again\n\n  this.errorEmitted = false; // Should close be emitted on destroy. Defaults to true.\n\n  this.emitClose = options.emitClose !== false; // Should .destroy() be called after 'finish' (and potentially 'end')\n\n  this.autoDestroy = !!options.autoDestroy; // count buffered requests\n\n  this.bufferedRequestCount = 0; // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\n\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n\n  return out;\n};\n\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function writableStateBufferGetter() {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})(); // Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\n\n\nvar realHasInstance;\n\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function value(object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function realHasInstance(object) {\n    return object instanceof this;\n  };\n}\n\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex'); // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the WritableState constructor, at least with V8 6.5\n\n  var isDuplex = this instanceof Duplex;\n  if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options);\n  this._writableState = new WritableState(options, this, isDuplex); // legacy.\n\n  this.writable = true;\n\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n    if (typeof options.writev === 'function') this._writev = options.writev;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n\n  Stream.call(this);\n} // Otherwise people can pipe Writable streams, which is just wrong.\n\n\nWritable.prototype.pipe = function () {\n  errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE());\n};\n\nfunction writeAfterEnd(stream, cb) {\n  var er = new ERR_STREAM_WRITE_AFTER_END(); // TODO: defer error events consistently everywhere, not just the cb\n\n  errorOrDestroy(stream, er);\n  process.nextTick(cb, er);\n} // Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\n\n\nfunction validChunk(stream, state, chunk, cb) {\n  var er;\n\n  if (chunk === null) {\n    er = new ERR_STREAM_NULL_VALUES();\n  } else if (typeof chunk !== 'string' && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer'], chunk);\n  }\n\n  if (er) {\n    errorOrDestroy(stream, er);\n    process.nextTick(cb, er);\n    return false;\n  }\n\n  return true;\n}\n\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n  if (typeof cb !== 'function') cb = nop;\n  if (state.ending) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n  return ret;\n};\n\nWritable.prototype.cork = function () {\n  this._writableState.corked++;\n};\n\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n\n  if (state.corked) {\n    state.corked--;\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\n\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new ERR_UNKNOWN_ENCODING(encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\n\nObject.defineProperty(Writable.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\n\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n\n  return chunk;\n}\n\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n}); // if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\n\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n\n  var len = state.objectMode ? 1 : chunk.length;\n  state.length += len;\n  var ret = state.length < state.highWaterMark; // we must ensure that previous needDrain will not be reset to false.\n\n  if (!ret) state.needDrain = true;\n\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n\n  return ret;\n}\n\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'));else if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\n\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    process.nextTick(cb, er); // this can emit finish, and it will always happen\n    // after error\n\n    process.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er); // this can emit finish, but finish must\n    // always follow error\n\n    finishMaybe(stream, state);\n  }\n}\n\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\n\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n  if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK();\n  onwriteStateUpdate(state);\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state) || stream.destroyed;\n\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n\n    if (sync) {\n      process.nextTick(afterWrite, stream, state, finished, cb);\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\n\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n} // Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\n\n\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n} // if there's something in the buffer waiting, then process it\n\n\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n    var count = 0;\n    var allBuffers = true;\n\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n\n    buffer.allBuffers = allBuffers;\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish); // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--; // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n\n      if (state.writing) {\n        break;\n      }\n    }\n\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\n\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'));\n};\n\nWritable.prototype._writev = null;\n\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding); // .end() fully uncorks\n\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  } // ignore unnecessary end() calls.\n\n\n  if (!state.ending) endWritable(this, state, cb);\n  return this;\n};\n\nObject.defineProperty(Writable.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\n\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\n\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n\n    if (err) {\n      errorOrDestroy(stream, err);\n    }\n\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\n\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function' && !state.destroyed) {\n      state.pendingcb++;\n      state.finalCalled = true;\n      process.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\n\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n\n  if (need) {\n    prefinish(stream, state);\n\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n\n      if (state.autoDestroy) {\n        // In case of duplex streams we need a way to detect\n        // if the readable side is ready for autoDestroy as well\n        var rState = stream._readableState;\n\n        if (!rState || rState.autoDestroy && rState.endEmitted) {\n          stream.destroy();\n        }\n      }\n    }\n  }\n\n  return need;\n}\n\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n\n  if (cb) {\n    if (state.finished) process.nextTick(cb);else stream.once('finish', cb);\n  }\n\n  state.ended = true;\n  stream.writable = false;\n}\n\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  } // reuse the free corkReq.\n\n\n  state.corkedRequestsFree.next = corkReq;\n}\n\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._writableState === undefined) {\n      return false;\n    }\n\n    return this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    } // backward compatibility, the user is explicitly\n    // managing destroyed\n\n\n    this._writableState.destroyed = value;\n  }\n});\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\n\nWritable.prototype._destroy = function (err, cb) {\n  cb(err);\n};", "\n/**\n * For Node.js, simply re-export the core `util.deprecate` function.\n */\n\nmodule.exports = require('util').deprecate;\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\n/*</replacement>*/\n\nvar isEncoding = Buffer.isEncoding || function (encoding) {\n  encoding = '' + encoding;\n  switch (encoding && encoding.toLowerCase()) {\n    case 'hex':case 'utf8':case 'utf-8':case 'ascii':case 'binary':case 'base64':case 'ucs2':case 'ucs-2':case 'utf16le':case 'utf-16le':case 'raw':\n      return true;\n    default:\n      return false;\n  }\n};\n\nfunction _normalizeEncoding(enc) {\n  if (!enc) return 'utf8';\n  var retried;\n  while (true) {\n    switch (enc) {\n      case 'utf8':\n      case 'utf-8':\n        return 'utf8';\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return 'utf16le';\n      case 'latin1':\n      case 'binary':\n        return 'latin1';\n      case 'base64':\n      case 'ascii':\n      case 'hex':\n        return enc;\n      default:\n        if (retried) return; // undefined\n        enc = ('' + enc).toLowerCase();\n        retried = true;\n    }\n  }\n};\n\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n  var nenc = _normalizeEncoding(enc);\n  if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);\n  return nenc || enc;\n}\n\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n  this.encoding = normalizeEncoding(encoding);\n  var nb;\n  switch (this.encoding) {\n    case 'utf16le':\n      this.text = utf16Text;\n      this.end = utf16End;\n      nb = 4;\n      break;\n    case 'utf8':\n      this.fillLast = utf8FillLast;\n      nb = 4;\n      break;\n    case 'base64':\n      this.text = base64Text;\n      this.end = base64End;\n      nb = 3;\n      break;\n    default:\n      this.write = simpleWrite;\n      this.end = simpleEnd;\n      return;\n  }\n  this.lastNeed = 0;\n  this.lastTotal = 0;\n  this.lastChar = Buffer.allocUnsafe(nb);\n}\n\nStringDecoder.prototype.write = function (buf) {\n  if (buf.length === 0) return '';\n  var r;\n  var i;\n  if (this.lastNeed) {\n    r = this.fillLast(buf);\n    if (r === undefined) return '';\n    i = this.lastNeed;\n    this.lastNeed = 0;\n  } else {\n    i = 0;\n  }\n  if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n  return r || '';\n};\n\nStringDecoder.prototype.end = utf8End;\n\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function (buf) {\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n  this.lastNeed -= buf.length;\n};\n\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n  if (byte <= 0x7F) return 0;else if (byte >> 5 === 0x06) return 2;else if (byte >> 4 === 0x0E) return 3;else if (byte >> 3 === 0x1E) return 4;\n  return byte >> 6 === 0x02 ? -1 : -2;\n}\n\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n  var j = buf.length - 1;\n  if (j < i) return 0;\n  var nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 1;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 2;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) {\n      if (nb === 2) nb = 0;else self.lastNeed = nb - 3;\n    }\n    return nb;\n  }\n  return 0;\n}\n\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n  if ((buf[0] & 0xC0) !== 0x80) {\n    self.lastNeed = 0;\n    return '\\ufffd';\n  }\n  if (self.lastNeed > 1 && buf.length > 1) {\n    if ((buf[1] & 0xC0) !== 0x80) {\n      self.lastNeed = 1;\n      return '\\ufffd';\n    }\n    if (self.lastNeed > 2 && buf.length > 2) {\n      if ((buf[2] & 0xC0) !== 0x80) {\n        self.lastNeed = 2;\n        return '\\ufffd';\n      }\n    }\n  }\n}\n\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n  var p = this.lastTotal - this.lastNeed;\n  var r = utf8CheckExtraBytes(this, buf, p);\n  if (r !== undefined) return r;\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, p, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, p, 0, buf.length);\n  this.lastNeed -= buf.length;\n}\n\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n  var total = utf8CheckIncomplete(this, buf, i);\n  if (!this.lastNeed) return buf.toString('utf8', i);\n  this.lastTotal = total;\n  var end = buf.length - (total - this.lastNeed);\n  buf.copy(this.lastChar, 0, end);\n  return buf.toString('utf8', i, end);\n}\n\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + '\\ufffd';\n  return r;\n}\n\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n  if ((buf.length - i) % 2 === 0) {\n    var r = buf.toString('utf16le', i);\n    if (r) {\n      var c = r.charCodeAt(r.length - 1);\n      if (c >= 0xD800 && c <= 0xDBFF) {\n        this.lastNeed = 2;\n        this.lastTotal = 4;\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n        return r.slice(0, -1);\n      }\n    }\n    return r;\n  }\n  this.lastNeed = 1;\n  this.lastTotal = 2;\n  this.lastChar[0] = buf[buf.length - 1];\n  return buf.toString('utf16le', i, buf.length - 1);\n}\n\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) {\n    var end = this.lastTotal - this.lastNeed;\n    return r + this.lastChar.toString('utf16le', 0, end);\n  }\n  return r;\n}\n\nfunction base64Text(buf, i) {\n  var n = (buf.length - i) % 3;\n  if (n === 0) return buf.toString('base64', i);\n  this.lastNeed = 3 - n;\n  this.lastTotal = 3;\n  if (n === 1) {\n    this.lastChar[0] = buf[buf.length - 1];\n  } else {\n    this.lastChar[0] = buf[buf.length - 2];\n    this.lastChar[1] = buf[buf.length - 1];\n  }\n  return buf.toString('base64', i, buf.length - n);\n}\n\nfunction base64End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);\n  return r;\n}\n\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n  return buf.toString(this.encoding);\n}\n\nfunction simpleEnd(buf) {\n  return buf && buf.length ? this.write(buf) : '';\n}", "/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n", "'use strict';\n\nvar _Object$setPrototypeO;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar finished = require('./end-of-stream');\n\nvar kLastResolve = Symbol('lastResolve');\nvar kLastReject = Symbol('lastReject');\nvar kError = Symbol('error');\nvar kEnded = Symbol('ended');\nvar kLastPromise = Symbol('lastPromise');\nvar kHandlePromise = Symbol('handlePromise');\nvar kStream = Symbol('stream');\n\nfunction createIterResult(value, done) {\n  return {\n    value: value,\n    done: done\n  };\n}\n\nfunction readAndResolve(iter) {\n  var resolve = iter[kLastResolve];\n\n  if (resolve !== null) {\n    var data = iter[kStream].read(); // we defer if data is null\n    // we can be expecting either 'end' or\n    // 'error'\n\n    if (data !== null) {\n      iter[kLastPromise] = null;\n      iter[kLastResolve] = null;\n      iter[kLastReject] = null;\n      resolve(createIterResult(data, false));\n    }\n  }\n}\n\nfunction onReadable(iter) {\n  // we wait for the next tick, because it might\n  // emit an error with process.nextTick\n  process.nextTick(readAndResolve, iter);\n}\n\nfunction wrapForNext(lastPromise, iter) {\n  return function (resolve, reject) {\n    lastPromise.then(function () {\n      if (iter[kEnded]) {\n        resolve(createIterResult(undefined, true));\n        return;\n      }\n\n      iter[kHandlePromise](resolve, reject);\n    }, reject);\n  };\n}\n\nvar AsyncIteratorPrototype = Object.getPrototypeOf(function () {});\nvar ReadableStreamAsyncIteratorPrototype = Object.setPrototypeOf((_Object$setPrototypeO = {\n  get stream() {\n    return this[kStream];\n  },\n\n  next: function next() {\n    var _this = this;\n\n    // if we have detected an error in the meanwhile\n    // reject straight away\n    var error = this[kError];\n\n    if (error !== null) {\n      return Promise.reject(error);\n    }\n\n    if (this[kEnded]) {\n      return Promise.resolve(createIterResult(undefined, true));\n    }\n\n    if (this[kStream].destroyed) {\n      // We need to defer via nextTick because if .destroy(err) is\n      // called, the error will be emitted via nextTick, and\n      // we cannot guarantee that there is no error lingering around\n      // waiting to be emitted.\n      return new Promise(function (resolve, reject) {\n        process.nextTick(function () {\n          if (_this[kError]) {\n            reject(_this[kError]);\n          } else {\n            resolve(createIterResult(undefined, true));\n          }\n        });\n      });\n    } // if we have multiple next() calls\n    // we will wait for the previous Promise to finish\n    // this logic is optimized to support for await loops,\n    // where next() is only called once at a time\n\n\n    var lastPromise = this[kLastPromise];\n    var promise;\n\n    if (lastPromise) {\n      promise = new Promise(wrapForNext(lastPromise, this));\n    } else {\n      // fast path needed to support multiple this.push()\n      // without triggering the next() queue\n      var data = this[kStream].read();\n\n      if (data !== null) {\n        return Promise.resolve(createIterResult(data, false));\n      }\n\n      promise = new Promise(this[kHandlePromise]);\n    }\n\n    this[kLastPromise] = promise;\n    return promise;\n  }\n}, _defineProperty(_Object$setPrototypeO, Symbol.asyncIterator, function () {\n  return this;\n}), _defineProperty(_Object$setPrototypeO, \"return\", function _return() {\n  var _this2 = this;\n\n  // destroy(err, cb) is a private API\n  // we can guarantee we have that here, because we control the\n  // Readable class this is attached to\n  return new Promise(function (resolve, reject) {\n    _this2[kStream].destroy(null, function (err) {\n      if (err) {\n        reject(err);\n        return;\n      }\n\n      resolve(createIterResult(undefined, true));\n    });\n  });\n}), _Object$setPrototypeO), AsyncIteratorPrototype);\n\nvar createReadableStreamAsyncIterator = function createReadableStreamAsyncIterator(stream) {\n  var _Object$create;\n\n  var iterator = Object.create(ReadableStreamAsyncIteratorPrototype, (_Object$create = {}, _defineProperty(_Object$create, kStream, {\n    value: stream,\n    writable: true\n  }), _defineProperty(_Object$create, kLastResolve, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kLastReject, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kError, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kEnded, {\n    value: stream._readableState.endEmitted,\n    writable: true\n  }), _defineProperty(_Object$create, kHandlePromise, {\n    value: function value(resolve, reject) {\n      var data = iterator[kStream].read();\n\n      if (data) {\n        iterator[kLastPromise] = null;\n        iterator[kLastResolve] = null;\n        iterator[kLastReject] = null;\n        resolve(createIterResult(data, false));\n      } else {\n        iterator[kLastResolve] = resolve;\n        iterator[kLastReject] = reject;\n      }\n    },\n    writable: true\n  }), _Object$create));\n  iterator[kLastPromise] = null;\n  finished(stream, function (err) {\n    if (err && err.code !== 'ERR_STREAM_PREMATURE_CLOSE') {\n      var reject = iterator[kLastReject]; // reject if we are waiting for data in the Promise\n      // returned by next() and store the error\n\n      if (reject !== null) {\n        iterator[kLastPromise] = null;\n        iterator[kLastResolve] = null;\n        iterator[kLastReject] = null;\n        reject(err);\n      }\n\n      iterator[kError] = err;\n      return;\n    }\n\n    var resolve = iterator[kLastResolve];\n\n    if (resolve !== null) {\n      iterator[kLastPromise] = null;\n      iterator[kLastResolve] = null;\n      iterator[kLastReject] = null;\n      resolve(createIterResult(undefined, true));\n    }\n\n    iterator[kEnded] = true;\n  });\n  stream.on('readable', onReadable.bind(null, iterator));\n  return iterator;\n};\n\nmodule.exports = createReadableStreamAsyncIterator;", "// Ported from https://github.com/mafintosh/end-of-stream with\n// permission from the author, <PERSON> (@mafintosh).\n'use strict';\n\nvar ERR_STREAM_PREMATURE_CLOSE = require('../../../errors').codes.ERR_STREAM_PREMATURE_CLOSE;\n\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    callback.apply(this, args);\n  };\n}\n\nfunction noop() {}\n\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\n\nfunction eos(stream, opts, callback) {\n  if (typeof opts === 'function') return eos(stream, null, opts);\n  if (!opts) opts = {};\n  callback = once(callback || noop);\n  var readable = opts.readable || opts.readable !== false && stream.readable;\n  var writable = opts.writable || opts.writable !== false && stream.writable;\n\n  var onlegacyfinish = function onlegacyfinish() {\n    if (!stream.writable) onfinish();\n  };\n\n  var writableEnded = stream._writableState && stream._writableState.finished;\n\n  var onfinish = function onfinish() {\n    writable = false;\n    writableEnded = true;\n    if (!readable) callback.call(stream);\n  };\n\n  var readableEnded = stream._readableState && stream._readableState.endEmitted;\n\n  var onend = function onend() {\n    readable = false;\n    readableEnded = true;\n    if (!writable) callback.call(stream);\n  };\n\n  var onerror = function onerror(err) {\n    callback.call(stream, err);\n  };\n\n  var onclose = function onclose() {\n    var err;\n\n    if (readable && !readableEnded) {\n      if (!stream._readableState || !stream._readableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n\n    if (writable && !writableEnded) {\n      if (!stream._writableState || !stream._writableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n  };\n\n  var onrequest = function onrequest() {\n    stream.req.on('finish', onfinish);\n  };\n\n  if (isRequest(stream)) {\n    stream.on('complete', onfinish);\n    stream.on('abort', onclose);\n    if (stream.req) onrequest();else stream.on('request', onrequest);\n  } else if (writable && !stream._writableState) {\n    // legacy streams\n    stream.on('end', onlegacyfinish);\n    stream.on('close', onlegacyfinish);\n  }\n\n  stream.on('end', onend);\n  stream.on('finish', onfinish);\n  if (opts.error !== false) stream.on('error', onerror);\n  stream.on('close', onclose);\n  return function () {\n    stream.removeListener('complete', onfinish);\n    stream.removeListener('abort', onclose);\n    stream.removeListener('request', onrequest);\n    if (stream.req) stream.req.removeListener('finish', onfinish);\n    stream.removeListener('end', onlegacyfinish);\n    stream.removeListener('close', onlegacyfinish);\n    stream.removeListener('finish', onfinish);\n    stream.removeListener('end', onend);\n    stream.removeListener('error', onerror);\n    stream.removeListener('close', onclose);\n  };\n}\n\nmodule.exports = eos;", "'use strict';\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar ERR_INVALID_ARG_TYPE = require('../../../errors').codes.ERR_INVALID_ARG_TYPE;\n\nfunction from(Readable, iterable, opts) {\n  var iterator;\n\n  if (iterable && typeof iterable.next === 'function') {\n    iterator = iterable;\n  } else if (iterable && iterable[Symbol.asyncIterator]) iterator = iterable[Symbol.asyncIterator]();else if (iterable && iterable[Symbol.iterator]) iterator = iterable[Symbol.iterator]();else throw new ERR_INVALID_ARG_TYPE('iterable', ['Iterable'], iterable);\n\n  var readable = new Readable(_objectSpread({\n    objectMode: true\n  }, opts)); // Reading boolean to protect against _read\n  // being called before last iteration completion.\n\n  var reading = false;\n\n  readable._read = function () {\n    if (!reading) {\n      reading = true;\n      next();\n    }\n  };\n\n  function next() {\n    return _next2.apply(this, arguments);\n  }\n\n  function _next2() {\n    _next2 = _asyncToGenerator(function* () {\n      try {\n        var _ref = yield iterator.next(),\n            value = _ref.value,\n            done = _ref.done;\n\n        if (done) {\n          readable.push(null);\n        } else if (readable.push((yield value))) {\n          next();\n        } else {\n          reading = false;\n        }\n      } catch (err) {\n        readable.destroy(err);\n      }\n    });\n    return _next2.apply(this, arguments);\n  }\n\n  return readable;\n}\n\nmodule.exports = from;", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n'use strict';\n\nmodule.exports = Transform;\n\nvar _require$codes = require('../errors').codes,\n    ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n    ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING,\n    ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0;\n\nvar Duplex = require('./_stream_duplex');\n\nrequire('inherits')(Transform, Duplex);\n\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n  var cb = ts.writecb;\n\n  if (cb === null) {\n    return this.emit('error', new ERR_MULTIPLE_CALLBACK());\n  }\n\n  ts.writechunk = null;\n  ts.writecb = null;\n  if (data != null) // single equals check for both `null` and `undefined`\n    this.push(data);\n  cb(er);\n  var rs = this._readableState;\n  rs.reading = false;\n\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\n\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n  Duplex.call(this, options);\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  }; // start out asking for a readable event once data is transformed.\n\n  this._readableState.needReadable = true; // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n\n  this._readableState.sync = false;\n\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  } // When the writable side finishes, then flush out anything remaining.\n\n\n  this.on('prefinish', prefinish);\n}\n\nfunction prefinish() {\n  var _this = this;\n\n  if (typeof this._flush === 'function' && !this._readableState.destroyed) {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\n\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n}; // This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\n\n\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'));\n};\n\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n}; // Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\n\n\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n\n  if (ts.writechunk !== null && !ts.transforming) {\n    ts.transforming = true;\n\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\n\nTransform.prototype._destroy = function (err, cb) {\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n  });\n};\n\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n  if (data != null) // single equals check for both `null` and `undefined`\n    stream.push(data); // TODO(BridgeAR): Write a test for these two error cases\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n\n  if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0();\n  if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING();\n  return stream.push(null);\n}", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n'use strict';\n\nmodule.exports = PassThrough;\n\nvar Transform = require('./_stream_transform');\n\nrequire('inherits')(PassThrough, Transform);\n\nfunction PassThrough(options) {\n  if (!(this instanceof PassThrough)) return new PassThrough(options);\n  Transform.call(this, options);\n}\n\nPassThrough.prototype._transform = function (chunk, encoding, cb) {\n  cb(null, chunk);\n};", "// Ported from https://github.com/mafin<PERSON>h/pump with\n// permission from the author, <PERSON> (@mafintosh).\n'use strict';\n\nvar eos;\n\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    callback.apply(void 0, arguments);\n  };\n}\n\nvar _require$codes = require('../../../errors').codes,\n    ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS,\n    ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED;\n\nfunction noop(err) {\n  // Rethrow the error if it exists to avoid swallowing it\n  if (err) throw err;\n}\n\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\n\nfunction destroyer(stream, reading, writing, callback) {\n  callback = once(callback);\n  var closed = false;\n  stream.on('close', function () {\n    closed = true;\n  });\n  if (eos === undefined) eos = require('./end-of-stream');\n  eos(stream, {\n    readable: reading,\n    writable: writing\n  }, function (err) {\n    if (err) return callback(err);\n    closed = true;\n    callback();\n  });\n  var destroyed = false;\n  return function (err) {\n    if (closed) return;\n    if (destroyed) return;\n    destroyed = true; // request.destroy just do .end - .abort is what we want\n\n    if (isRequest(stream)) return stream.abort();\n    if (typeof stream.destroy === 'function') return stream.destroy();\n    callback(err || new ERR_STREAM_DESTROYED('pipe'));\n  };\n}\n\nfunction call(fn) {\n  fn();\n}\n\nfunction pipe(from, to) {\n  return from.pipe(to);\n}\n\nfunction popCallback(streams) {\n  if (!streams.length) return noop;\n  if (typeof streams[streams.length - 1] !== 'function') return noop;\n  return streams.pop();\n}\n\nfunction pipeline() {\n  for (var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++) {\n    streams[_key] = arguments[_key];\n  }\n\n  var callback = popCallback(streams);\n  if (Array.isArray(streams[0])) streams = streams[0];\n\n  if (streams.length < 2) {\n    throw new ERR_MISSING_ARGS('streams');\n  }\n\n  var error;\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1;\n    var writing = i > 0;\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err;\n      if (err) destroys.forEach(call);\n      if (reading) return;\n      destroys.forEach(call);\n      callback(error);\n    });\n  });\n  return streams.reduce(pipe);\n}\n\nmodule.exports = pipeline;", "// @flow\nimport type {FilePath, DependencySpecifier, SemverRange} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {\n  ModuleRequest,\n  PackageManager,\n  PackageInstaller,\n  InstallOptions,\n  Invalidations,\n} from './types';\nimport type {ResolveResult} from './types';\n\nimport {registerSerializableClass} from '@parcel/core';\nimport ThrowableDiagnostic, {\n  encodeJSONKeyComponent,\n  escapeMarkdown,\n  generateJSONCodeHighlights,\n  md,\n} from '@parcel/diagnostic';\nimport {NodeFS} from '@parcel/fs';\nimport nativeFS from 'fs';\nimport Module from 'module';\nimport path from 'path';\nimport semver from 'semver';\nimport logger from '@parcel/logger';\nimport nullthrows from 'nullthrows';\n\nimport {getModuleParts} from '@parcel/utils';\nimport {getConflictingLocalDependencies} from './utils';\nimport {installPackage} from './installPackage';\nimport pkg from '../package.json';\nimport {ResolverBase} from '@parcel/node-resolver-core';\nimport {pathToFileURL} from 'url';\n\n// Package.json fields. Must match package_json.rs.\nconst MAIN = 1 << 0;\nconst SOURCE = 1 << 2;\nconst ENTRIES =\n  MAIN |\n  (process.env.PARCEL_BUILD_ENV !== 'production' ||\n  process.env.PARCEL_SELF_BUILD\n    ? SOURCE\n    : 0);\n\n// There can be more than one instance of NodePackageManager, but node has only a single module cache.\n// Therefore, the resolution cache and the map of parent to child modules should also be global.\nconst cache = new Map<DependencySpecifier, ResolveResult>();\nconst children = new Map<FilePath, Set<DependencySpecifier>>();\nconst invalidationsCache = new Map<string, Invalidations>();\n\n// This implements a package manager for Node by monkey patching the Node require\n// algorithm so that it uses the specified FileSystem instead of the native one.\n// It also handles installing packages when they are required if not already installed.\n// See https://github.com/nodejs/node/blob/master/lib/internal/modules/cjs/loader.js\n// for reference to Node internals.\nexport class NodePackageManager implements PackageManager {\n  fs: FileSystem;\n  projectRoot: FilePath;\n  installer: ?PackageInstaller;\n  resolver: ResolverBase;\n\n  constructor(\n    fs: FileSystem,\n    projectRoot: FilePath,\n    installer?: ?PackageInstaller,\n  ) {\n    this.fs = fs;\n    this.projectRoot = projectRoot;\n    this.installer = installer;\n  }\n\n  _createResolver(): ResolverBase {\n    return new ResolverBase(this.projectRoot, {\n      fs:\n        this.fs instanceof NodeFS && process.versions.pnp == null\n          ? undefined\n          : {\n              canonicalize: path => this.fs.realpathSync(path),\n              read: path => this.fs.readFileSync(path),\n              isFile: path => this.fs.statSync(path).isFile(),\n              isDir: path => this.fs.statSync(path).isDirectory(),\n            },\n      mode: 2,\n      entries: ENTRIES,\n      packageExports: true,\n      moduleDirResolver:\n        process.versions.pnp != null\n          ? (module, from) => {\n              // $FlowFixMe[prop-missing]\n              let pnp = Module.findPnpApi(path.dirname(from));\n\n              return pnp.resolveToUnqualified(\n                // append slash to force loading builtins from npm\n                module + '/',\n                from,\n              );\n            }\n          : undefined,\n    });\n  }\n\n  static deserialize(opts: any): NodePackageManager {\n    return new NodePackageManager(opts.fs, opts.projectRoot, opts.installer);\n  }\n\n  serialize(): {|\n    $$raw: boolean,\n    fs: FileSystem,\n    projectRoot: FilePath,\n    installer: ?PackageInstaller,\n  |} {\n    return {\n      $$raw: false,\n      fs: this.fs,\n      projectRoot: this.projectRoot,\n      installer: this.installer,\n    };\n  }\n\n  async require(\n    name: DependencySpecifier,\n    from: FilePath,\n    opts: ?{|\n      range?: ?SemverRange,\n      shouldAutoInstall?: boolean,\n      saveDev?: boolean,\n    |},\n  ): Promise<any> {\n    let {resolved, type} = await this.resolve(name, from, opts);\n    if (type === 2) {\n      logger.warn({\n        message: 'ES module dependencies are experimental.',\n        origin: '@parcel/package-manager',\n        codeFrames: [\n          {\n            filePath: resolved,\n            codeHighlights: [],\n          },\n        ],\n      });\n\n      // On Windows, Node requires absolute paths to be file URLs.\n      if (process.platform === 'win32' && path.isAbsolute(resolved)) {\n        resolved = pathToFileURL(resolved);\n      }\n\n      // $FlowFixMe\n      return import(resolved);\n    }\n    return this.load(resolved, from);\n  }\n\n  requireSync(name: DependencySpecifier, from: FilePath): any {\n    let {resolved} = this.resolveSync(name, from);\n    return this.load(resolved, from);\n  }\n\n  load(filePath: FilePath, from: FilePath): any {\n    if (!path.isAbsolute(filePath)) {\n      // Node builtin module\n      // $FlowFixMe\n      return require(filePath);\n    }\n\n    // $FlowFixMe[prop-missing]\n    const cachedModule = Module._cache[filePath];\n    if (cachedModule !== undefined) {\n      return cachedModule.exports;\n    }\n\n    // $FlowFixMe\n    let m = new Module(filePath, Module._cache[from] || module.parent);\n    // $FlowFixMe[prop-missing]\n    Module._cache[filePath] = m;\n\n    // Patch require within this module so it goes through our require\n    m.require = id => {\n      return this.requireSync(id, filePath);\n    };\n\n    // Patch `fs.readFileSync` temporarily so that it goes through our file system\n    let {readFileSync, statSync} = nativeFS;\n    // $FlowFixMe\n    nativeFS.readFileSync = (filename, encoding) => {\n      return this.fs.readFileSync(filename, encoding);\n    };\n\n    // $FlowFixMe\n    nativeFS.statSync = filename => {\n      return this.fs.statSync(filename);\n    };\n\n    try {\n      m.load(filePath);\n    } catch (err) {\n      // $FlowFixMe[prop-missing]\n      delete Module._cache[filePath];\n      throw err;\n    } finally {\n      // $FlowFixMe\n      nativeFS.readFileSync = readFileSync;\n      // $FlowFixMe\n      nativeFS.statSync = statSync;\n    }\n\n    return m.exports;\n  }\n\n  async resolve(\n    id: DependencySpecifier,\n    from: FilePath,\n    options?: ?{|\n      range?: ?SemverRange,\n      shouldAutoInstall?: boolean,\n      saveDev?: boolean,\n    |},\n  ): Promise<ResolveResult> {\n    let basedir = path.dirname(from);\n    let key = basedir + ':' + id;\n    let resolved = cache.get(key);\n    if (!resolved) {\n      let [name] = getModuleParts(id);\n      try {\n        resolved = this.resolveInternal(id, from);\n      } catch (e) {\n        if (\n          e.code !== 'MODULE_NOT_FOUND' ||\n          options?.shouldAutoInstall !== true\n        ) {\n          if (\n            e.code === 'MODULE_NOT_FOUND' &&\n            options?.shouldAutoInstall !== true\n          ) {\n            let err = new ThrowableDiagnostic({\n              diagnostic: {\n                message: escapeMarkdown(e.message),\n                hints: [\n                  'Autoinstall is disabled, please install this package manually and restart Parcel.',\n                ],\n              },\n            });\n            // $FlowFixMe - needed for loadParcelPlugin\n            err.code = 'MODULE_NOT_FOUND';\n            throw err;\n          } else {\n            throw e;\n          }\n        }\n\n        let conflicts = await getConflictingLocalDependencies(\n          this.fs,\n          name,\n          from,\n          this.projectRoot,\n        );\n\n        if (conflicts == null) {\n          this.invalidate(id, from);\n          await this.install([{name, range: options?.range}], from, {\n            saveDev: options?.saveDev ?? true,\n          });\n\n          return this.resolve(id, from, {\n            ...options,\n            shouldAutoInstall: false,\n          });\n        }\n\n        throw new ThrowableDiagnostic({\n          diagnostic: conflicts.fields.map(field => ({\n            message: md`Could not find module \"${name}\", but it was listed in package.json. Run your package manager first.`,\n            origin: '@parcel/package-manager',\n            codeFrames: [\n              {\n                filePath: conflicts.filePath,\n                language: 'json',\n                code: conflicts.json,\n                codeHighlights: generateJSONCodeHighlights(conflicts.json, [\n                  {\n                    key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                    type: 'key',\n                    message: 'Defined here, but not installed',\n                  },\n                ]),\n              },\n            ],\n          })),\n        });\n      }\n\n      let range = options?.range;\n      if (range != null) {\n        let pkg = resolved.pkg;\n        if (pkg == null || !semver.satisfies(pkg.version, range)) {\n          let conflicts = await getConflictingLocalDependencies(\n            this.fs,\n            name,\n            from,\n            this.projectRoot,\n          );\n\n          if (conflicts == null && options?.shouldAutoInstall === true) {\n            this.invalidate(id, from);\n            await this.install([{name, range}], from);\n            return this.resolve(id, from, {\n              ...options,\n              shouldAutoInstall: false,\n            });\n          } else if (conflicts != null) {\n            throw new ThrowableDiagnostic({\n              diagnostic: {\n                message: md`Could not find module \"${name}\" satisfying ${range}.`,\n                origin: '@parcel/package-manager',\n                codeFrames: [\n                  {\n                    filePath: conflicts.filePath,\n                    language: 'json',\n                    code: conflicts.json,\n                    codeHighlights: generateJSONCodeHighlights(\n                      conflicts.json,\n                      conflicts.fields.map(field => ({\n                        key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                        type: 'key',\n                        message: 'Found this conflicting local requirement.',\n                      })),\n                    ),\n                  },\n                ],\n              },\n            });\n          }\n\n          let version = pkg?.version;\n          let message = md`Could not resolve package \"${name}\" that satisfies ${range}.`;\n          if (version != null) {\n            message += md` Found ${version}.`;\n          }\n\n          throw new ThrowableDiagnostic({\n            diagnostic: {\n              message,\n              hints: [\n                'Looks like the incompatible version was installed transitively. Add this package as a direct dependency with a compatible version range.',\n              ],\n            },\n          });\n        }\n      }\n\n      cache.set(key, resolved);\n      invalidationsCache.clear();\n\n      // Add the specifier as a child to the parent module.\n      // Don't do this if the specifier was an absolute path, as this was likely a dynamically resolved path\n      // (e.g. babel uses require() to load .babelrc.js configs and we don't want them to be added  as children of babel itself).\n      if (!path.isAbsolute(name)) {\n        let moduleChildren = children.get(from);\n        if (!moduleChildren) {\n          moduleChildren = new Set();\n          children.set(from, moduleChildren);\n        }\n\n        moduleChildren.add(name);\n      }\n    }\n\n    return resolved;\n  }\n\n  resolveSync(name: DependencySpecifier, from: FilePath): ResolveResult {\n    let basedir = path.dirname(from);\n    let key = basedir + ':' + name;\n    let resolved = cache.get(key);\n    if (!resolved) {\n      resolved = this.resolveInternal(name, from);\n      cache.set(key, resolved);\n      invalidationsCache.clear();\n\n      if (!path.isAbsolute(name)) {\n        let moduleChildren = children.get(from);\n        if (!moduleChildren) {\n          moduleChildren = new Set();\n          children.set(from, moduleChildren);\n        }\n\n        moduleChildren.add(name);\n      }\n    }\n\n    return resolved;\n  }\n\n  async install(\n    modules: Array<ModuleRequest>,\n    from: FilePath,\n    opts?: InstallOptions,\n  ) {\n    await installPackage(this.fs, this, modules, from, this.projectRoot, {\n      packageInstaller: this.installer,\n      ...opts,\n    });\n  }\n\n  getInvalidations(name: DependencySpecifier, from: FilePath): Invalidations {\n    let basedir = path.dirname(from);\n    let cacheKey = basedir + ':' + name;\n    let resolved = cache.get(cacheKey);\n\n    if (resolved && path.isAbsolute(resolved.resolved)) {\n      let cached = invalidationsCache.get(resolved.resolved);\n      if (cached != null) {\n        return cached;\n      }\n\n      let res = {\n        invalidateOnFileCreate: [],\n        invalidateOnFileChange: new Set(),\n        invalidateOnStartup: false,\n      };\n\n      let seen = new Set();\n      let addKey = (name, from) => {\n        let basedir = path.dirname(from);\n        let key = basedir + ':' + name;\n        if (seen.has(key)) {\n          return;\n        }\n\n        seen.add(key);\n        let resolved = cache.get(key);\n        if (!resolved || !path.isAbsolute(resolved.resolved)) {\n          return;\n        }\n\n        res.invalidateOnFileCreate.push(...resolved.invalidateOnFileCreate);\n        res.invalidateOnFileChange.add(resolved.resolved);\n\n        for (let file of resolved.invalidateOnFileChange) {\n          res.invalidateOnFileChange.add(file);\n        }\n\n        let moduleChildren = children.get(resolved.resolved);\n        if (moduleChildren) {\n          for (let specifier of moduleChildren) {\n            addKey(specifier, resolved.resolved);\n          }\n        }\n      };\n\n      addKey(name, from);\n\n      // If this is an ES module, we won't have any of the dependencies because import statements\n      // cannot be intercepted. Instead, ask the resolver to parse the file and recursively analyze the deps.\n      if (resolved.type === 2) {\n        let invalidations = this.resolver.getInvalidations(resolved.resolved);\n        invalidations.invalidateOnFileChange.forEach(i =>\n          res.invalidateOnFileChange.add(i),\n        );\n        invalidations.invalidateOnFileCreate.forEach(i =>\n          res.invalidateOnFileCreate.push(i),\n        );\n        res.invalidateOnStartup ||= invalidations.invalidateOnStartup;\n        if (res.invalidateOnStartup) {\n          logger.warn({\n            message: md`${path.relative(\n              this.projectRoot,\n              resolved.resolved,\n            )} contains non-statically analyzable dependencies in its module graph. This causes Parcel to invalidate the cache on startup.`,\n            origin: '@parcel/package-manager',\n          });\n        }\n      }\n\n      invalidationsCache.set(resolved.resolved, res);\n      return res;\n    }\n\n    return {\n      invalidateOnFileCreate: [],\n      invalidateOnFileChange: new Set(),\n      invalidateOnStartup: false,\n    };\n  }\n\n  invalidate(name: DependencySpecifier, from: FilePath) {\n    let seen = new Set();\n\n    let invalidate = (name, from) => {\n      let basedir = path.dirname(from);\n      let key = basedir + ':' + name;\n      if (seen.has(key)) {\n        return;\n      }\n\n      seen.add(key);\n      let resolved = cache.get(key);\n      if (!resolved || !path.isAbsolute(resolved.resolved)) {\n        return;\n      }\n\n      invalidationsCache.delete(resolved.resolved);\n\n      // $FlowFixMe\n      let module = Module._cache[resolved.resolved];\n      if (module) {\n        // $FlowFixMe\n        delete Module._cache[resolved.resolved];\n      }\n\n      let moduleChildren = children.get(resolved.resolved);\n      if (moduleChildren) {\n        for (let specifier of moduleChildren) {\n          invalidate(specifier, resolved.resolved);\n        }\n      }\n\n      children.delete(resolved.resolved);\n      cache.delete(key);\n    };\n\n    invalidate(name, from);\n    this.resolver = this._createResolver();\n  }\n\n  resolveInternal(name: string, from: string): ResolveResult {\n    if (this.resolver == null) {\n      this.resolver = this._createResolver();\n    }\n\n    let res = this.resolver.resolve({\n      filename: name,\n      specifierType: 'commonjs',\n      parent: from,\n    });\n\n    // Invalidate whenever the .pnp.js file changes.\n    // TODO: only when we actually resolve a node_modules package?\n    if (process.versions.pnp != null && res.invalidateOnFileChange) {\n      // $FlowFixMe[prop-missing]\n      let pnp = Module.findPnpApi(path.dirname(from));\n      res.invalidateOnFileChange.push(pnp.resolveToUnqualified('pnpapi', null));\n    }\n\n    if (res.error) {\n      let e = new Error(`Could not resolve module \"${name}\" from \"${from}\"`);\n      // $FlowFixMe\n      e.code = 'MODULE_NOT_FOUND';\n      throw e;\n    }\n    let getPkg;\n    switch (res.resolution.type) {\n      case 'Path':\n        getPkg = () => {\n          let pkgPath = this.fs.findAncestorFile(\n            ['package.json'],\n            nullthrows(res.resolution.value),\n            this.projectRoot,\n          );\n          return pkgPath\n            ? JSON.parse(this.fs.readFileSync(pkgPath, 'utf8'))\n            : null;\n        };\n      // fallthrough\n      case 'Builtin':\n        return {\n          resolved: res.resolution.value,\n          invalidateOnFileChange: new Set(res.invalidateOnFileChange),\n          invalidateOnFileCreate: res.invalidateOnFileCreate,\n          type: res.moduleType,\n          get pkg() {\n            return getPkg();\n          },\n        };\n      default:\n        throw new Error('Unknown resolution type');\n    }\n  }\n}\n\nregisterSerializableClass(\n  `${pkg.version}:NodePackageManager`,\n  NodePackageManager,\n);\n", "'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "// @flow strict-local\n\nimport type {ModuleRequest} from './types';\nimport type {FilePath} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\n\nimport invariant from 'assert';\nimport ThrowableDiagnostic from '@parcel/diagnostic';\nimport {resolveConfig} from '@parcel/utils';\nimport {exec as _exec} from 'child_process';\nimport {promisify} from 'util';\n\nexport const exec: (\n  command: string,\n  options?: child_process$execOpts,\n) => Promise<{|stdout: string | Buffer, stderr: string | Buffer|}> =\n  promisify(_exec);\n\nexport function npmSpecifierFromModuleRequest(\n  moduleRequest: ModuleRequest,\n): string {\n  return moduleRequest.range != null\n    ? [moduleRequest.name, moduleRequest.range].join('@')\n    : moduleRequest.name;\n}\n\nexport function moduleRequestsFromDependencyMap(dependencyMap: {|\n  [string]: string,\n|}): Array<ModuleRequest> {\n  return Object.entries(dependencyMap).map(([name, range]) => {\n    invariant(typeof range === 'string');\n    return {\n      name,\n      range,\n    };\n  });\n}\n\nexport async function getConflictingLocalDependencies(\n  fs: FileSystem,\n  name: string,\n  local: FilePath,\n  projectRoot: FilePath,\n): Promise<?{|json: string, filePath: FilePath, fields: Array<string>|}> {\n  let pkgPath = await resolveConfig(fs, local, ['package.json'], projectRoot);\n  if (pkgPath == null) {\n    return;\n  }\n\n  let pkgStr = await fs.readFile(pkgPath, 'utf8');\n  let pkg;\n  try {\n    pkg = JSON.parse(pkgStr);\n  } catch (e) {\n    // TODO: codeframe\n    throw new ThrowableDiagnostic({\n      diagnostic: {\n        message: 'Failed to parse package.json',\n        origin: '@parcel/package-manager',\n      },\n    });\n  }\n\n  if (typeof pkg !== 'object' || pkg == null) {\n    // TODO: codeframe\n    throw new ThrowableDiagnostic({\n      diagnostic: {\n        message: 'Expected package.json contents to be an object.',\n        origin: '@parcel/package-manager',\n      },\n    });\n  }\n\n  let fields = [];\n  for (let field of ['dependencies', 'devDependencies', 'peerDependencies']) {\n    if (\n      typeof pkg[field] === 'object' &&\n      pkg[field] != null &&\n      pkg[field][name] != null\n    ) {\n      fields.push(field);\n    }\n  }\n\n  if (fields.length > 0) {\n    return {\n      filePath: pkgPath,\n      json: pkgStr,\n      fields,\n    };\n  }\n}\n", "// @flow\n\nimport type {FilePath, PackageJSON} from '@parcel/types';\nimport type {\n  ModuleRequest,\n  PackageManager,\n  PackageInstaller,\n  InstallOptions,\n} from './types';\nimport type {FileSystem} from '@parcel/fs';\n\nimport invariant from 'assert';\nimport path from 'path';\nimport nullthrows from 'nullthrows';\nimport semver from 'semver';\nimport ThrowableDiagnostic, {\n  generateJSONCodeHighlights,\n  encodeJSONKeyComponent,\n  md,\n} from '@parcel/diagnostic';\nimport logger from '@parcel/logger';\nimport {loadConfig, PromiseQueue, resolveConfig} from '@parcel/utils';\nimport WorkerFarm from '@parcel/workers';\n\nimport {Npm} from './Npm';\nimport {Yarn} from './Yarn';\nimport {Pnpm} from './Pnpm.js';\nimport {getConflictingLocalDependencies} from './utils';\nimport validateModuleSpecifier from './validateModuleSpecifier';\n\nasync function install(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  from: FilePath,\n  projectRoot: FilePath,\n  options: InstallOptions = {},\n): Promise<void> {\n  let {installPeers = true, saveDev = true, packageInstaller} = options;\n  let moduleNames = modules.map(m => m.name).join(', ');\n\n  logger.progress(`Installing ${moduleNames}...`);\n\n  let fromPkgPath = await resolveConfig(\n    fs,\n    from,\n    ['package.json'],\n    projectRoot,\n  );\n  let cwd = fromPkgPath ? path.dirname(fromPkgPath) : fs.cwd();\n\n  if (!packageInstaller) {\n    packageInstaller = await determinePackageInstaller(fs, from, projectRoot);\n  }\n\n  try {\n    await packageInstaller.install({\n      modules,\n      saveDev,\n      cwd,\n      packagePath: fromPkgPath,\n      fs,\n    });\n  } catch (err) {\n    throw new Error(`Failed to install ${moduleNames}: ${err.message}`);\n  }\n\n  if (installPeers) {\n    await Promise.all(\n      modules.map(m =>\n        installPeerDependencies(\n          fs,\n          packageManager,\n          m,\n          from,\n          projectRoot,\n          options,\n        ),\n      ),\n    );\n  }\n}\n\nasync function installPeerDependencies(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  module: ModuleRequest,\n  from: FilePath,\n  projectRoot: FilePath,\n  options,\n) {\n  const {resolved} = await packageManager.resolve(module.name, from);\n  const modulePkg: PackageJSON = nullthrows(\n    await loadConfig(fs, resolved, ['package.json'], projectRoot),\n  ).config;\n  const peers = modulePkg.peerDependencies || {};\n\n  let modules: Array<ModuleRequest> = [];\n  for (let [name, range] of Object.entries(peers)) {\n    invariant(typeof range === 'string');\n\n    let conflicts = await getConflictingLocalDependencies(\n      fs,\n      name,\n      from,\n      projectRoot,\n    );\n    if (conflicts) {\n      let {pkg} = await packageManager.resolve(name, from);\n      invariant(pkg);\n      if (!semver.satisfies(pkg.version, range)) {\n        throw new ThrowableDiagnostic({\n          diagnostic: {\n            message: md`Could not install the peer dependency \"${name}\" for \"${module.name}\", installed version ${pkg.version} is incompatible with ${range}`,\n            origin: '@parcel/package-manager',\n            codeFrames: [\n              {\n                filePath: conflicts.filePath,\n                language: 'json',\n                code: conflicts.json,\n                codeHighlights: generateJSONCodeHighlights(\n                  conflicts.json,\n                  conflicts.fields.map(field => ({\n                    key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                    type: 'key',\n                    message: 'Found this conflicting local requirement.',\n                  })),\n                ),\n              },\n            ],\n          },\n        });\n      }\n\n      continue;\n    }\n    modules.push({name, range});\n  }\n\n  if (modules.length) {\n    await install(\n      fs,\n      packageManager,\n      modules,\n      from,\n      projectRoot,\n      Object.assign({}, options, {installPeers: false}),\n    );\n  }\n}\n\nasync function determinePackageInstaller(\n  fs: FileSystem,\n  filepath: FilePath,\n  projectRoot: FilePath,\n): Promise<PackageInstaller> {\n  let configFile = await resolveConfig(\n    fs,\n    filepath,\n    ['package-lock.json', 'pnpm-lock.yaml', 'yarn.lock'],\n    projectRoot,\n  );\n\n  let configName = configFile && path.basename(configFile);\n\n  // Always use the package manager that seems to be used in the project,\n  // falling back to a different one wouldn't update the existing lockfile.\n  if (configName === 'package-lock.json') {\n    return new Npm();\n  } else if (configName === 'pnpm-lock.yaml') {\n    return new Pnpm();\n  } else if (configName === 'yarn.lock') {\n    return new Yarn();\n  }\n\n  if (await Yarn.exists()) {\n    return new Yarn();\n  } else if (await Pnpm.exists()) {\n    return new Pnpm();\n  } else {\n    return new Npm();\n  }\n}\n\nlet queue = new PromiseQueue({maxConcurrent: 1});\nlet modulesInstalling: Set<string> = new Set();\n\n// Exported so that it may be invoked from the worker api below.\n// Do not call this directly! This can result in concurrent package installations\n// across multiple instances of the package manager.\nexport function _addToInstallQueue(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  filePath: FilePath,\n  projectRoot: FilePath,\n  options?: InstallOptions,\n): Promise<mixed> {\n  modules = modules.map(request => ({\n    name: validateModuleSpecifier(request.name),\n    range: request.range,\n  }));\n\n  // Wrap PromiseQueue and track modules that are currently installing.\n  // If a request comes in for a module that is currently installing, don't bother\n  // enqueuing it.\n  let modulesToInstall = modules.filter(\n    m => !modulesInstalling.has(getModuleRequestKey(m)),\n  );\n  if (modulesToInstall.length) {\n    for (let m of modulesToInstall) {\n      modulesInstalling.add(getModuleRequestKey(m));\n    }\n\n    queue\n      .add(() =>\n        install(\n          fs,\n          packageManager,\n          modulesToInstall,\n          filePath,\n          projectRoot,\n          options,\n        ).then(() => {\n          for (let m of modulesToInstall) {\n            modulesInstalling.delete(getModuleRequestKey(m));\n          }\n        }),\n      )\n      .then(\n        () => {},\n        () => {},\n      );\n  }\n\n  return queue.run();\n}\n\nexport function installPackage(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  filePath: FilePath,\n  projectRoot: FilePath,\n  options?: InstallOptions,\n): Promise<mixed> {\n  if (WorkerFarm.isWorker()) {\n    let workerApi = WorkerFarm.getWorkerApi();\n    // TODO this should really be `__filename` but without the rewriting.\n    let bundlePath =\n      process.env.PARCEL_BUILD_ENV === 'production' &&\n      !process.env.PARCEL_SELF_BUILD\n        ? path.join(__dirname, '..', 'lib/index.js')\n        : __filename;\n    return workerApi.callMaster({\n      location: bundlePath,\n      args: [fs, packageManager, modules, filePath, projectRoot, options],\n      method: '_addToInstallQueue',\n    });\n  }\n\n  return _addToInstallQueue(\n    fs,\n    packageManager,\n    modules,\n    filePath,\n    projectRoot,\n    options,\n  );\n}\n\nfunction getModuleRequestKey(moduleRequest: ModuleRequest): string {\n  return [moduleRequest.name, moduleRequest.range].join('@');\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from './types';\n\nimport path from 'path';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst NPM_CMD = 'npm';\n\nexport class Npm implements PackageInstaller {\n  async install({\n    modules,\n    cwd,\n    fs,\n    packagePath,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    // npm doesn't auto-create a package.json when installing,\n    // so create an empty one if needed.\n    if (packagePath == null) {\n      await fs.writeFile(path.join(cwd, 'package.json'), '{}');\n    }\n\n    let args = ['install', '--json', saveDev ? '--save-dev' : '--save'].concat(\n      modules.map(npmSpecifierFromModuleRequest),\n    );\n\n    // When Parcel is run by npm (e.g. via package.json scripts), several environment variables are\n    // added. When parcel in turn calls npm again, these can cause npm to behave stragely, so we\n    // filter them out when installing packages.\n    let env = {};\n    for (let key in process.env) {\n      if (!key.startsWith('npm_') && key !== 'INIT_CWD' && key !== 'NODE_ENV') {\n        env[key] = process.env[key];\n      }\n    }\n\n    let installProcess = spawn(NPM_CMD, args, {cwd, env});\n    let stdout = '';\n    installProcess.stdout.on('data', (buf: Buffer) => {\n      stdout += buf.toString();\n    });\n\n    let stderr = [];\n    installProcess.stderr.on('data', (buf: Buffer) => {\n      stderr.push(buf.toString().trim());\n    });\n\n    try {\n      await promiseFromProcess(installProcess);\n\n      let results: NPMResults = JSON.parse(stdout);\n      let addedCount = results.added.length;\n      if (addedCount > 0) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message: `Added ${addedCount} packages via npm`,\n        });\n      }\n\n      // Since we succeeded, stderr might have useful information not included\n      // in the json written to stdout. It's also not necessary to log these as\n      // errors as they often aren't.\n      for (let message of stderr) {\n        if (message.length > 0) {\n          logger.log({\n            origin: '@parcel/package-manager',\n            message,\n          });\n        }\n      }\n    } catch (e) {\n      throw new Error(\n        'npm failed to install modules: ' +\n          e.message +\n          ' - ' +\n          stderr.join('\\n'),\n      );\n    }\n  }\n}\n\ntype NPMResults = {|\n  added: Array<{name: string, ...}>,\n|};\n\nregisterSerializableClass(`${pkg.version}:Npm`, Npm);\n", "'use strict';\n\nconst cp = require('child_process');\nconst parse = require('./lib/parse');\nconst enoent = require('./lib/enoent');\n\nfunction spawn(command, args, options) {\n    // Parse the arguments\n    const parsed = parse(command, args, options);\n\n    // Spawn the child process\n    const spawned = cp.spawn(parsed.command, parsed.args, parsed.options);\n\n    // Hook into child process \"exit\" event to emit an error if the command\n    // does not exists, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16\n    enoent.hookChildProcess(spawned, parsed);\n\n    return spawned;\n}\n\nfunction spawnSync(command, args, options) {\n    // Parse the arguments\n    const parsed = parse(command, args, options);\n\n    // Spawn the child process\n    const result = cp.spawnSync(parsed.command, parsed.args, parsed.options);\n\n    // Analyze if the command does not exist, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16\n    result.error = result.error || enoent.verifyENOENTSync(result.status, parsed);\n\n    return result;\n}\n\nmodule.exports = spawn;\nmodule.exports.spawn = spawn;\nmodule.exports.sync = spawnSync;\n\nmodule.exports._parse = parse;\nmodule.exports._enoent = enoent;\n", "'use strict';\n\nconst path = require('path');\nconst niceTry = require('nice-try');\nconst resolveCommand = require('./util/resolveCommand');\nconst escape = require('./util/escape');\nconst readShebang = require('./util/readShebang');\nconst semver = require('semver');\n\nconst isWin = process.platform === 'win32';\nconst isExecutableRegExp = /\\.(?:com|exe)$/i;\nconst isCmdShimRegExp = /node_modules[\\\\/].bin[\\\\/][^\\\\/]+\\.cmd$/i;\n\n// `options.shell` is supported in Node ^4.8.0, ^5.7.0 and >= 6.0.0\nconst supportsShellOption = niceTry(() => semver.satisfies(process.version, '^4.8.0 || ^5.7.0 || >= 6.0.0', true)) || false;\n\nfunction detectShebang(parsed) {\n    parsed.file = resolveCommand(parsed);\n\n    const shebang = parsed.file && readShebang(parsed.file);\n\n    if (shebang) {\n        parsed.args.unshift(parsed.file);\n        parsed.command = shebang;\n\n        return resolveCommand(parsed);\n    }\n\n    return parsed.file;\n}\n\nfunction parseNonShell(parsed) {\n    if (!isWin) {\n        return parsed;\n    }\n\n    // Detect & add support for shebangs\n    const commandFile = detectShebang(parsed);\n\n    // We don't need a shell if the command filename is an executable\n    const needsShell = !isExecutableRegExp.test(commandFile);\n\n    // If a shell is required, use cmd.exe and take care of escaping everything correctly\n    // Note that `forceShell` is an hidden option used only in tests\n    if (parsed.options.forceShell || needsShell) {\n        // Need to double escape meta chars if the command is a cmd-shim located in `node_modules/.bin/`\n        // The cmd-shim simply calls execute the package bin file with NodeJS, proxying any argument\n        // Because the escape of metachars with ^ gets interpreted when the cmd.exe is first called,\n        // we need to double escape them\n        const needsDoubleEscapeMetaChars = isCmdShimRegExp.test(commandFile);\n\n        // Normalize posix paths into OS compatible paths (e.g.: foo/bar -> foo\\bar)\n        // This is necessary otherwise it will always fail with ENOENT in those cases\n        parsed.command = path.normalize(parsed.command);\n\n        // Escape command & arguments\n        parsed.command = escape.command(parsed.command);\n        parsed.args = parsed.args.map((arg) => escape.argument(arg, needsDoubleEscapeMetaChars));\n\n        const shellCommand = [parsed.command].concat(parsed.args).join(' ');\n\n        parsed.args = ['/d', '/s', '/c', `\"${shellCommand}\"`];\n        parsed.command = process.env.comspec || 'cmd.exe';\n        parsed.options.windowsVerbatimArguments = true; // Tell node's spawn that the arguments are already escaped\n    }\n\n    return parsed;\n}\n\nfunction parseShell(parsed) {\n    // If node supports the shell option, there's no need to mimic its behavior\n    if (supportsShellOption) {\n        return parsed;\n    }\n\n    // Mimic node shell option\n    // See https://github.com/nodejs/node/blob/b9f6a2dc059a1062776133f3d4fd848c4da7d150/lib/child_process.js#L335\n    const shellCommand = [parsed.command].concat(parsed.args).join(' ');\n\n    if (isWin) {\n        parsed.command = typeof parsed.options.shell === 'string' ? parsed.options.shell : process.env.comspec || 'cmd.exe';\n        parsed.args = ['/d', '/s', '/c', `\"${shellCommand}\"`];\n        parsed.options.windowsVerbatimArguments = true; // Tell node's spawn that the arguments are already escaped\n    } else {\n        if (typeof parsed.options.shell === 'string') {\n            parsed.command = parsed.options.shell;\n        } else if (process.platform === 'android') {\n            parsed.command = '/system/bin/sh';\n        } else {\n            parsed.command = '/bin/sh';\n        }\n\n        parsed.args = ['-c', shellCommand];\n    }\n\n    return parsed;\n}\n\nfunction parse(command, args, options) {\n    // Normalize arguments, similar to nodejs\n    if (args && !Array.isArray(args)) {\n        options = args;\n        args = null;\n    }\n\n    args = args ? args.slice(0) : []; // Clone array to avoid changing the original\n    options = Object.assign({}, options); // Clone object to avoid changing the original\n\n    // Build our parsed object\n    const parsed = {\n        command,\n        args,\n        options,\n        file: undefined,\n        original: {\n            command,\n            args,\n        },\n    };\n\n    // Delegate further parsing to shell or non-shell\n    return options.shell ? parseShell(parsed) : parseNonShell(parsed);\n}\n\nmodule.exports = parse;\n", "'use strict'\n\n/**\n * Tri<PERSON> to execute a function and discards any error that occurs.\n * @param {Function} fn - Function that might or might not throw an error.\n * @returns {?*} Return-value of the function when no error occurred.\n */\nmodule.exports = function(fn) {\n\n\ttry { return fn() } catch (e) {}\n\n}", "'use strict';\n\nconst path = require('path');\nconst which = require('which');\nconst pathKey = require('path-key')();\n\nfunction resolveCommandAttempt(parsed, withoutPathExt) {\n    const cwd = process.cwd();\n    const hasCustomCwd = parsed.options.cwd != null;\n\n    // If a custom `cwd` was specified, we need to change the process cwd\n    // because `which` will do stat calls but does not support a custom cwd\n    if (hasCustomCwd) {\n        try {\n            process.chdir(parsed.options.cwd);\n        } catch (err) {\n            /* Empty */\n        }\n    }\n\n    let resolved;\n\n    try {\n        resolved = which.sync(parsed.command, {\n            path: (parsed.options.env || process.env)[pathKey],\n            pathExt: withoutPathExt ? path.delimiter : undefined,\n        });\n    } catch (e) {\n        /* Empty */\n    } finally {\n        process.chdir(cwd);\n    }\n\n    // If we successfully resolved, ensure that an absolute path is returned\n    // Note that when a custom `cwd` was used, we need to resolve to an absolute path based on it\n    if (resolved) {\n        resolved = path.resolve(hasCustomCwd ? parsed.options.cwd : '', resolved);\n    }\n\n    return resolved;\n}\n\nfunction resolveCommand(parsed) {\n    return resolveCommandAttempt(parsed) || resolveCommandAttempt(parsed, true);\n}\n\nmodule.exports = resolveCommand;\n", "module.exports = which\nwhich.sync = whichSync\n\nvar isWindows = process.platform === 'win32' ||\n    process.env.OSTYPE === 'cygwin' ||\n    process.env.OSTYPE === 'msys'\n\nvar path = require('path')\nvar COLON = isWindows ? ';' : ':'\nvar isexe = require('isexe')\n\nfunction getNotFoundError (cmd) {\n  var er = new Error('not found: ' + cmd)\n  er.code = 'ENOENT'\n\n  return er\n}\n\nfunction getPathInfo (cmd, opt) {\n  var colon = opt.colon || COLON\n  var pathEnv = opt.path || process.env.PATH || ''\n  var pathExt = ['']\n\n  pathEnv = pathEnv.split(colon)\n\n  var pathExtExe = ''\n  if (isWindows) {\n    pathEnv.unshift(process.cwd())\n    pathExtExe = (opt.pathExt || process.env.PATHEXT || '.EXE;.CMD;.BAT;.COM')\n    pathExt = pathExtExe.split(colon)\n\n\n    // Always test the cmd itself first.  isexe will check to make sure\n    // it's found in the pathExt set.\n    if (cmd.indexOf('.') !== -1 && pathExt[0] !== '')\n      pathExt.unshift('')\n  }\n\n  // If it has a slash, then we don't bother searching the pathenv.\n  // just check the file itself, and that's it.\n  if (cmd.match(/\\//) || isWindows && cmd.match(/\\\\/))\n    pathEnv = ['']\n\n  return {\n    env: pathEnv,\n    ext: pathExt,\n    extExe: pathExtExe\n  }\n}\n\nfunction which (cmd, opt, cb) {\n  if (typeof opt === 'function') {\n    cb = opt\n    opt = {}\n  }\n\n  var info = getPathInfo(cmd, opt)\n  var pathEnv = info.env\n  var pathExt = info.ext\n  var pathExtExe = info.extExe\n  var found = []\n\n  ;(function F (i, l) {\n    if (i === l) {\n      if (opt.all && found.length)\n        return cb(null, found)\n      else\n        return cb(getNotFoundError(cmd))\n    }\n\n    var pathPart = pathEnv[i]\n    if (pathPart.charAt(0) === '\"' && pathPart.slice(-1) === '\"')\n      pathPart = pathPart.slice(1, -1)\n\n    var p = path.join(pathPart, cmd)\n    if (!pathPart && (/^\\.[\\\\\\/]/).test(cmd)) {\n      p = cmd.slice(0, 2) + p\n    }\n    ;(function E (ii, ll) {\n      if (ii === ll) return F(i + 1, l)\n      var ext = pathExt[ii]\n      isexe(p + ext, { pathExt: pathExtExe }, function (er, is) {\n        if (!er && is) {\n          if (opt.all)\n            found.push(p + ext)\n          else\n            return cb(null, p + ext)\n        }\n        return E(ii + 1, ll)\n      })\n    })(0, pathExt.length)\n  })(0, pathEnv.length)\n}\n\nfunction whichSync (cmd, opt) {\n  opt = opt || {}\n\n  var info = getPathInfo(cmd, opt)\n  var pathEnv = info.env\n  var pathExt = info.ext\n  var pathExtExe = info.extExe\n  var found = []\n\n  for (var i = 0, l = pathEnv.length; i < l; i ++) {\n    var pathPart = pathEnv[i]\n    if (pathPart.charAt(0) === '\"' && pathPart.slice(-1) === '\"')\n      pathPart = pathPart.slice(1, -1)\n\n    var p = path.join(pathPart, cmd)\n    if (!pathPart && /^\\.[\\\\\\/]/.test(cmd)) {\n      p = cmd.slice(0, 2) + p\n    }\n    for (var j = 0, ll = pathExt.length; j < ll; j ++) {\n      var cur = p + pathExt[j]\n      var is\n      try {\n        is = isexe.sync(cur, { pathExt: pathExtExe })\n        if (is) {\n          if (opt.all)\n            found.push(cur)\n          else\n            return cur\n        }\n      } catch (ex) {}\n    }\n  }\n\n  if (opt.all && found.length)\n    return found\n\n  if (opt.nothrow)\n    return null\n\n  throw getNotFoundError(cmd)\n}\n", "var fs = require('fs')\nvar core\nif (process.platform === 'win32' || global.TESTING_WINDOWS) {\n  core = require('./windows.js')\n} else {\n  core = require('./mode.js')\n}\n\nmodule.exports = isexe\nisexe.sync = sync\n\nfunction isexe (path, options, cb) {\n  if (typeof options === 'function') {\n    cb = options\n    options = {}\n  }\n\n  if (!cb) {\n    if (typeof Promise !== 'function') {\n      throw new TypeError('callback not provided')\n    }\n\n    return new Promise(function (resolve, reject) {\n      isexe(path, options || {}, function (er, is) {\n        if (er) {\n          reject(er)\n        } else {\n          resolve(is)\n        }\n      })\n    })\n  }\n\n  core(path, options || {}, function (er, is) {\n    // ignore EACCES because that just means we aren't allowed to run it\n    if (er) {\n      if (er.code === 'EACCES' || options && options.ignoreErrors) {\n        er = null\n        is = false\n      }\n    }\n    cb(er, is)\n  })\n}\n\nfunction sync (path, options) {\n  // my kingdom for a filtered catch\n  try {\n    return core.sync(path, options || {})\n  } catch (er) {\n    if (options && options.ignoreErrors || er.code === 'EACCES') {\n      return false\n    } else {\n      throw er\n    }\n  }\n}\n", "'use strict';\n\n// See http://www.robvanderwoude.com/escapechars.php\nconst metaCharsRegExp = /([()\\][%!^\"`<>&|;, *?])/g;\n\nfunction escapeCommand(arg) {\n    // Escape meta chars\n    arg = arg.replace(metaCharsRegExp, '^$1');\n\n    return arg;\n}\n\nfunction escapeArgument(arg, doubleEscapeMetaChars) {\n    // Convert to string\n    arg = `${arg}`;\n\n    // Algorithm below is based on https://qntm.org/cmd\n\n    // Sequence of backslashes followed by a double quote:\n    // double up all the backslashes and escape the double quote\n    arg = arg.replace(/(\\\\*)\"/g, '$1$1\\\\\"');\n\n    // Sequence of backslashes followed by the end of the string\n    // (which will become a double quote later):\n    // double up all the backslashes\n    arg = arg.replace(/(\\\\*)$/, '$1$1');\n\n    // All other backslashes occur literally\n\n    // Quote the whole thing:\n    arg = `\"${arg}\"`;\n\n    // Escape meta chars\n    arg = arg.replace(metaCharsRegExp, '^$1');\n\n    // Double escape meta chars if necessary\n    if (doubleEscapeMetaChars) {\n        arg = arg.replace(metaCharsRegExp, '^$1');\n    }\n\n    return arg;\n}\n\nmodule.exports.command = escapeCommand;\nmodule.exports.argument = escapeArgument;\n", "'use strict';\n\nconst fs = require('fs');\nconst shebangCommand = require('shebang-command');\n\nfunction readShebang(command) {\n    // Read the first 150 bytes from the file\n    const size = 150;\n    let buffer;\n\n    if (Buffer.alloc) {\n        // Node.js v4.5+ / v5.10+\n        buffer = Buffer.alloc(size);\n    } else {\n        // Old Node.js API\n        buffer = new Buffer(size);\n        buffer.fill(0); // zero-fill\n    }\n\n    let fd;\n\n    try {\n        fd = fs.openSync(command, 'r');\n        fs.readSync(fd, buffer, 0, size, 0);\n        fs.closeSync(fd);\n    } catch (e) { /* Empty */ }\n\n    // Attempt to extract shebang (null is returned if not a shebang)\n    return shebangCommand(buffer.toString());\n}\n\nmodule.exports = readShebang;\n", "'use strict';\nvar shebangRegex = require('shebang-regex');\n\nmodule.exports = function (str) {\n\tvar match = str.match(shebangRegex);\n\n\tif (!match) {\n\t\treturn null;\n\t}\n\n\tvar arr = match[0].replace(/#! ?/, '').split(' ');\n\tvar bin = arr[0].split('/').pop();\n\tvar arg = arr[1];\n\n\treturn (bin === 'env' ?\n\t\targ :\n\t\tbin + (arg ? ' ' + arg : '')\n\t);\n};\n", "'use strict';\nmodule.exports = /^#!.*/;\n", "'use strict';\n\nconst isWin = process.platform === 'win32';\n\nfunction notFoundError(original, syscall) {\n    return Object.assign(new Error(`${syscall} ${original.command} ENOENT`), {\n        code: 'ENOENT',\n        errno: 'ENOENT',\n        syscall: `${syscall} ${original.command}`,\n        path: original.command,\n        spawnargs: original.args,\n    });\n}\n\nfunction hookChildProcess(cp, parsed) {\n    if (!isWin) {\n        return;\n    }\n\n    const originalEmit = cp.emit;\n\n    cp.emit = function (name, arg1) {\n        // If emitting \"exit\" event and exit code is 1, we need to check if\n        // the command exists and emit an \"error\" instead\n        // See https://github.com/IndigoUnited/node-cross-spawn/issues/16\n        if (name === 'exit') {\n            const err = verifyENOENT(arg1, parsed, 'spawn');\n\n            if (err) {\n                return originalEmit.call(cp, 'error', err);\n            }\n        }\n\n        return originalEmit.apply(cp, arguments); // eslint-disable-line prefer-rest-params\n    };\n}\n\nfunction verifyENOENT(status, parsed) {\n    if (isWin && status === 1 && !parsed.file) {\n        return notFoundError(parsed.original, 'spawn');\n    }\n\n    return null;\n}\n\nfunction verifyENOENTSync(status, parsed) {\n    if (isWin && status === 1 && !parsed.file) {\n        return notFoundError(parsed.original, 'spawnSync');\n    }\n\n    return null;\n}\n\nmodule.exports = {\n    hookChildProcess,\n    verifyENOENT,\n    verifyENOENTSync,\n    notFoundError,\n};\n", "// @flow strict-local\n\nimport type {ChildProcess} from 'child_process';\n\nexport default function promiseFromProcess(\n  childProcess: ChildProcess,\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    childProcess.on('error', reject);\n    childProcess.on('close', code => {\n      if (code !== 0) {\n        reject(new Error('Child process failed'));\n        return;\n      }\n\n      resolve();\n    });\n  });\n}\n", "{\n  \"name\": \"@parcel/package-manager\",\n  \"version\": \"2.9.3\",\n  \"description\": \"Blazing fast, zero configuration web application bundler\",\n  \"license\": \"MIT\",\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"funding\": {\n    \"type\": \"opencollective\",\n    \"url\": \"https://opencollective.com/parcel\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/parcel-bundler/parcel.git\"\n  },\n  \"main\": \"lib/index.js\",\n  \"source\": \"src/index.js\",\n  \"types\": \"index.d.ts\",\n  \"engines\": {\n    \"node\": \">= 12.0.0\"\n  },\n  \"scripts\": {\n    \"build-ts\": \"mkdir -p lib && flow-to-ts src/types.js > lib/types.d.ts\",\n    \"check-ts\": \"tsc --noEmit index.d.ts\",\n    \"test\": \"mocha test\"\n  },\n  \"targets\": {\n    \"types\": false,\n    \"main\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/diagnostic\": false,\n        \"@parcel/fs\": false,\n        \"@parcel/logger\": false,\n        \"@parcel/node-resolver-core\": false,\n        \"@parcel/types\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/workers\": false,\n        \"semver\": false\n      }\n    }\n  },\n  \"dependencies\": {\n    \"@parcel/diagnostic\": \"2.9.3\",\n    \"@parcel/fs\": \"2.9.3\",\n    \"@parcel/logger\": \"2.9.3\",\n    \"@parcel/node-resolver-core\": \"3.0.3\",\n    \"@parcel/types\": \"2.9.3\",\n    \"@parcel/utils\": \"2.9.3\",\n    \"@parcel/workers\": \"2.9.3\",\n    \"semver\": \"^7.5.2\"\n  },\n  \"devDependencies\": {\n    \"command-exists\": \"^1.2.6\",\n    \"cross-spawn\": \"^6.0.4\",\n    \"nullthrows\": \"^1.1.1\",\n    \"split2\": \"^3.1.1\"\n  },\n  \"peerDependencies\": {\n    \"@parcel/core\": \"^2.9.3\"\n  },\n  \"browser\": {\n    \"./src/Npm.js\": false,\n    \"./src/Pnpm.js\": false,\n    \"./src/Yarn.js\": false\n  },\n  \"gitHead\": \"db3bcae10497fa6a712fd9a135f93f26c5745454\"\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from './types';\n\nimport commandExists from 'command-exists';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport split from 'split2';\nimport JSONParseStream from './JSONParseStream';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {exec, npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst YARN_CMD = 'yarn';\n\ntype YarnStdOutMessage =\n  | {|\n      +type: 'step',\n      data: {|\n        message: string,\n        current: number,\n        total: number,\n      |},\n    |}\n  | {|+type: 'success', data: string|}\n  | {|+type: 'info', data: string|}\n  | {|+type: 'tree' | 'progressStart' | 'progressTick'|};\n\ntype YarnStdErrMessage = {|\n  +type: 'error' | 'warning',\n  data: string,\n|};\n\nlet hasYarn: ?boolean;\nlet yarnVersion: ?number;\n\nexport class Yarn implements PackageInstaller {\n  static async exists(): Promise<boolean> {\n    if (hasYarn != null) {\n      return hasYarn;\n    }\n\n    try {\n      hasYarn = Boolean(await commandExists('yarn'));\n    } catch (err) {\n      hasYarn = false;\n    }\n\n    return hasYarn;\n  }\n\n  async install({\n    modules,\n    cwd,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (yarnVersion == null) {\n      let version = await exec('yarn --version');\n      yarnVersion = parseInt(version.stdout, 10);\n    }\n\n    let args = ['add', '--json'].concat(\n      modules.map(npmSpecifierFromModuleRequest),\n    );\n\n    if (saveDev) {\n      args.push('-D');\n      if (yarnVersion < 2) {\n        args.push('-W');\n      }\n    }\n\n    // When Parcel is run by Yarn (e.g. via package.json scripts), several environment variables are\n    // added. When parcel in turn calls Yarn again, these can cause Yarn to behave stragely, so we\n    // filter them out when installing packages.\n    let env = {};\n    for (let key in process.env) {\n      if (\n        !key.startsWith('npm_') &&\n        key !== 'YARN_WRAP_OUTPUT' &&\n        key !== 'INIT_CWD' &&\n        key !== 'NODE_ENV'\n      ) {\n        env[key] = process.env[key];\n      }\n    }\n\n    let installProcess = spawn(YARN_CMD, args, {cwd, env});\n    installProcess.stdout\n      // Invoking yarn with --json provides streaming, newline-delimited JSON output.\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.error(e, '@parcel/package-manager');\n      })\n      .on('data', (message: YarnStdOutMessage) => {\n        switch (message.type) {\n          case 'step':\n            logger.progress(\n              prefix(\n                `[${message.data.current}/${message.data.total}] ${message.data.message}`,\n              ),\n            );\n            return;\n          case 'success':\n          case 'info':\n            logger.info({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          default:\n          // ignore\n        }\n      });\n\n    installProcess.stderr\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.error(e, '@parcel/package-manager');\n      })\n      .on('data', (message: YarnStdErrMessage) => {\n        switch (message.type) {\n          case 'warning':\n            logger.warn({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          case 'error':\n            logger.error({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          default:\n          // ignore\n        }\n      });\n\n    try {\n      return await promiseFromProcess(installProcess);\n    } catch (e) {\n      throw new Error('Yarn failed to install modules:' + e.message);\n    }\n  }\n}\n\nfunction prefix(message: string): string {\n  return 'yarn: ' + message;\n}\n\nregisterSerializableClass(`${pkg.version}:Yarn`, Yarn);\n", "module.exports = require('./lib/command-exists');\n", "/*\nCopyright (c) 2014-2018, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n*/\n\n'use strict'\n\nconst { Transform } = require('readable-stream')\nconst { StringDecoder } = require('string_decoder')\nconst kLast = Symbol('last')\nconst kDecoder = Symbol('decoder')\n\nfunction transform (chunk, enc, cb) {\n  var list\n  if (this.overflow) { // Line buffer is full. Skip to start of next line.\n    var buf = this[kDecoder].write(chunk)\n    list = buf.split(this.matcher)\n\n    if (list.length === 1) return cb() // Line ending not found. Discard entire chunk.\n\n    // Line ending found. Discard trailing fragment of previous line and reset overflow state.\n    list.shift()\n    this.overflow = false\n  } else {\n    this[kLast] += this[kDecoder].write(chunk)\n    list = this[kLast].split(this.matcher)\n  }\n\n  this[kLast] = list.pop()\n\n  for (var i = 0; i < list.length; i++) {\n    try {\n      push(this, this.mapper(list[i]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  this.overflow = this[kLast].length > this.maxLength\n  if (this.overflow && !this.skipOverflow) return cb(new Error('maximum buffer reached'))\n\n  cb()\n}\n\nfunction flush (cb) {\n  // forward any gibberish left in there\n  this[kLast] += this[kDecoder].end()\n\n  if (this[kLast]) {\n    try {\n      push(this, this.mapper(this[kLast]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  cb()\n}\n\nfunction push (self, val) {\n  if (val !== undefined) {\n    self.push(val)\n  }\n}\n\nfunction noop (incoming) {\n  return incoming\n}\n\nfunction split (matcher, mapper, options) {\n  // Set defaults for any arguments not supplied.\n  matcher = matcher || /\\r?\\n/\n  mapper = mapper || noop\n  options = options || {}\n\n  // Test arguments explicitly.\n  switch (arguments.length) {\n    case 1:\n      // If mapper is only argument.\n      if (typeof matcher === 'function') {\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If options is only argument.\n      } else if (typeof matcher === 'object' && !(matcher instanceof RegExp)) {\n        options = matcher\n        matcher = /\\r?\\n/\n      }\n      break\n\n    case 2:\n      // If mapper and options are arguments.\n      if (typeof matcher === 'function') {\n        options = mapper\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If matcher and options are arguments.\n      } else if (typeof mapper === 'object') {\n        options = mapper\n        mapper = noop\n      }\n  }\n\n  options = Object.assign({}, options)\n  options.transform = transform\n  options.flush = flush\n  options.readableObjectMode = true\n\n  const stream = new Transform(options)\n\n  stream[kLast] = ''\n  stream[kDecoder] = new StringDecoder('utf8')\n  stream.matcher = matcher\n  stream.mapper = mapper\n  stream.maxLength = options.maxLength\n  stream.skipOverflow = options.skipOverflow\n  stream.overflow = false\n\n  return stream\n}\n\nmodule.exports = split\n", "// @flow strict-local\n\nimport type {JSONObject} from '@parcel/types';\n\nimport logger from '@parcel/logger';\nimport {Transform} from 'stream';\n\n// Transforms chunks of json strings to parsed objects.\n// Pair with split2 to parse stream of newline-delimited text.\nexport default class JSONParseStream extends Transform {\n  constructor(options: mixed) {\n    super({...options, objectMode: true});\n  }\n\n  // $FlowFixMe We are in object mode, so we emit objects, not strings\n  _transform(\n    chunk: Buffer | string,\n    encoding: string,\n    callback: (err: ?Error, parsed: ?JSONObject) => mixed,\n  ) {\n    try {\n      let parsed;\n      try {\n        parsed = JSON.parse(chunk.toString());\n      } catch (e) {\n        // Be permissive and ignoreJSON parse errors in case there was\n        // a non-JSON line in the package manager's stdout.\n        logger.verbose({\n          message: 'Ignored invalid JSON message: ' + chunk.toString(),\n          origin: '@parcel/package-manager',\n        });\n        return;\n      }\n      callback(null, parsed);\n    } catch (err) {\n      callback(err);\n    }\n  }\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from './types';\n\nimport path from 'path';\nimport fs from 'fs';\nimport commandExists from 'command-exists';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport split from 'split2';\nimport JSONParseStream from './JSONParseStream';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {exec, npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst PNPM_CMD = 'pnpm';\n\ntype LogLevel = 'error' | 'warn' | 'info' | 'debug';\n\ntype ErrorLog = {|\n  err: {|\n    message: string,\n    code: string,\n    stack: string,\n  |},\n|};\n\ntype PNPMLog =\n  | {|\n      +name: 'pnpm:progress',\n      packageId: string,\n      status: 'fetched' | 'found_in_store' | 'resolved',\n    |}\n  | {|\n      +name: 'pnpm:root',\n      added?: {|\n        id?: string,\n        name: string,\n        realName: string,\n        version?: string,\n        dependencyType?: 'prod' | 'dev' | 'optional',\n        latest?: string,\n        linkedFrom?: string,\n      |},\n      removed?: {|\n        name: string,\n        version?: string,\n        dependencyType?: 'prod' | 'dev' | 'optional',\n      |},\n    |}\n  | {|+name: 'pnpm:importing', from: string, method: string, to: string|}\n  | {|+name: 'pnpm:link', target: string, link: string|}\n  | {|+name: 'pnpm:stats', prefix: string, removed?: number, added?: number|};\n\ntype PNPMResults = {|\n  level: LogLevel,\n  prefix?: string,\n  message?: string,\n  ...ErrorLog,\n  ...PNPMLog,\n|};\n\nlet hasPnpm: ?boolean;\nlet pnpmVersion: ?number;\n\nexport class Pnpm implements PackageInstaller {\n  static async exists(): Promise<boolean> {\n    if (hasPnpm != null) {\n      return hasPnpm;\n    }\n\n    try {\n      hasPnpm = Boolean(await commandExists('pnpm'));\n    } catch (err) {\n      hasPnpm = false;\n    }\n\n    return hasPnpm;\n  }\n\n  async install({\n    modules,\n    cwd,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (pnpmVersion == null) {\n      let version = await exec('pnpm --version');\n      pnpmVersion = parseInt(version.stdout, 10);\n    }\n\n    let args = ['add', '--reporter', 'ndjson'];\n    if (saveDev) {\n      args.push('-D');\n    }\n    if (pnpmVersion >= 7) {\n      if (fs.existsSync(path.join(cwd, 'pnpm-workspace.yaml'))) {\n        // installs in workspace root (regardless of cwd)\n        args.push('-w');\n      }\n    } else {\n      // ignores workspace root check\n      args.push('-W');\n    }\n    args = args.concat(modules.map(npmSpecifierFromModuleRequest));\n\n    let env = {};\n    for (let key in process.env) {\n      if (!key.startsWith('npm_') && key !== 'INIT_CWD' && key !== 'NODE_ENV') {\n        env[key] = process.env[key];\n      }\n    }\n\n    let addedCount = 0,\n      removedCount = 0;\n\n    let installProcess = spawn(PNPM_CMD, args, {\n      cwd,\n      env,\n    });\n    installProcess.stdout\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.warn({\n          origin: '@parcel/package-manager',\n          message: e.chunk,\n          stack: e.stack,\n        });\n      })\n      .on('data', (json: PNPMResults) => {\n        if (json.level === 'error') {\n          logger.error({\n            origin: '@parcel/package-manager',\n            message: json.err.message,\n            stack: json.err.stack,\n          });\n        } else if (json.level === 'info' && typeof json.message === 'string') {\n          logger.info({\n            origin: '@parcel/package-manager',\n            message: prefix(json.message),\n          });\n        } else if (json.name === 'pnpm:stats') {\n          addedCount += json.added ?? 0;\n          removedCount += json.removed ?? 0;\n        }\n      });\n\n    let stderr = [];\n    installProcess.stderr\n      .on('data', str => {\n        stderr.push(str.toString());\n      })\n      .on('error', e => {\n        logger.warn({\n          origin: '@parcel/package-manager',\n          message: e.message,\n        });\n      });\n\n    try {\n      await promiseFromProcess(installProcess);\n\n      if (addedCount > 0 || removedCount > 0) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message: `Added ${addedCount} ${\n            removedCount > 0 ? `and removed ${removedCount} ` : ''\n          }packages via pnpm`,\n        });\n      }\n\n      // Since we succeeded, stderr might have useful information not included\n      // in the json written to stdout. It's also not necessary to log these as\n      // errors as they often aren't.\n      for (let message of stderr) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message,\n        });\n      }\n    } catch (e) {\n      throw new Error('pnpm failed to install modules');\n    }\n  }\n}\n\nfunction prefix(message: string): string {\n  return 'pnpm: ' + message;\n}\n\nregisterSerializableClass(`${pkg.version}:Pnpm`, Pnpm);\n", "// @flow\n\nconst MODULE_REGEX = /^((@[^/\\s]+\\/){0,1}([^/\\s.~]+[^/\\s]*)){1}(@[^/\\s]+){0,1}/;\n\nexport default function validateModuleSpecifier(moduleName: string): string {\n  let matches = MODULE_REGEX.exec(moduleName);\n  if (matches) {\n    return matches[0];\n  }\n\n  return '';\n}\n", "// @flow\nexport type * from './types';\nexport * from './Npm';\nexport * from './Pnpm';\nexport * from './Yarn';\nexport * from './MockPackageInstaller';\nexport * from './NodePackageManager';\nexport {_addToInstallQueue} from './installPackage';\n", "// @flow\n\nimport type {ModuleRequest, PackageInstaller, InstallerOptions} from './types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {FilePath} from '@parcel/types';\n\nimport path from 'path';\nimport {ncp} from '@parcel/fs';\nimport {registerSerializableClass} from '@parcel/core';\nimport pkg from '../package.json';\nimport {moduleRequestsFromDependencyMap} from './utils';\n\ntype Package = {|\n  fs: FileSystem,\n  packagePath: FilePath,\n|};\n\n// This PackageInstaller implementation simply copies files from one filesystem to another.\n// Mostly useful for testing purposes.\nexport class MockPackageInstaller implements PackageInstaller {\n  packages: Map<string, Package> = new Map<string, Package>();\n\n  register(packageName: string, fs: FileSystem, packagePath: FilePath) {\n    this.packages.set(packageName, {fs, packagePath});\n  }\n\n  async install({\n    modules,\n    fs,\n    cwd,\n    packagePath,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (packagePath == null) {\n      packagePath = path.join(cwd, 'package.json');\n      await fs.writeFile(packagePath, '{}');\n    }\n\n    let pkg = JSON.parse(await fs.readFile(packagePath, 'utf8'));\n    let key = saveDev ? 'devDependencies' : 'dependencies';\n\n    if (!pkg[key]) {\n      pkg[key] = {};\n    }\n\n    for (let module of modules) {\n      pkg[key][module.name] =\n        '^' + (await this.installPackage(module, fs, packagePath));\n    }\n\n    await fs.writeFile(packagePath, JSON.stringify(pkg));\n  }\n\n  async installPackage(\n    moduleRequest: ModuleRequest,\n    fs: FileSystem,\n    packagePath: FilePath,\n  ): Promise<any> {\n    let pkg = this.packages.get(moduleRequest.name);\n    if (!pkg) {\n      throw new Error('Unknown package ' + moduleRequest.name);\n    }\n\n    let dest = path.join(\n      path.dirname(packagePath),\n      'node_modules',\n      moduleRequest.name,\n    );\n    await ncp(pkg.fs, pkg.packagePath, fs, dest);\n\n    let packageJSON = JSON.parse(\n      await fs.readFile(path.join(dest, 'package.json'), 'utf8'),\n    );\n\n    if (packageJSON.dependencies != null) {\n      for (let dep of moduleRequestsFromDependencyMap(\n        packageJSON.dependencies,\n      )) {\n        await this.installPackage(dep, fs, packagePath);\n      }\n    }\n\n    return packageJSON.version;\n  }\n}\n\nregisterSerializableClass(\n  `${pkg.version}:MockPackageInstaller`,\n  MockPackageInstaller,\n);\n"], "names": ["registerSerializableClass", "ThrowableDiagnostic", "encodeJSONKeyComponent", "escapeMarkdown", "generateJSONCodeHighlights", "md", "NodeFS", "nativeFS", "<PERSON><PERSON><PERSON>", "path", "semver", "logger", "nullthrows", "getModuleParts", "getConflictingLocalDependencies", "installPackage", "pkg", "ResolverBase", "pathToFileURL", "MAIN", "SOURCE", "ENTRIES", "process", "env", "PARCEL_SELF_BUILD", "cache", "Map", "children", "invalidationsCache", "NodePackageManager", "constructor", "fs", "projectRoot", "installer", "_createResolver", "versions", "pnp", "undefined", "canonicalize", "realpathSync", "read", "readFileSync", "isFile", "statSync", "isDir", "isDirectory", "mode", "entries", "packageExports", "moduleDirResolver", "module", "from", "findPnpApi", "dirname", "resolveToUnqualified", "deserialize", "opts", "serialize", "$$raw", "require", "name", "resolved", "type", "resolve", "warn", "message", "origin", "codeFrames", "filePath", "codeHighlights", "platform", "isAbsolute", "load", "requireSync", "resolveSync", "cachedModule", "_cache", "exports", "m", "parent", "id", "filename", "encoding", "err", "options", "basedir", "key", "get", "resolveInternal", "e", "code", "shouldAutoInstall", "diagnostic", "hints", "conflicts", "invalidate", "install", "range", "saveDev", "fields", "map", "field", "language", "json", "satisfies", "version", "set", "clear", "module<PERSON><PERSON><PERSON><PERSON>", "Set", "add", "modules", "packageInstaller", "getInvalidations", "cached", "res", "invalidateOnFileCreate", "invalidateOnFileChange", "invalidateOnStartup", "seen", "<PERSON><PERSON><PERSON>", "has", "push", "file", "specifier", "invalidations", "resolver", "for<PERSON>ach", "i", "relative", "delete", "specifierType", "error", "Error", "getPkg", "resolution", "pkgPath", "findAncestorFile", "value", "JSON", "parse", "moduleType", "invariant", "resolveConfig", "exec", "_exec", "promisify", "npmSpecifierFromModuleRequest", "moduleRequest", "join", "moduleRequestsFromDependencyMap", "dependencyMap", "Object", "local", "pkgStr", "readFile", "length", "loadConfig", "PromiseQueue", "WorkerFarm", "Npm", "Yarn", "Pnpm", "validateModuleSpecifier", "packageManager", "installPeers", "moduleNames", "progress", "fromPkgPath", "cwd", "determinePackageInstaller", "packagePath", "Promise", "all", "installPeerDependencies", "modulePkg", "config", "peers", "peerDependencies", "assign", "filepath", "configFile", "config<PERSON><PERSON>", "basename", "exists", "queue", "maxConcurrent", "modulesInstalling", "_addToInstallQueue", "request", "modulesToInstall", "filter", "getModuleRequestKey", "then", "run", "isWorker", "workerApi", "getWorkerApi", "bundlePath", "__dirname", "__filename", "callMaster", "location", "args", "method", "spawn", "promiseFromProcess", "NPM_CMD", "writeFile", "concat", "startsWith", "installProcess", "stdout", "on", "buf", "toString", "stderr", "trim", "results", "addedCount", "added", "log", "childProcess", "reject", "commandExists", "split", "JSONParseStream", "YARN_CMD", "<PERSON><PERSON><PERSON><PERSON>", "yarnVersion", "Boolean", "parseInt", "pipe", "prefix", "data", "current", "total", "info", "Transform", "objectMode", "_transform", "chunk", "callback", "parsed", "verbose", "PNPM_CMD", "hasPnpm", "pnpmVersion", "existsSync", "removedCount", "stack", "level", "removed", "str", "MODULE_REGEX", "moduleName", "matches", "ncp", "MockPackageInstaller", "packages", "register", "packageName", "stringify", "dest", "packageJSON", "dependencies", "dep"], "version": 3, "file": "index.js.map", "sourceRoot": "../../../../"}