{"name": "@plasmohq/parcel-resolver", "version": "0.13.1", "description": "Plasmo Parcel Resolver", "files": ["dist"], "main": "dist/index.js", "author": "Plasmo Corp. <<EMAIL>>", "homepage": "https://docs.plasmo.com/", "engines": {"parcel": ">= 2.7.0"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/PlasmoHQ/plasmo.git"}, "devDependencies": {"assert": "2.0.0", "browserify-zlib": "0.2.0", "buffer": "6.0.3", "console-browserify": "1.2.0", "constants-browserify": "1.0.0", "crc-32": "1.2.2", "crypto-browserify": "3.12.0", "domain-browser": "4.22.0", "esbuild": "0.18.10", "events": "3.3.0", "https-browserify": "1.0.0", "os-browserify": "0.3.0", "path-browserify": "1.0.1", "process": "0.11.10", "punycode": "2.3.0", "querystring-es3": "0.2.1", "react-refresh": "0.14.0", "stream-browserify": "3.0.0", "stream-http": "3.2.0", "string_decoder": "1.3.0", "timers-browserify": "2.0.12", "tsup": "7.1.0", "tty-browserify": "0.0.1", "url": "0.11.1", "util": "0.12.5", "vm-browserify": "1.1.2", "@plasmo/config": "0.2.5", "@plasmo/utils": "0.2.3", "@plasmohq/rps": "1.8.7"}, "dependencies": {"@parcel/core": "2.9.3", "@parcel/hash": "2.9.3", "@parcel/plugin": "2.9.3", "@parcel/types": "2.9.3", "fast-glob": "3.2.12", "fs-extra": "11.1.1", "got": "13.0.0"}, "scripts": {"dev": "run-s build:polyfills dev:resolver", "dev:resolver": "tsup src/index.ts --watch", "build": "run-s build:*", "build:resolver": "tsup src/index.ts --minify --clean", "build:polyfills": "node index.mjs"}}