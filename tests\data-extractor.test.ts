// Simple test for the data extractor
// This would be expanded with a proper testing framework in a full implementation

import { GoogleMapsDataExtractor } from '../utils/data-extractor';

// Mock Google Maps API response data
const mockApiResponse = {
  d: `)]}'
[
  [
    [
      [
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        [
          "0x89c25a4c8b8b8b8b:0x1234567890abcdef",
          "Test Restaurant",
          null,
          null,
          null,
          null,
          null,
          [
            "https://example.com",
            "example.com"
          ],
          null,
          [
            null,
            null,
            40.7128,
            -74.0060
          ],
          "Test Restaurant",
          null,
          ["Restaurant", "Food"],
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          [
            [
              null,
              null,
              null,
              null,
              null,
              null,
              "https://example.com/image.jpg"
            ]
          ],
          "123 Main St, New York, NY 10001",
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          [
            "Claimed"
          ],
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          "ChIJd8BlQ2BZwokRAFUEcm_qrcA",
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          "kg:/m/0123456789"
        ]
      ]
    ]
  ]
]`
};

// Test function
function testDataExtractor() {
  console.log('🧪 Testing Google Maps Data Extractor...');
  
  try {
    // Test basic extraction
    const businesses = GoogleMapsDataExtractor.extractBusinessData(mockApiResponse);
    
    console.log('✅ Extraction successful');
    console.log(`📊 Extracted ${businesses.length} businesses`);
    
    if (businesses.length > 0) {
      const business = businesses[0];
      console.log('📍 Sample business data:');
      console.log(`   Name: ${business.name}`);
      console.log(`   Address: ${business.fullAddress}`);
      console.log(`   Website: ${business.website}`);
      console.log(`   Categories: ${business.categories.join(', ')}`);
      console.log(`   Location: ${business.latitude}, ${business.longitude}`);
      console.log(`   Place ID: ${business.placeId}`);
    }
    
    // Test validation
    const validatedBusiness = GoogleMapsDataExtractor.validateBusinessData(businesses[0]);
    
    if (validatedBusiness) {
      console.log('✅ Validation successful');
    } else {
      console.log('❌ Validation failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testDataExtractor = testDataExtractor;
}

export { testDataExtractor };
