{"name": "ts-algebra", "version": "1.2.2", "description": "Types on steroids 💊", "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"release": "bash scripts/release.bash", "set-package-version": "ts-node scripts/setPackageVersion", "test": "yarn test-type && yarn test-format && yarn test-unused-exports && yarn test-lint", "test-type": "tsc --noEmit", "test-format": "yarn prettier . --check", "test-unused-exports": "yarn ts-unused-exports ./tsconfig.json --excludePathsFromReport='src/index.ts;'", "test-lint": "yarn eslint --ext=js,ts .", "format": "yarn prettier . --write", "build": "ttsc -p tsconfig.build.json"}, "dependencies": {}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^3.4.0", "@types/node": "^20.5.7", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "@zerollup/ts-transform-paths": "^1.7.18", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^46.4.6", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unused-imports": "^2.0.0", "prettier": "^2.7.1", "rollup": "^2.67.3", "rollup-plugin-dts": "4.1.0", "rollup-plugin-import-map": "^2.2.2", "ts-node": "^10.9.1", "ts-toolbelt": "^9.6.0", "ts-unused-exports": "^8.0.0", "ttypescript": "^1.5.13", "typescript": "^4.5.5"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ThomasAribart/ts-algebra.git"}, "keywords": ["typescript", "type", "ts"], "bugs": {"url": "https://github.com/ThomasAribart/ts-algebra/issues"}, "homepage": "https://github.com/ThomasAribart/ts-algebra#readme"}