{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"~*": ["./*"]}}, "include": [".plasmo/index.d.ts", "src/**/*", "contents/**/*", "background/**/*", "popup/**/*", "options/**/*", "types/**/*"], "exclude": ["node_modules", "build", "dist"]}