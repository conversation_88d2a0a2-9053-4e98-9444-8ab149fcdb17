import type { BusinessData } from "~types/business";

export class GoogleMapsDataExtractor {
  
  /**
   * Extract business data from Google Maps API response
   */
  static extractBusinessData(apiResponse: any): BusinessData[] {
    try {
      const businesses: BusinessData[] = [];
      
      // Handle different API response formats
      if (apiResponse.d) {
        // Format 1: Response with 'd' property
        const results = this.parseFormat1(apiResponse.d);
        businesses.push(...results);
      } else if (Array.isArray(apiResponse)) {
        // Format 2: Direct array response
        const results = this.parseFormat2(apiResponse);
        businesses.push(...results);
      } else if (apiResponse.results) {
        // Format 3: Response with 'results' property
        const results = this.parseFormat3(apiResponse.results);
        businesses.push(...results);
      }
      
      return businesses;
    } catch (error) {
      console.error('Error extracting business data:', error);
      return [];
    }
  }

  /**
   * Parse format 1: Google Maps internal API format
   */
  private static parseFormat1(data: string): BusinessData[] {
    try {
      // Remove the leading characters and parse JSON
      const cleanData = data.substring(data.indexOf('['));
      const parsed = JSON.parse(cleanData);
      
      if (!Array.isArray(parsed) || parsed.length < 2) return [];
      
      const results = parsed[0]?.[1] || [];
      return results.map((item: any) => this.parseBusinessItem(item)).filter(Boolean);
    } catch (error) {
      console.error('Error parsing format 1:', error);
      return [];
    }
  }

  /**
   * Parse format 2: Direct array format
   */
  private static parseFormat2(data: any[]): BusinessData[] {
    return data.map(item => this.parseBusinessItem(item)).filter(Boolean);
  }

  /**
   * Parse format 3: Standard results format
   */
  private static parseFormat3(results: any[]): BusinessData[] {
    return results.map(item => this.parseBusinessItem(item)).filter(Boolean);
  }

  /**
   * Parse individual business item from various formats
   */
  private static parseBusinessItem(item: any): BusinessData | null {
    try {
      if (!item || !Array.isArray(item)) return null;

      const business: Partial<BusinessData> = {};

      // Extract basic information
      business.name = this.extractValue(item, [11]) || '';
      business.fullAddress = this.extractValue(item, [39]) || '';
      business.categories = this.extractValue(item, [13]) || [];
      
      // Extract contact information
      const phoneData = this.extractValue(item, [178, 0]);
      business.phone = phoneData?.[0] || '';
      business.phones = phoneData?.[1]?.map((p: any) => p[0]) || [];
      
      // Extract website information
      const websiteData = this.extractValue(item, [7]);
      business.website = websiteData?.[0] || '';
      business.domain = websiteData?.[1] || '';
      
      // Extract location data
      const locationData = this.extractValue(item, [9]);
      business.latitude = locationData?.[2] || 0;
      business.longitude = locationData?.[3] || 0;
      
      // Extract plus code
      business.plusCode = this.extractValue(item, [183, 2, 2]) || '';
      
      // Extract ratings and reviews
      const ratingData = this.extractValue(item, [4]);
      business.averageRating = ratingData?.[7] || 0;
      business.reviewCount = ratingData?.[8] || 0;
      business.reviewUrl = ratingData?.[3]?.[0] || '';
      
      // Extract business details
      business.claimed = this.extractValue(item, [49, 0]) !== null;
      business.featuredImage = this.extractValue(item, [37, 0, 0, 6, 0]) || '';
      
      // Extract opening hours
      const hoursData = this.extractValue(item, [34, 1]);
      business.openingHours = this.parseOpeningHours(hoursData);
      
      // Extract identifiers
      const cidData = this.extractValue(item, [10]);
      if (cidData && typeof cidData === 'string') {
        const cidMatch = cidData.match(/:([^:]+)$/);
        business.cid = cidMatch ? BigInt(cidMatch[1]).toString() : '';
      }
      
      business.placeId = this.extractValue(item, [78]) || '';
      business.kgmid = this.extractValue(item, [89]) || '';
      business.googleKnowledgeUrl = this.extractValue(item, [49, 0]) || '';
      
      // Generate Google Maps URL
      if (business.latitude && business.longitude) {
        business.googleMapsUrl = `https://www.google.com/maps/place/${business.name}/@${business.latitude},${business.longitude},17z`;
      }
      
      // Clean up website URL
      if (business.website && !business.website.startsWith('http')) {
        business.website = '';
        business.domain = '';
      }
      
      // Ensure required fields are present
      if (!business.name) return null;
      
      return business as BusinessData;
    } catch (error) {
      console.error('Error parsing business item:', error);
      return null;
    }
  }

  /**
   * Safely extract nested values from arrays
   */
  private static extractValue(obj: any, path: (string | number)[]): any {
    try {
      let current = obj;
      for (const key of path) {
        if (current == null || typeof current !== 'object') return null;
        current = current[key];
      }
      return current;
    } catch {
      return null;
    }
  }

  /**
   * Parse opening hours data
   */
  private static parseOpeningHours(hoursData: any[]): string[] {
    if (!Array.isArray(hoursData)) return [];
    
    try {
      return hoursData.map(dayData => {
        if (!Array.isArray(dayData) || dayData.length < 2) return '';
        
        const dayName = dayData[0] || '';
        const hours = dayData[1]?.[0]?.replace('–', '-') || '';
        
        return `${dayName}: ${hours}`;
      }).filter(Boolean);
    } catch {
      return [];
    }
  }

  /**
   * Validate and clean business data
   */
  static validateBusinessData(business: Partial<BusinessData>): BusinessData | null {
    if (!business.name || !business.fullAddress) {
      return null;
    }

    // Set defaults for required fields
    const validated: BusinessData = {
      name: business.name,
      fullAddress: business.fullAddress,
      street: business.street || '',
      municipality: business.municipality || '',
      categories: business.categories || [],
      phone: business.phone || '',
      phones: business.phones || [],
      website: business.website || '',
      domain: business.domain || '',
      latitude: business.latitude || 0,
      longitude: business.longitude || 0,
      plusCode: business.plusCode || '',
      claimed: business.claimed || false,
      reviewCount: business.reviewCount || 0,
      averageRating: business.averageRating || 0,
      reviewUrl: business.reviewUrl || '',
      googleMapsUrl: business.googleMapsUrl || '',
      openingHours: business.openingHours || [],
      featuredImage: business.featuredImage || '',
      cid: business.cid || '',
      placeId: business.placeId || '',
      googleKnowledgeUrl: business.googleKnowledgeUrl || '',
      kgmid: business.kgmid || '',
      email: business.email,
      socialMedias: business.socialMedias
    };

    return validated;
  }
}
