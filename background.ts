import { StorageManager } from '~utils/storage';

// Background script for the Google Maps Scraper extension
class BackgroundService {
  constructor() {
    this.setupEventListeners();
    this.initializeExtension();
  }

  private setupEventListeners(): void {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Handle messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep the message channel open for async responses
    });

    // Handle tab updates to inject content scripts
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Handle extension startup
    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });
  }

  private async initializeExtension(): Promise<void> {
    try {
      // Initialize default configuration if not exists
      const config = await StorageManager.loadConfig();
      await StorageManager.saveConfig(config);
      
      console.log('🚀 Google Maps Scraper extension initialized');
    } catch (error) {
      console.error('Failed to initialize extension:', error);
    }
  }

  private handleInstallation(details: chrome.runtime.InstalledDetails): void {
    if (details.reason === 'install') {
      // First time installation
      this.showWelcomeMessage();
    } else if (details.reason === 'update') {
      // Extension updated
      this.handleUpdate(details.previousVersion);
    }
  }

  private showWelcomeMessage(): void {
    chrome.tabs.create({
      url: 'https://www.google.com/maps',
      active: true
    });

    // Show notification
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icon48.png',
      title: 'Google Maps Scraper Installed',
      message: 'Navigate to Google Maps and click the extension icon to start scraping business data!'
    });
  }

  private handleUpdate(previousVersion?: string): void {
    console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
    
    // Perform any necessary data migrations here
    this.migrateData(previousVersion);
  }

  private async migrateData(previousVersion?: string): Promise<void> {
    try {
      // Add any data migration logic here for future versions
      console.log('Data migration completed');
    } catch (error) {
      console.error('Data migration failed:', error);
    }
  }

  private async handleMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      switch (message.action) {
        case 'get-storage-stats':
          const stats = await StorageManager.getStorageStats();
          sendResponse({ success: true, data: stats });
          break;

        case 'export-all-data':
          const allData = await StorageManager.exportAllData();
          sendResponse({ success: true, data: allData });
          break;

        case 'import-all-data':
          await StorageManager.importAllData(message.data);
          sendResponse({ success: true });
          break;

        case 'cleanup-old-data':
          const cleanedCount = await StorageManager.cleanupOldData(message.daysToKeep);
          sendResponse({ success: true, cleanedCount });
          break;

        case 'get-config':
          const config = await StorageManager.loadConfig();
          sendResponse({ success: true, config });
          break;

        case 'save-config':
          await StorageManager.saveConfig(message.config);
          sendResponse({ success: true });
          break;

        case 'ping':
          sendResponse({ success: true, message: 'pong' });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private handleTabUpdate(
    tabId: number,
    changeInfo: chrome.tabs.TabChangeInfo,
    tab: chrome.tabs.Tab
  ): void {
    // Check if the tab is a Google Maps page and has finished loading
    if (
      changeInfo.status === 'complete' &&
      tab.url &&
      this.isGoogleMapsUrl(tab.url)
    ) {
      this.injectContentScript(tabId);
    }
  }

  private isGoogleMapsUrl(url: string): boolean {
    const googleMapsPatterns = [
      /^https:\/\/www\.google\.[a-z.]+\/maps/,
      /^https:\/\/maps\.google\.[a-z.]+/
    ];

    return googleMapsPatterns.some(pattern => pattern.test(url));
  }

  private async injectContentScript(tabId: number): Promise<void> {
    try {
      // Check if content script is already injected
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          return (window as any).googleMapsScraperInjected === true;
        }
      });

      if (!results[0]?.result) {
        // Content script not injected, inject it
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['contents/google-maps.js']
        });

        // Mark as injected
        await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            (window as any).googleMapsScraperInjected = true;
          }
        });

        console.log(`Content script injected into tab ${tabId}`);
      }
    } catch (error) {
      console.error('Failed to inject content script:', error);
    }
  }

  private handleStartup(): void {
    console.log('Extension started');
  }
}

// Initialize the background service
new BackgroundService();

// Handle extension context invalidation
chrome.runtime.onSuspend.addListener(() => {
  console.log('Extension context suspended');
});

// Utility functions for other parts of the extension
export const backgroundUtils = {
  /**
   * Send message to content script in active tab
   */
  async sendToActiveTab(message: any): Promise<any> {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab.id) {
        return await chrome.tabs.sendMessage(tab.id, message);
      }
      throw new Error('No active tab found');
    } catch (error) {
      console.error('Failed to send message to active tab:', error);
      throw error;
    }
  },

  /**
   * Check if current tab is Google Maps
   */
  async isCurrentTabGoogleMaps(): Promise<boolean> {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      return tab.url ? /google\.[a-z.]+\/maps/.test(tab.url) : false;
    } catch (error) {
      console.error('Failed to check current tab:', error);
      return false;
    }
  },

  /**
   * Open Google Maps in new tab
   */
  async openGoogleMaps(): Promise<void> {
    try {
      await chrome.tabs.create({
        url: 'https://www.google.com/maps',
        active: true
      });
    } catch (error) {
      console.error('Failed to open Google Maps:', error);
      throw error;
    }
  }
};
