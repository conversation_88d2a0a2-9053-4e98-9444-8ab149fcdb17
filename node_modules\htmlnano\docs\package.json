{"name": "htmlnano-docs", "version": "1.0.0", "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "2.2.0", "@docusaurus/preset-classic": "2.2.0", "@mdx-js/react": "^1.6.21", "@svgr/webpack": "^6.5.1", "clsx": "^1.1.1", "docusaurus-plugin-goatcounter": "^2.0.1", "file-loader": "^6.2.0", "prism-react-renderer": "^1.2.1", "react": "^17.0.1", "react-dom": "^17.0.1", "url-loader": "^4.1.1"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}