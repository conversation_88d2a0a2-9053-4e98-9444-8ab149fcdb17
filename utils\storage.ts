import type { BusinessData, ScrapingConfig, StorageData } from '~types/business';

export class StorageManager {
  private static readonly STORAGE_KEYS = {
    BUSINESSES: 'scrapedBusinesses',
    CONFIG: 'scrapingConfig',
    LAST_SCRAPE: 'lastScrapeDate',
    SESSION_DATA: 'sessionData'
  } as const;

  /**
   * Save businesses to storage
   */
  static async saveBusinesses(businesses: BusinessData[]): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.BUSINESSES]: businesses,
        [this.STORAGE_KEYS.LAST_SCRAPE]: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to save businesses:', error);
      throw error;
    }
  }

  /**
   * Load businesses from storage
   */
  static async loadBusinesses(): Promise<BusinessData[]> {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEYS.BUSINESSES]);
      return result[this.STORAGE_KEYS.BUSINESSES] || [];
    } catch (error) {
      console.error('Failed to load businesses:', error);
      return [];
    }
  }

  /**
   * Add new businesses to existing storage
   */
  static async addBusinesses(newBusinesses: BusinessData[]): Promise<BusinessData[]> {
    try {
      const existingBusinesses = await this.loadBusinesses();
      const existingPlaceIds = new Set(existingBusinesses.map(b => b.placeId));
      
      // Filter out duplicates
      const uniqueNewBusinesses = newBusinesses.filter(
        business => business.placeId && !existingPlaceIds.has(business.placeId)
      );
      
      const allBusinesses = [...existingBusinesses, ...uniqueNewBusinesses];
      await this.saveBusinesses(allBusinesses);
      
      return allBusinesses;
    } catch (error) {
      console.error('Failed to add businesses:', error);
      throw error;
    }
  }

  /**
   * Clear all businesses from storage
   */
  static async clearBusinesses(): Promise<void> {
    try {
      await chrome.storage.local.remove([
        this.STORAGE_KEYS.BUSINESSES,
        this.STORAGE_KEYS.LAST_SCRAPE
      ]);
    } catch (error) {
      console.error('Failed to clear businesses:', error);
      throw error;
    }
  }

  /**
   * Save scraping configuration
   */
  static async saveConfig(config: ScrapingConfig): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.CONFIG]: config
      });
    } catch (error) {
      console.error('Failed to save config:', error);
      throw error;
    }
  }

  /**
   * Load scraping configuration
   */
  static async loadConfig(): Promise<ScrapingConfig> {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEYS.CONFIG]);
      return result[this.STORAGE_KEYS.CONFIG] || this.getDefaultConfig();
    } catch (error) {
      console.error('Failed to load config:', error);
      return this.getDefaultConfig();
    }
  }

  /**
   * Get default configuration
   */
  static getDefaultConfig(): ScrapingConfig {
    return {
      autoScroll: true,
      scrollDelay: 2000,
      maxResults: 1000,
      includeEmails: false,
      includeSocialMedia: false
    };
  }

  /**
   * Save session data (temporary data that doesn't persist between browser sessions)
   */
  static async saveSessionData(data: any): Promise<void> {
    try {
      await chrome.storage.session.set({
        [this.STORAGE_KEYS.SESSION_DATA]: data
      });
    } catch (error) {
      console.error('Failed to save session data:', error);
      // Fallback to local storage if session storage is not available
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.SESSION_DATA]: data
      });
    }
  }

  /**
   * Load session data
   */
  static async loadSessionData(): Promise<any> {
    try {
      let result = await chrome.storage.session.get([this.STORAGE_KEYS.SESSION_DATA]);
      
      if (!result[this.STORAGE_KEYS.SESSION_DATA]) {
        // Fallback to local storage
        result = await chrome.storage.local.get([this.STORAGE_KEYS.SESSION_DATA]);
      }
      
      return result[this.STORAGE_KEYS.SESSION_DATA] || {};
    } catch (error) {
      console.error('Failed to load session data:', error);
      return {};
    }
  }

  /**
   * Get storage usage statistics
   */
  static async getStorageStats(): Promise<{
    businessCount: number;
    storageUsed: number;
    lastScrapeDate: string | null;
  }> {
    try {
      const [businesses, result] = await Promise.all([
        this.loadBusinesses(),
        chrome.storage.local.get([this.STORAGE_KEYS.LAST_SCRAPE])
      ]);

      // Calculate approximate storage usage
      const storageUsed = new Blob([JSON.stringify(businesses)]).size;

      return {
        businessCount: businesses.length,
        storageUsed,
        lastScrapeDate: result[this.STORAGE_KEYS.LAST_SCRAPE] || null
      };
    } catch (error) {
      console.error('Failed to get storage stats:', error);
      return {
        businessCount: 0,
        storageUsed: 0,
        lastScrapeDate: null
      };
    }
  }

  /**
   * Export all data for backup
   */
  static async exportAllData(): Promise<StorageData> {
    try {
      const [businesses, config] = await Promise.all([
        this.loadBusinesses(),
        this.loadConfig()
      ]);

      const result = await chrome.storage.local.get([this.STORAGE_KEYS.LAST_SCRAPE]);

      return {
        businesses,
        config,
        lastScrapeDate: result[this.STORAGE_KEYS.LAST_SCRAPE] || new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to export all data:', error);
      throw error;
    }
  }

  /**
   * Import data from backup
   */
  static async importAllData(data: StorageData): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEYS.BUSINESSES]: data.businesses,
        [this.STORAGE_KEYS.CONFIG]: data.config,
        [this.STORAGE_KEYS.LAST_SCRAPE]: data.lastScrapeDate
      });
    } catch (error) {
      console.error('Failed to import all data:', error);
      throw error;
    }
  }

  /**
   * Clean up old data (remove businesses older than specified days)
   */
  static async cleanupOldData(daysToKeep: number = 30): Promise<number> {
    try {
      const businesses = await this.loadBusinesses();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // For now, we don't have timestamps on individual businesses
      // This could be enhanced to track when each business was scraped
      const result = await chrome.storage.local.get([this.STORAGE_KEYS.LAST_SCRAPE]);
      const lastScrapeDate = result[this.STORAGE_KEYS.LAST_SCRAPE];

      if (lastScrapeDate && new Date(lastScrapeDate) < cutoffDate) {
        await this.clearBusinesses();
        return businesses.length;
      }

      return 0;
    } catch (error) {
      console.error('Failed to cleanup old data:', error);
      return 0;
    }
  }

  /**
   * Listen for storage changes
   */
  static onStorageChanged(callback: (changes: any) => void): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local') {
        callback(changes);
      }
    });
  }

  /**
   * Remove storage change listener
   */
  static removeStorageListener(callback: (changes: any) => void): void {
    chrome.storage.onChanged.removeListener(callback);
  }
}

/**
 * State manager for handling application state
 */
export class StateManager {
  private static listeners: Map<string, Function[]> = new Map();

  /**
   * Subscribe to state changes
   */
  static subscribe(event: string, callback: Function): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    
    this.listeners.get(event)!.push(callback);
    
    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Emit state change event
   */
  static emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in state change callback:', error);
        }
      });
    }
  }

  /**
   * Clear all listeners
   */
  static clearListeners(): void {
    this.listeners.clear();
  }
}
