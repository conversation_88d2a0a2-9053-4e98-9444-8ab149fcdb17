export interface BusinessData {
  // Basic Information
  name: string;
  fullAddress: string;
  street: string;
  municipality: string;
  categories: string[];
  
  // Contact Information
  phone: string;
  phones: string[];
  website: string;
  domain: string;
  
  // Location Data
  latitude: number;
  longitude: number;
  plusCode: string;
  
  // Business Details
  claimed: boolean;
  reviewCount: number;
  averageRating: number;
  reviewUrl: string;
  googleMapsUrl: string;
  openingHours: string[];
  featuredImage: string;
  
  // Identifiers
  cid: string;
  placeId: string;
  googleKnowledgeUrl: string;
  kgmid: string;
  
  // Enhanced Data (for future features)
  email?: string;
  socialMedias?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    yelp?: string;
    [key: string]: string | undefined;
  };
}

export interface ScrapingState {
  isActive: boolean;
  status: 'idle' | 'finding' | 'paused' | 'finished';
  totalFound: number;
  currentIndex: number;
  hasNextPage: boolean;
}

export interface ScrapingConfig {
  autoScroll: boolean;
  scrollDelay: number;
  maxResults: number;
  includeEmails: boolean;
  includeSocialMedia: boolean;
}

export interface ExportOptions {
  format: 'csv' | 'xlsx';
  filename: string;
  fields: (keyof BusinessData)[];
}

export interface StorageData {
  businesses: BusinessData[];
  config: ScrapingConfig;
  lastScrapeDate: string;
}
