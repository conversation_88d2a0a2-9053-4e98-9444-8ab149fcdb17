import React, { useState, useEffect } from 'react';
import { Play, Pause, Square, Download, Trash2, Settings } from 'lucide-react';
import type { ScrapingState, BusinessData } from '~types/business';

interface ScrapingOverlayProps {
  state: ScrapingState;
  businesses: BusinessData[];
  onStart: () => void;
  onStop: () => void;
  onClear: () => void;
  onExport: (format: 'csv' | 'xlsx') => void;
}

export const ScrapingOverlay: React.FC<ScrapingOverlayProps> = ({
  state,
  businesses,
  onStart,
  onStop,
  onClear,
  onExport
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [position, setPosition] = useState({ x: 20, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y
        });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  const getStatusColor = () => {
    switch (state.status) {
      case 'finding': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'finished': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (state.status) {
      case 'finding': return 'Scraping...';
      case 'paused': return 'Paused';
      case 'finished': return 'Finished';
      default: return 'Ready';
    }
  };

  return (
    <div
      className="fixed z-[10000] bg-white rounded-lg shadow-lg border border-gray-200 select-none"
      style={{
        left: position.x,
        top: position.y,
        width: isMinimized ? '200px' : '320px'
      }}
    >
      {/* Header */}
      <div
        className="flex items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg cursor-move"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
          <span className="font-medium text-sm">Maps Scraper</span>
        </div>
        <button
          onClick={() => setIsMinimized(!isMinimized)}
          className="text-white hover:bg-blue-700 rounded p-1"
        >
          {isMinimized ? '□' : '−'}
        </button>
      </div>

      {!isMinimized && (
        <div className="p-4">
          {/* Status */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              <span className="text-sm text-gray-600">{getStatusText()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">Found:</span>
              <span className="text-sm font-bold text-blue-600">{state.totalFound}</span>
            </div>
          </div>

          {/* Controls */}
          <div className="flex space-x-2 mb-4">
            {!state.isActive ? (
              <button
                onClick={onStart}
                className="flex-1 flex items-center justify-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm"
              >
                <Play size={14} />
                <span>Start</span>
              </button>
            ) : (
              <button
                onClick={onStop}
                className="flex-1 flex items-center justify-center space-x-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm"
              >
                <Square size={14} />
                <span>Stop</span>
              </button>
            )}
            
            <button
              onClick={onClear}
              className="flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm"
              title="Clear Data"
            >
              <Trash2 size={14} />
            </button>
          </div>

          {/* Export Options */}
          {state.totalFound > 0 && (
            <div className="border-t pt-3">
              <div className="text-sm font-medium text-gray-700 mb-2">Export:</div>
              <div className="flex space-x-2">
                <button
                  onClick={() => onExport('csv')}
                  className="flex-1 flex items-center justify-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
                >
                  <Download size={14} />
                  <span>CSV</span>
                </button>
                <button
                  onClick={() => onExport('xlsx')}
                  className="flex-1 flex items-center justify-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm"
                >
                  <Download size={14} />
                  <span>Excel</span>
                </button>
              </div>
            </div>
          )}

          {/* Progress Info */}
          {state.isActive && (
            <div className="mt-3 text-xs text-gray-500">
              {state.hasNextPage ? (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>Loading more results...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                  <span>Scroll or search for more results</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
