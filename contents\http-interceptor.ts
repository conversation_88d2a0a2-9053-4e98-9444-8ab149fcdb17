// HTTP Interceptor for Google Maps API calls
// This script will be injected into the page to intercept XMLHttpRequest and fetch calls

export interface InterceptedRequest {
  url: string;
  data: any;
  timestamp: number;
}

export class HttpInterceptor {
  private originalXHR: typeof XMLHttpRequest;
  private originalFetch: typeof fetch;
  private isActive: boolean = false;

  constructor() {
    this.originalXHR = window.XMLHttpRequest;
    this.originalFetch = window.fetch;
  }

  start() {
    if (this.isActive) return;
    
    this.isActive = true;
    this.interceptXHR();
    this.interceptFetch();
    console.log('🔍 HTTP Interceptor started');
  }

  stop() {
    if (!this.isActive) return;
    
    this.isActive = false;
    window.XMLHttpRequest = this.originalXHR;
    window.fetch = this.originalFetch;
    console.log('🛑 HTTP Interceptor stopped');
  }

  private interceptXHR() {
    const self = this;
    
    window.XMLHttpRequest = function() {
      const xhr = new self.originalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;
      
      let requestUrl = '';
      let requestData: any = null;

      xhr.open = function(method: string, url: string, ...args: any[]) {
        requestUrl = url;
        return originalOpen.apply(this, [method, url, ...args]);
      };

      xhr.send = function(data?: any) {
        requestData = data;
        return originalSend.apply(this, [data]);
      };

      xhr.addEventListener('readystatechange', function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
          self.handleResponse(requestUrl, xhr.responseText, requestData);
        }
      });

      return xhr;
    } as any;

    // Copy static properties
    Object.setPrototypeOf(window.XMLHttpRequest, this.originalXHR);
    Object.defineProperty(window.XMLHttpRequest, 'prototype', {
      value: this.originalXHR.prototype,
      writable: false
    });
  }

  private interceptFetch() {
    const self = this;
    
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      const url = typeof input === 'string' ? input : input.toString();
      
      try {
        const response = await self.originalFetch(input, init);
        
        // Clone the response to avoid consuming it
        const clonedResponse = response.clone();
        
        if (response.ok) {
          const responseText = await clonedResponse.text();
          self.handleResponse(url, responseText, init?.body);
        }
        
        return response;
      } catch (error) {
        throw error;
      }
    };
  }

  private handleResponse(url: string, responseText: string, requestData?: any) {
    // Check if this is a Google Maps search API call
    if (this.isGoogleMapsSearchAPI(url)) {
      try {
        const data = JSON.parse(responseText);
        this.dispatchInterceptedData({
          url,
          data,
          timestamp: Date.now()
        });
      } catch (error) {
        console.warn('Failed to parse response data:', error);
      }
    }
  }

  private isGoogleMapsSearchAPI(url: string): boolean {
    // Check for Google Maps search API patterns
    const patterns = [
      '/search',
      '/place/details',
      '/place/nearbysearch',
      '/place/textsearch',
      'maps/api/place',
      'maps/preview/place'
    ];
    
    return patterns.some(pattern => url.includes(pattern));
  }

  private dispatchInterceptedData(data: InterceptedRequest) {
    // Dispatch custom event with intercepted data
    const event = new CustomEvent('gmaps-api-intercepted', {
      detail: data
    });
    
    window.dispatchEvent(event);
  }
}

// Create and export the interceptor instance
export const httpInterceptor = new HttpInterceptor();

// Auto-start when script loads
if (typeof window !== 'undefined') {
  httpInterceptor.start();
}
