import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import type { BusinessData, ExportOptions } from '~types/business';

export class DataExporter {
  
  /**
   * Export business data to CSV format
   */
  static exportToCSV(businesses: BusinessData[], options?: Partial<ExportOptions>): void {
    const filename = options?.filename || `google-maps-data-${new Date().toISOString().split('T')[0]}.csv`;
    const fields = options?.fields || this.getDefaultFields();
    
    // Prepare data for CSV export
    const csvData = businesses.map(business => {
      const row: any = {};
      
      fields.forEach(field => {
        let value = business[field];
        
        // Handle special formatting for certain fields
        switch (field) {
          case 'categories':
            value = Array.isArray(value) ? value.join('; ') : value;
            break;
          case 'phones':
            value = Array.isArray(value) ? value.join('; ') : value;
            break;
          case 'openingHours':
            value = Array.isArray(value) ? value.join(' | ') : value;
            break;
          case 'socialMedias':
            if (value && typeof value === 'object') {
              value = Object.entries(value)
                .filter(([_, url]) => url)
                .map(([platform, url]) => `${platform}: ${url}`)
                .join('; ');
            }
            break;
          default:
            // Convert to string and handle null/undefined
            value = value != null ? String(value) : '';
        }
        
        row[this.getFieldLabel(field)] = value;
      });
      
      return row;
    });

    // Convert to CSV
    const csv = Papa.unparse(csvData, {
      header: true,
      delimiter: ',',
      quotes: true
    });

    // Download file
    this.downloadFile(csv, filename, 'text/csv');
  }

  /**
   * Export business data to Excel format
   */
  static exportToExcel(businesses: BusinessData[], options?: Partial<ExportOptions>): void {
    const filename = options?.filename || `google-maps-data-${new Date().toISOString().split('T')[0]}.xlsx`;
    const fields = options?.fields || this.getDefaultFields();

    // Prepare data for Excel export
    const excelData = businesses.map(business => {
      const row: any = {};
      
      fields.forEach(field => {
        let value = business[field];
        
        // Handle special formatting for Excel
        switch (field) {
          case 'categories':
            value = Array.isArray(value) ? value.join('; ') : value;
            break;
          case 'phones':
            value = Array.isArray(value) ? value.join('; ') : value;
            break;
          case 'openingHours':
            value = Array.isArray(value) ? value.join('\n') : value;
            break;
          case 'socialMedias':
            if (value && typeof value === 'object') {
              value = Object.entries(value)
                .filter(([_, url]) => url)
                .map(([platform, url]) => `${platform}: ${url}`)
                .join('\n');
            }
            break;
          case 'latitude':
          case 'longitude':
          case 'averageRating':
          case 'reviewCount':
            // Keep as numbers for Excel
            value = typeof value === 'number' ? value : parseFloat(String(value)) || 0;
            break;
          default:
            value = value != null ? String(value) : '';
        }
        
        row[this.getFieldLabel(field)] = value;
      });
      
      return row;
    });

    // Create workbook
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Auto-size columns
    const columnWidths = this.calculateColumnWidths(excelData, fields);
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Google Maps Data');

    // Generate Excel file and download
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    this.downloadBlob(blob, filename);
  }

  /**
   * Export business data to JSON format
   */
  static exportToJSON(businesses: BusinessData[], options?: Partial<ExportOptions>): void {
    const filename = options?.filename || `google-maps-data-${new Date().toISOString().split('T')[0]}.json`;
    const fields = options?.fields;

    let exportData = businesses;
    
    // Filter fields if specified
    if (fields && fields.length > 0) {
      exportData = businesses.map(business => {
        const filtered: any = {};
        fields.forEach(field => {
          filtered[field] = business[field];
        });
        return filtered;
      });
    }

    const jsonString = JSON.stringify(exportData, null, 2);
    this.downloadFile(jsonString, filename, 'application/json');
  }

  /**
   * Get default fields for export
   */
  private static getDefaultFields(): (keyof BusinessData)[] {
    return [
      'name',
      'fullAddress',
      'street',
      'municipality',
      'categories',
      'phone',
      'phones',
      'website',
      'domain',
      'latitude',
      'longitude',
      'plusCode',
      'claimed',
      'reviewCount',
      'averageRating',
      'reviewUrl',
      'googleMapsUrl',
      'openingHours',
      'featuredImage',
      'cid',
      'placeId',
      'googleKnowledgeUrl',
      'kgmid',
      'email',
      'socialMedias'
    ];
  }

  /**
   * Get human-readable field labels
   */
  private static getFieldLabel(field: keyof BusinessData): string {
    const labels: Record<keyof BusinessData, string> = {
      name: 'Business Name',
      fullAddress: 'Full Address',
      street: 'Street',
      municipality: 'Municipality',
      categories: 'Categories',
      phone: 'Primary Phone',
      phones: 'All Phones',
      website: 'Website',
      domain: 'Domain',
      latitude: 'Latitude',
      longitude: 'Longitude',
      plusCode: 'Plus Code',
      claimed: 'Claimed',
      reviewCount: 'Review Count',
      averageRating: 'Average Rating',
      reviewUrl: 'Review URL',
      googleMapsUrl: 'Google Maps URL',
      openingHours: 'Opening Hours',
      featuredImage: 'Featured Image',
      cid: 'CID',
      placeId: 'Place ID',
      googleKnowledgeUrl: 'Google Knowledge URL',
      kgmid: 'KGMID',
      email: 'Email',
      socialMedias: 'Social Media'
    };

    return labels[field] || field;
  }

  /**
   * Calculate optimal column widths for Excel
   */
  private static calculateColumnWidths(data: any[], fields: (keyof BusinessData)[]): any[] {
    const widths: any[] = [];
    
    fields.forEach((field, index) => {
      const label = this.getFieldLabel(field);
      let maxWidth = label.length;
      
      // Check data values for this column
      data.forEach(row => {
        const value = String(row[label] || '');
        maxWidth = Math.max(maxWidth, value.length);
      });
      
      // Set reasonable limits
      maxWidth = Math.min(Math.max(maxWidth, 10), 50);
      
      widths.push({ wch: maxWidth });
    });
    
    return widths;
  }

  /**
   * Download file with given content
   */
  private static downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    this.downloadBlob(blob, filename);
  }

  /**
   * Download blob as file
   */
  private static downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 100);
  }

  /**
   * Generate summary statistics for exported data
   */
  static generateSummary(businesses: BusinessData[]): any {
    const summary = {
      totalBusinesses: businesses.length,
      withPhone: businesses.filter(b => b.phone).length,
      withWebsite: businesses.filter(b => b.website).length,
      withEmail: businesses.filter(b => b.email).length,
      withRating: businesses.filter(b => b.averageRating > 0).length,
      averageRating: 0,
      topCategories: {} as Record<string, number>,
      exportDate: new Date().toISOString()
    };

    // Calculate average rating
    const ratedBusinesses = businesses.filter(b => b.averageRating > 0);
    if (ratedBusinesses.length > 0) {
      summary.averageRating = ratedBusinesses.reduce((sum, b) => sum + b.averageRating, 0) / ratedBusinesses.length;
    }

    // Count categories
    businesses.forEach(business => {
      business.categories.forEach(category => {
        summary.topCategories[category] = (summary.topCategories[category] || 0) + 1;
      });
    });

    return summary;
  }
}
