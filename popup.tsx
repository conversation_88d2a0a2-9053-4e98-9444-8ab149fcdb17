import React, { useState, useEffect } from 'react';
import { Play, Pause, Square, Download, Trash2, Settings, MapPin, Phone, Globe, Star } from 'lucide-react';
import type { BusinessData, ScrapingState } from '~types/business';

const Popup: React.FC = () => {
  const [state, setState] = useState<ScrapingState>({
    isActive: false,
    status: 'idle',
    totalFound: 0,
    currentIndex: 0,
    hasNextPage: false
  });
  const [businesses, setBusinesses] = useState<BusinessData[]>([]);
  const [activeTab, setActiveTab] = useState<'control' | 'data' | 'settings'>('control');
  const [isGoogleMapsPage, setIsGoogleMapsPage] = useState(false);

  useEffect(() => {
    checkCurrentPage();
    loadStoredData();
    setupMessageListener();
  }, []);

  const checkCurrentPage = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isGoogleMaps = tab.url?.includes('google.com/maps') || false;
      setIsGoogleMapsPage(isGoogleMaps);
    } catch (error) {
      console.error('Error checking current page:', error);
    }
  };

  const loadStoredData = async () => {
    try {
      const result = await chrome.storage.local.get(['scrapedBusinesses']);
      if (result.scrapedBusinesses) {
        setBusinesses(result.scrapedBusinesses);
        setState(prev => ({ ...prev, totalFound: result.scrapedBusinesses.length }));
      }
    } catch (error) {
      console.error('Error loading stored data:', error);
    }
  };

  const setupMessageListener = () => {
    chrome.runtime.onMessage.addListener((message) => {
      if (message.action === 'state-updated') {
        setState(message.state);
      }
    });
  };

  const sendMessageToContentScript = async (action: string, data?: any) => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab.id) {
        return await chrome.tabs.sendMessage(tab.id, { action, ...data });
      }
    } catch (error) {
      console.error('Error sending message to content script:', error);
      return { error: 'Failed to communicate with content script' };
    }
  };

  const handleStart = async () => {
    const response = await sendMessageToContentScript('start-scraping');
    if (response?.success) {
      setState(prev => ({ ...prev, isActive: true, status: 'finding' }));
    }
  };

  const handleStop = async () => {
    const response = await sendMessageToContentScript('stop-scraping');
    if (response?.success) {
      setState(prev => ({ ...prev, isActive: false, status: 'idle' }));
    }
  };

  const handleClear = async () => {
    const response = await sendMessageToContentScript('clear-data');
    if (response?.success) {
      setBusinesses([]);
      setState(prev => ({ ...prev, totalFound: 0 }));
    }
  };

  const handleExport = async (format: 'csv' | 'xlsx') => {
    const response = await sendMessageToContentScript('export-data');
    if (response?.businesses) {
      if (format === 'csv') {
        exportToCSV(response.businesses);
      } else {
        exportToExcel(response.businesses);
      }
    }
  };

  const exportToCSV = (data: BusinessData[]) => {
    const headers = [
      'Name', 'Full Address', 'Phone', 'Website', 'Categories', 
      'Rating', 'Review Count', 'Latitude', 'Longitude', 'Place ID'
    ];
    
    const csvContent = [
      headers.join(','),
      ...data.map(business => [
        `"${business.name}"`,
        `"${business.fullAddress}"`,
        `"${business.phone}"`,
        `"${business.website}"`,
        `"${business.categories.join('; ')}"`,
        business.averageRating,
        business.reviewCount,
        business.latitude,
        business.longitude,
        `"${business.placeId}"`
      ].join(','))
    ].join('\n');

    downloadFile(csvContent, 'google-maps-data.csv', 'text/csv');
  };

  const exportToExcel = async (data: BusinessData[]) => {
    // For now, export as CSV. In a full implementation, you'd use a library like xlsx
    exportToCSV(data);
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusColor = () => {
    switch (state.status) {
      case 'finding': return 'text-green-600';
      case 'paused': return 'text-yellow-600';
      case 'finished': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (state.status) {
      case 'finding': return 'Scraping...';
      case 'paused': return 'Paused';
      case 'finished': return 'Finished';
      default: return 'Ready';
    }
  };

  if (!isGoogleMapsPage) {
    return (
      <div className="w-80 p-6 text-center">
        <MapPin className="mx-auto mb-4 text-gray-400" size={48} />
        <h2 className="text-lg font-semibold text-gray-700 mb-2">
          Navigate to Google Maps
        </h2>
        <p className="text-gray-500 text-sm">
          This extension only works on Google Maps pages. Please navigate to Google Maps to start scraping business data.
        </p>
      </div>
    );
  }

  return (
    <div className="w-80 bg-white">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <h1 className="text-lg font-semibold">Google Maps Scraper</h1>
        <div className="flex items-center justify-between mt-2">
          <span className={`text-sm ${getStatusColor()}`}>{getStatusText()}</span>
          <span className="text-sm">Found: {state.totalFound}</span>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b">
        {[
          { id: 'control', label: 'Control', icon: Play },
          { id: 'data', label: 'Data', icon: MapPin },
          { id: 'settings', label: 'Settings', icon: Settings }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-1 py-3 text-sm ${
              activeTab === tab.id
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <tab.icon size={16} />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'control' && (
          <div className="space-y-4">
            {/* Controls */}
            <div className="flex space-x-2">
              {!state.isActive ? (
                <button
                  onClick={handleStart}
                  className="flex-1 flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                >
                  <Play size={16} />
                  <span>Start Scraping</span>
                </button>
              ) : (
                <button
                  onClick={handleStop}
                  className="flex-1 flex items-center justify-center space-x-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
                >
                  <Square size={16} />
                  <span>Stop</span>
                </button>
              )}
              
              <button
                onClick={handleClear}
                className="flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded"
                title="Clear Data"
              >
                <Trash2 size={16} />
              </button>
            </div>

            {/* Export */}
            {state.totalFound > 0 && (
              <div className="border-t pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Export Data:</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleExport('csv')}
                    className="flex-1 flex items-center justify-center space-x-1 bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
                  >
                    <Download size={14} />
                    <span>CSV</span>
                  </button>
                  <button
                    onClick={() => handleExport('xlsx')}
                    className="flex-1 flex items-center justify-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm"
                  >
                    <Download size={14} />
                    <span>Excel</span>
                  </button>
                </div>
              </div>
            )}

            {/* Status Info */}
            {state.isActive && (
              <div className="bg-gray-50 p-3 rounded text-sm">
                {state.hasNextPage ? (
                  <div className="flex items-center space-x-2 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span>Loading more results...</span>
                  </div>
                ) : (
                  <div className="text-gray-600">
                    Scroll down or search for more results to continue scraping.
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === 'data' && (
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">
              Recent Results ({businesses.length})
            </h3>
            
            {businesses.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MapPin className="mx-auto mb-2" size={32} />
                <p className="text-sm">No data collected yet</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {businesses.slice(-10).reverse().map((business, index) => (
                  <div key={index} className="border rounded p-2 text-xs">
                    <div className="font-medium text-gray-800 truncate">
                      {business.name}
                    </div>
                    <div className="text-gray-600 truncate">
                      {business.fullAddress}
                    </div>
                    <div className="flex items-center space-x-3 mt-1">
                      {business.phone && (
                        <div className="flex items-center space-x-1">
                          <Phone size={10} />
                          <span>{business.phone}</span>
                        </div>
                      )}
                      {business.averageRating > 0 && (
                        <div className="flex items-center space-x-1">
                          <Star size={10} />
                          <span>{business.averageRating}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700">Settings</h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Auto-scroll delay (seconds)
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  defaultValue="2"
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                />
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Max results per session
                </label>
                <input
                  type="number"
                  min="10"
                  max="1000"
                  defaultValue="100"
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="autoExport"
                  className="rounded"
                />
                <label htmlFor="autoExport" className="text-xs text-gray-700">
                  Auto-export when finished
                </label>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Popup;
