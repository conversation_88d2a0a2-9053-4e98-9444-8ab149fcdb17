{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iBAAiB;AACjB,4BAAM,IAAI,GAAG;;AAIb,SAAS,mCAAc,IAAI,EAAE,OAAO,EAAE;IACpC,IAAI,UAAU,QAAQ,OAAO,KAAK,YAChC,QAAQ,OAAO,GAAG,QAAQ,GAAG,CAAC,OAAO;IAEvC,IAAI,CAAC,SACH,OAAO,IAAI;IAGb,UAAU,QAAQ,KAAK,CAAC;IACxB,IAAI,QAAQ,OAAO,CAAC,QAAQ,IAC1B,OAAO,IAAI;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC,WAAW;QAC9B,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,OAAO,GAChD,OAAO,IAAI;IAEf;IACA,OAAO,KAAK;AACd;AAEA,SAAS,gCAAW,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;IACvC,IAAI,CAAC,KAAK,cAAc,MAAM,CAAC,KAAK,MAAM,IACxC,OAAO,KAAK;IAEd,OAAO,mCAAa,MAAM;AAC5B;AAEA,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;IACjC,eAAQ,MAAM,SAAU,EAAE,EAAE,IAAI,EAAE;QAChC,GAAG,IAAI,KAAK,KAAK,GAAG,gCAAU,MAAM,MAAM,QAAQ;IACpD;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO,EAAE;IAC5B,OAAO,gCAAU,mBAAY,OAAO,MAAM;AAC5C;;;;;ACzCA,iBAAiB;AACjB,4BAAM,IAAI,GAAG;;AAIb,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;IACjC,eAAQ,MAAM,SAAU,EAAE,EAAE,IAAI,EAAE;QAChC,GAAG,IAAI,KAAK,KAAK,GAAG,gCAAU,MAAM,QAAQ;IAC9C;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO,EAAE;IAC5B,OAAO,gCAAU,mBAAY,OAAO;AACtC;AAEA,SAAS,gCAAW,IAAI,EAAE,OAAO,EAAE;IACjC,OAAO,KAAK,MAAM,MAAM,gCAAU,MAAM;AAC1C;AAEA,SAAS,gCAAW,IAAI,EAAE,OAAO,EAAE;IACjC,IAAI,MAAM,KAAK,IAAI;IACnB,IAAI,MAAM,KAAK,GAAG;IAClB,IAAI,MAAM,KAAK,GAAG;IAElB,IAAI,QAAQ,QAAQ,GAAG,KAAK,YAC1B,QAAQ,GAAG,GAAG,QAAQ,MAAM,IAAI,QAAQ,MAAM,EAAE;IAClD,IAAI,QAAQ,QAAQ,GAAG,KAAK,YAC1B,QAAQ,GAAG,GAAG,QAAQ,MAAM,IAAI,QAAQ,MAAM,EAAE;IAElD,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,KAAK,IAAI;IAEb,IAAI,MAAM,AAAC,MAAM,KACf,AAAC,MAAM,KAAM,QAAQ,SACrB,AAAC,MAAM,KAAM,QAAQ,SACrB,AAAC,MAAM,MAAO,UAAU;IAE1B,OAAO;AACT;;;;;ACxCA;AACA,iBAAiB,CAAA,OAAQ;IACxB,OAAO,QAAQ,CAAC;IAEhB,MAAM,MAAM,KAAK,GAAG,IAAI,QAAQ,GAAG;IACnC,MAAM,WAAW,KAAK,QAAQ,IAAI,QAAQ,QAAQ;IAElD,IAAI,aAAa,SAChB,OAAO;IAGR,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,OAAO,WAAW;AAClE;;;;;ACZA;;qCAEI;;yCACA;;;AAGJ,IAAI,+BAAS,UAAG,MAAM;AACtB,IAAI,mCAAa,UAAG,UAAU;AAC9B,IAAI,kCAAY,UAAG,SAAS,IAAI;AAEhC,IAAI,uCAAiB,QAAQ,QAAQ,IAAI;AAEzC,IAAI,sCAAgB,SAAS,WAAW,EAAE,QAAQ,EAAC;IAC/C,6BAAO,aAAa,gCAAU,IAAI,EAClC,SAAS,GAAG,EAAC;QACT,SAAS,CAAC;IACd;AACJ;AAEA,IAAI,0CAAoB,SAAS,WAAW,EAAC;IACzC,IAAG;QACC,iCAAW,aAAa,gCAAU,IAAI;QACtC,OAAO,KAAK;IAChB,EAAC,OAAM,GAAE;QACL,OAAO,IAAI;IACf;AACJ;AAEA,IAAI,wCAAkB,SAAS,WAAW,EAAE,QAAQ,EAAC;IACjD,6BAAO,aAAa,gCAAU,IAAI,GAAG,gCAAU,IAAI,EAC/C,SAAS,GAAG,EAAC;QACb,SAAS,IAAI,EAAE,CAAC;IACpB;AACJ;AAEA,IAAI,4CAAsB,SAAS,WAAW,EAAC;IAC3C,IAAG;QACC,iCAAW,aAAa,gCAAU,IAAI,GAAG,gCAAU,IAAI;QACvD,OAAO,IAAI;IACf,EAAC,OAAM,GAAE;QACL,OAAO,KAAK;IAChB;AACJ;AAEA,IAAI,0CAAoB,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE;IAExE,oCAAc,aAAa,SAAS,MAAM,EAAC;QAEvC,IAAG,CAAC,QAAO;YACP,IAAI,QAAQ,+BAAK,gBAAgB,qBAC3B,iBACA,oBAAoB,qBAAqB,eACzC,SAAU,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;gBAC7B,SAAS,IAAI,EAAE,CAAC,CAAC;YACrB;YACN;QACJ,CAAC;QAED,sCAAgB,aAAa;IACjC;AAEJ;AAEA,IAAI,6CAAuB,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE;IAC7E,mGAAmG;IACnG,IAAI,CAAE,uFAAuF,IAAI,CAAC,cAAe;QAC/G,SAAS,IAAI,EAAE,KAAK;QACpB;IACF,CAAC;IACD,IAAI,QAAQ,+BAAK,WAAW,oBAC1B,SAAU,KAAK,EAAE;QACf,IAAI,UAAU,IAAI,EAChB,SAAS,IAAI,EAAE,KAAK;aAEpB,SAAS,IAAI,EAAE,IAAI;IAEvB;AAEJ;AAEA,IAAI,8CAAwB,SAAS,WAAW,EAAE,kBAAkB,EAAE;IACpE,IAAG,wCAAkB,cACjB,IAAI;QACF,IAAI,SAAS,mCAAS,gBAAgB,qBAChC,iBACA,oBAAoB,qBAAqB;QAE/C,OAAO,CAAC,CAAC;IACX,EAAE,OAAO,OAAO;QACd,OAAO,KAAK;IACd;IAEJ,OAAO,0CAAoB;AAC7B;AAEA,IAAI,iDAA2B,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE;IACjF,mGAAmG;IACnG,IAAI,CAAE,uFAAuF,IAAI,CAAC,cAChG,OAAO,KAAK;IAEd,IAAI;QACA,IAAI,SAAS,mCAAS,WAAW,oBAAoB;YAAC,OAAO,EAAE;QAAA;QAC/D,OAAO,CAAC,CAAC;IACb,EAAE,OAAO,OAAO;QACZ,OAAO,KAAK;IAChB;AACF;AAEA,IAAI,mCAAa,SAAS,CAAC,EAAE;IAC3B,IAAI,qBAAqB,IAAI,CAAC,IAAI;QAChC,IAAI,MAAI,EAAE,OAAO,CAAC,MAAK,WAAS;QAChC,IAAI,EAAE,OAAO,CAAC,aAAa,IAAI,4CAA4C;SACxE,OAAO,CAAC,UAAU,QAAS,0EAA0E;IAC1G,CAAC;IACD,OAAO;AACT;AAEA,IAAI,sCACF,mCAAa,SAAS,CAAC,EAAE;IACvB,IAAI,aAAa,OAAO,IAAI,CAAC;IAC7B,IAAI,YAAY;QACd,IAAI,UAAU,MAAM,oBAAa,KAAK;QACtC,IAAI,WAAW,MAAM,qBAAc,KAAK;QACxC,OAAO,UAAU,MAAM;IACzB,CAAC;IACD,OAAO,MAAM,IAAI;AACnB;AAGF,iBAAiB,SAAS,cAAc,WAAW,EAAE,QAAQ,EAAE;IAC7D,IAAI,qBAAqB,iCAAW;IACpC,IAAI,CAAC,YAAY,OAAO,YAAY,aAClC,OAAO,IAAI,QAAQ,SAAS,OAAO,EAAE,MAAM,EAAC;QAC1C,cAAc,aAAa,SAAS,KAAK,EAAE,MAAM,EAAE;YACjD,IAAI,QACF,QAAQ;iBAER,OAAO;QAEX;IACF;IAEF,IAAI,sCACF,2CAAqB,aAAa,oBAAoB;SAEtD,wCAAkB,aAAa,oBAAoB;AAEvD;AAEA,eAAe,IAAI,GAAG,SAAS,WAAW,EAAE;IAC1C,IAAI,qBAAqB,iCAAW;IACpC,IAAI,sCACF,OAAO,+CAAyB,aAAa;SAE7C,OAAO,4CAAsB,aAAa;AAE9C;;;;;AC5JA;;;;;;;;AACA,IAAI,QAAQ,GAAG,CAAC,eAAe,KAAK,aAAa,eAAQ;IACvD,OAAO,OAAO,GAAG,cAAO,QAAQ;IAChC,OAAO,MAAM,CAAC,OAAO,OAAO,EAAE;IAC9B,OAAO,OAAO,CAAC,MAAM,GAAG;AAC1B,OAAO;IACL,UAAU,OAAO,OAAO,GAAG;IAC3B,QAAQ,MAAM,GAAG,iBAAU;IAC3B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG;IACjB,QAAQ,SAAS,GAAG;IACpB,QAAQ,WAAW,GAAG;IACtB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,QAAQ,GAAG;AACrB,CAAC;;;;ACfD,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC;AAEA,iBAAiB;AACjB,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,+BAAS,aAAa,GAAG;;mCAGrB;AAEJ,IAAI,wCAAkB,SAAS,gBAAgB,OAAO,EAAE,IAAI,EAAE;IAC5D,OAAO,QAAQ,SAAS,CAAC,MAAM,MAAM;AACvC;;;;uCAUI;AAEJ,IAAI,sCAAgB,eAAO,UAAU,IAAI,WAAY,CAAC;AAEtD,SAAS,0CAAoB,KAAK,EAAE;IAClC,OAAO,iCAAO,IAAI,CAAC;AACrB;AAEA,SAAS,oCAAc,GAAG,EAAE;IAC1B,OAAO,iCAAO,QAAQ,CAAC,QAAQ,eAAe;AAChD;;AAMA,IAAI;AAEJ,IAAI,eAAa,YAAU,QAAQ,EACjC,8BAAQ,YAAU,QAAQ,CAAC;KAE3B,8BAAQ,SAAS,QAAQ,CAAC;;;;;;;AAS5B,IACI,yCAAmB;;;+CAEnB;AAAJ,IACI,6CAAuB,yCAAe,oBAAoB,EAC1D,kDAA4B,yCAAe,yBAAyB,EACpE,mDAA6B,yCAAe,0BAA0B,EACtE,2DAAqC,yCAAe,kCAAkC,EAAE,kDAAkD;AAG9I,IAAI;AACJ,IAAI;AACJ,IAAI;;AAEJ,yBAAoB,gCAAU;AAE9B,IAAI,uCAAiB;AACrB,IAAI,qCAAe;IAAC;IAAS;IAAS;IAAW;IAAS;CAAS;AAEnE,SAAS,sCAAgB,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;IAC3C,iEAAiE;IACjE,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,eAAe,KAAK,YAAY,OAAO,QAAQ,eAAe,CAAC,OAAO,KAAK,4EAA4E;IAC1K,2EAA2E;IAC3E,yEAAyE;IACzE,4EAA4E;IAE5E,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,OAAO;SAAS,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,CAAC,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG;QAAC;QAAI,QAAQ,OAAO,CAAC,MAAM;KAAC;AACtN;;;AAEA,SAAS,oCAAc,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChD,+BAAS,gCAAU;IACnB,UAAU,WAAW,CAAC,GAAG,2DAA2D;IACpF,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAE3E,IAAI,OAAO,aAAa,WAAW,WAAW,kBAAkB,8BAAQ,2DAA2D;IACnI,wDAAwD;IAExD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;IACtC,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,kBAAkB,EAAE,iEAAiE;IAClJ,uEAAuE;IAEvE,IAAI,CAAC,aAAa,GAAG,uCAAiB,IAAI,EAAE,SAAS,yBAAyB,WAAW,6EAA6E;IACtK,iEAAiE;IACjE,gBAAgB;IAEhB,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,sEAAsE;IAC5F,0EAA0E;IAC1E,wEAAwE;IACxE,yCAAyC;IAEzC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,qDAAqD;IACvE,mDAAmD;IAEnD,IAAI,CAAC,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,wDAAwD;IAE5E,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,KAAK,KAAK,EAAE,qEAAqE;IAEnH,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,WAAW,EAAE,wBAAwB;IAElE,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,sEAAsE;IAC9F,6DAA6D;IAC7D,uDAAuD;IAEvD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI,QAAQ,oEAAoE;IAE9H,IAAI,CAAC,UAAU,GAAG,GAAG,8CAA8C;IAEnE,IAAI,CAAC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC,QAAQ,GAAG,IAAI;IAEpB,IAAI,QAAQ,QAAQ,EAAE;QACpB,IAAI,CAAC,qCAAe,sCAAgB;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,oCAAc,QAAQ,QAAQ;QACjD,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;IAClC,CAAC;AACH;;AAEA,SAAS,+BAAS,OAAO,EAAE;IACzB,+BAAS,gCAAU;IACnB,IAAI,CAAE,CAAA,IAAI,YAAY,8BAAO,GAAI,OAAO,IAAI,+BAAS,UAAU,yEAAyE;IACxI,sDAAsD;IAEtD,IAAI,WAAW,IAAI,YAAY;IAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,oCAAc,SAAS,IAAI,EAAE,WAAW,SAAS;IAE3E,IAAI,CAAC,QAAQ,GAAG,IAAI;IAEpB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;QACjE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;IAC5E,CAAC;IAED,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,aAAa;IACrD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,IAAI,IAAI,CAAC,cAAc,KAAK,WAC1B,OAAO,KAAK;QAGd,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;IACtC;IACA,KAAK,SAAS,IAAI,KAAK,EAAE;QACvB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EACtB;QACD,CAAC,iDAAiD;QACnD,qBAAqB;QAGrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AACA,+BAAS,SAAS,CAAC,OAAO,GAAG;AAC7B,+BAAS,SAAS,CAAC,UAAU,GAAG;AAEhC,+BAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE,EAAE;IAC/C,GAAG;AACL,GAAG,mDAAmD;AACtD,+DAA+D;AAC/D,6DAA6D;AAC7D,qBAAqB;AAGrB,+BAAS,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE;IACnD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI;IAEJ,IAAI,CAAC,MAAM,UAAU,EACnB;QAAA,IAAI,OAAO,UAAU,UAAU;YAC7B,WAAW,YAAY,MAAM,eAAe;YAE5C,IAAI,aAAa,MAAM,QAAQ,EAAE;gBAC/B,QAAQ,iCAAO,IAAI,CAAC,OAAO;gBAC3B,WAAW;YACb,CAAC;YAED,iBAAiB,IAAI;QACvB,CAAC;IAAD,OAEA,iBAAiB,IAAI;IAGvB,OAAO,uCAAiB,IAAI,EAAE,OAAO,UAAU,KAAK,EAAE;AACxD,GAAG,8DAA8D;AAGjE,+BAAS,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE;IAC5C,OAAO,uCAAiB,IAAI,EAAE,OAAO,IAAI,EAAE,IAAI,EAAE,KAAK;AACxD;AAEA,SAAS,uCAAiB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE;IAC7E,4BAAM,oBAAoB;IAC1B,IAAI,QAAQ,OAAO,cAAc;IAEjC,IAAI,UAAU,IAAI,EAAE;QAClB,MAAM,OAAO,GAAG,KAAK;QACrB,iCAAW,QAAQ;IACrB,OAAO;QACL,IAAI;QACJ,IAAI,CAAC,gBAAgB,KAAK,mCAAa,OAAO;QAE9C,IAAI,IACF,qCAAe,QAAQ;aAClB,IAAI,MAAM,UAAU,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YACxD,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,UAAU,IAAI,OAAO,cAAc,CAAC,WAAW,iCAAO,SAAS,EACrG,QAAQ,0CAAoB;YAG9B,IAAI;gBACF,IAAI,MAAM,UAAU,EAAE,qCAAe,QAAQ,IAAI;qBAA2C,+BAAS,QAAQ,OAAO,OAAO,IAAI;mBAC1H,IAAI,MAAM,KAAK,EACpB,qCAAe,QAAQ,IAAI;iBACtB,IAAI,MAAM,SAAS,EACxB,OAAO,KAAK;iBACP;gBACL,MAAM,OAAO,GAAG,KAAK;gBAErB,IAAI,MAAM,OAAO,IAAI,CAAC,UAAU;oBAC9B,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;oBAC5B,IAAI,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG,+BAAS,QAAQ,OAAO,OAAO,KAAK;yBAAO,oCAAc,QAAQ;gBAC/G,OACE,+BAAS,QAAQ,OAAO,OAAO,KAAK;YAExC,CAAC;QACH,OAAO,IAAI,CAAC,YAAY;YACtB,MAAM,OAAO,GAAG,KAAK;YACrB,oCAAc,QAAQ;QACxB,CAAC;IACH,CAAC,CAAC,2DAA2D;IAC7D,8DAA8D;IAC9D,8DAA8D;IAG9D,OAAO,CAAC,MAAM,KAAK,IAAK,CAAA,MAAM,MAAM,GAAG,MAAM,aAAa,IAAI,MAAM,MAAM,KAAK,CAAA;AACjF;AAEA,SAAS,+BAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE;IAClD,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,EAAE;QACtD,MAAM,UAAU,GAAG;QACnB,OAAO,IAAI,CAAC,QAAQ;IACtB,OAAO;QACL,0BAA0B;QAC1B,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACnD,IAAI,YAAY,MAAM,MAAM,CAAC,OAAO,CAAC;aAAY,MAAM,MAAM,CAAC,IAAI,CAAC;QACnE,IAAI,MAAM,YAAY,EAAE,mCAAa;IACvC,CAAC;IAED,oCAAc,QAAQ;AACxB;AAEA,SAAS,mCAAa,KAAK,EAAE,KAAK,EAAE;IAClC,IAAI;IAEJ,IAAI,CAAC,oCAAc,UAAU,OAAO,UAAU,YAAY,UAAU,aAAa,CAAC,MAAM,UAAU,EAChG,KAAK,IAAI,2CAAqB,SAAS;QAAC;QAAU;QAAU;KAAa,EAAE;IAG7E,OAAO;AACT;AAEA,+BAAS,SAAS,CAAC,QAAQ,GAAG,WAAY;IACxC,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,KAAK;AAC9C,GAAG,2BAA2B;;AAG9B,+BAAS,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG,EAAE;IAC9C,IAAI,CAAC,qCAAe,sCAAgB;IACpC,IAAI,UAAU,IAAI,oCAAc;IAChC,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,SAAS,qDAAqD;IAE5F,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,iEAAiE;IAEtI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI;IACvC,IAAI,UAAU;IAEd,MAAO,MAAM,IAAI,CAAE;QACjB,WAAW,QAAQ,KAAK,CAAC,EAAE,IAAI;QAC/B,IAAI,EAAE,IAAI;IACZ;IAEA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK;IAEhC,IAAI,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;IACpD,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,QAAQ,MAAM;IAC3C,OAAO,IAAI;AACb,GAAG,4BAA4B;AAG/B,IAAI,gCAAU;AAEd,SAAS,8CAAwB,CAAC,EAAE;IAClC,IAAI,KAAK,+BACP,6CAA6C;IAC7C,IAAI;SACC;QACL,2EAA2E;QAC3E,eAAe;QACf;QACA,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX;IACF,CAAC;IAED,OAAO;AACT,EAAE,6EAA6E;AAC/E,gCAAgC;AAGhC,SAAS,oCAAc,CAAC,EAAE,KAAK,EAAE;IAC/B,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,OAAO;IACxD,IAAI,MAAM,UAAU,EAAE,OAAO;IAE7B,IAAI,MAAM,GAAG;QACX,iCAAiC;QACjC,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;aAAM,OAAO,MAAM,MAAM;IAClG,CAAC,CAAC,qEAAqE;IAGvE,IAAI,IAAI,MAAM,aAAa,EAAE,MAAM,aAAa,GAAG,8CAAwB;IAC3E,IAAI,KAAK,MAAM,MAAM,EAAE,OAAO,GAAG,oBAAoB;IAErD,IAAI,CAAC,MAAM,KAAK,EAAE;QAChB,MAAM,YAAY,GAAG,IAAI;QACzB,OAAO;IACT,CAAC;IAED,OAAO,MAAM,MAAM;AACrB,EAAE,oEAAoE;AAGtE,+BAAS,SAAS,CAAC,IAAI,GAAG,SAAU,CAAC,EAAE;IACrC,4BAAM,QAAQ;IACd,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,QAAQ;IACZ,IAAI,MAAM,GAAG,MAAM,eAAe,GAAG,KAAK,EAAE,6DAA6D;IACzG,gEAAgE;IAChE,oCAAoC;IAEpC,IAAI,MAAM,KAAK,MAAM,YAAY,IAAK,CAAA,AAAC,CAAA,MAAM,aAAa,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,GAAG,CAAC,AAAD,KAAM,MAAM,KAAK,AAAD,GAAI;QAC1I,4BAAM,sBAAsB,MAAM,MAAM,EAAE,MAAM,KAAK;QACrD,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,kCAAY,IAAI;aAAO,mCAAa,IAAI;QAC/E,OAAO,IAAI;IACb,CAAC;IAED,IAAI,oCAAc,GAAG,QAAQ,0DAA0D;IAEvF,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,kCAAY,IAAI;QACxC,OAAO,IAAI;IACb,CAAC,CAAC,oDAAoD;IACtD,4DAA4D;IAC5D,6DAA6D;IAC7D,6DAA6D;IAC7D,2DAA2D;IAC3D,iCAAiC;IACjC,EAAE;IACF,qBAAqB;IACrB,6DAA6D;IAC7D,0BAA0B;IAC1B,EAAE;IACF,oEAAoE;IACpE,kEAAkE;IAClE,kEAAkE;IAClE,mEAAmE;IACnE,sCAAsC;IACtC,qEAAqE;IACrE,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,sEAAsE;IACtE,gEAAgE;IAGhE,IAAI,SAAS,MAAM,YAAY;IAC/B,4BAAM,iBAAiB,SAAS,wEAAwE;IAExG,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,IAAI,MAAM,aAAa,EAAE;QAChE,SAAS,IAAI;QACb,4BAAM,8BAA8B;IACtC,CAAC,CAAC,uEAAuE;IACzE,kCAAkC;IAGlC,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,EAAE;QAChC,SAAS,KAAK;QACd,4BAAM,oBAAoB;IAC5B,OAAO,IAAI,QAAQ;QACjB,4BAAM;QACN,MAAM,OAAO,GAAG,IAAI;QACpB,MAAM,IAAI,GAAG,IAAI,EAAE,oEAAoE;QAEvF,IAAI,MAAM,MAAM,KAAK,GAAG,MAAM,YAAY,GAAG,IAAI,EAAE,4BAA4B;QAE/E,IAAI,CAAC,KAAK,CAAC,MAAM,aAAa;QAE9B,MAAM,IAAI,GAAG,KAAK,EAAE,oEAAoE;QACxF,sEAAsE;QAEtE,IAAI,CAAC,MAAM,OAAO,EAAE,IAAI,oCAAc,OAAO;IAC/C,CAAC;IAED,IAAI;IACJ,IAAI,IAAI,GAAG,MAAM,+BAAS,GAAG;SAAY,MAAM,IAAI;IAEnD,IAAI,QAAQ,IAAI,EAAE;QAChB,MAAM,YAAY,GAAG,MAAM,MAAM,IAAI,MAAM,aAAa;QACxD,IAAI;IACN,OAAO;QACL,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,GAAG;IACrB,CAAC;IAED,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,yDAAyD;QACzD,oDAAoD;QACpD,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAY,GAAG,IAAI,EAAE,sEAAsE;QAEnH,IAAI,UAAU,KAAK,MAAM,KAAK,EAAE,kCAAY,IAAI;IAClD,CAAC;IAED,IAAI,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;IACpC,OAAO;AACT;AAEA,SAAS,iCAAW,MAAM,EAAE,KAAK,EAAE;IACjC,4BAAM;IACN,IAAI,MAAM,KAAK,EAAE;IAEjB,IAAI,MAAM,OAAO,EAAE;QACjB,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG;QAE7B,IAAI,SAAS,MAAM,MAAM,EAAE;YACzB,MAAM,MAAM,CAAC,IAAI,CAAC;YAClB,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACrD,CAAC;IACH,CAAC;IAED,MAAM,KAAK,GAAG,IAAI;IAElB,IAAI,MAAM,IAAI,EACZ,yDAAyD;IACzD,gDAAgD;IAChD,kDAAkD;IAClD,mCAAa;SACR;QACL,sDAAsD;QACtD,MAAM,YAAY,GAAG,KAAK;QAE1B,IAAI,CAAC,MAAM,eAAe,EAAE;YAC1B,MAAM,eAAe,GAAG,IAAI;YAC5B,oCAAc;QAChB,CAAC;IACH,CAAC;AACH,EAAE,wEAAwE;AAC1E,qEAAqE;AACrE,uDAAuD;AAGvD,SAAS,mCAAa,MAAM,EAAE;IAC5B,IAAI,QAAQ,OAAO,cAAc;IACjC,4BAAM,gBAAgB,MAAM,YAAY,EAAE,MAAM,eAAe;IAC/D,MAAM,YAAY,GAAG,KAAK;IAE1B,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,4BAAM,gBAAgB,MAAM,OAAO;QACnC,MAAM,eAAe,GAAG,IAAI;QAC5B,QAAQ,QAAQ,CAAC,qCAAe;IAClC,CAAC;AACH;AAEA,SAAS,oCAAc,MAAM,EAAE;IAC7B,IAAI,QAAQ,OAAO,cAAc;IACjC,4BAAM,iBAAiB,MAAM,SAAS,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK;IAEjE,IAAI,CAAC,MAAM,SAAS,IAAK,CAAA,MAAM,MAAM,IAAI,MAAM,KAAK,AAAD,GAAI;QACrD,OAAO,IAAI,CAAC;QACZ,MAAM,eAAe,GAAG,KAAK;IAC/B,CAAC,CAAC,6CAA6C;IAC/C,wDAAwD;IACxD,iBAAiB;IACjB,sBAAsB;IACtB,uDAAuD;IACvD,6BAA6B;IAG7B,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,aAAa;IAC1F,2BAAK;AACP,EAAE,oEAAoE;AACtE,mEAAmE;AACnE,iEAAiE;AACjE,oBAAoB;AACpB,iEAAiE;AACjE,wDAAwD;AAGxD,SAAS,oCAAc,MAAM,EAAE,KAAK,EAAE;IACpC,IAAI,CAAC,MAAM,WAAW,EAAE;QACtB,MAAM,WAAW,GAAG,IAAI;QACxB,QAAQ,QAAQ,CAAC,sCAAgB,QAAQ;IAC3C,CAAC;AACH;AAEA,SAAS,qCAAe,MAAM,EAAE,KAAK,EAAE;IACrC,0CAA0C;IAC1C,EAAE;IACF,qDAAqD;IACrD,4EAA4E;IAC5E,wEAAwE;IACxE,2EAA2E;IAC3E,0EAA0E;IAC1E,mDAAmD;IACnD,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,0EAA0E;IAC1E,+CAA+C;IAC/C,wCAAwC;IACxC,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,sEAAsE;IACtE,4EAA4E;IAC5E,sCAAsC;IACtC,MAAO,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,IAAK,CAAA,MAAM,MAAM,GAAG,MAAM,aAAa,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,CAAA,EAAI;QACpH,IAAI,MAAM,MAAM,MAAM;QACtB,4BAAM;QACN,OAAO,IAAI,CAAC;QACZ,IAAI,QAAQ,MAAM,MAAM,EACtB,KAAM;IACV;IAEA,MAAM,WAAW,GAAG,KAAK;AAC3B,EAAE,yEAAyE;AAC3E,kDAAkD;AAClD,qEAAqE;AACrE,8CAA8C;AAG9C,+BAAS,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC,EAAE;IACtC,qCAAe,IAAI,EAAE,IAAI,iDAA2B;AACtD;AAEA,+BAAS,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,QAAQ,EAAE;IAClD,IAAI,MAAM,IAAI;IACd,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,OAAQ,MAAM,UAAU;QACtB,KAAK;YACH,MAAM,KAAK,GAAG;YACd,KAAM;QAER,KAAK;YACH,MAAM,KAAK,GAAG;gBAAC,MAAM,KAAK;gBAAE;aAAK;YACjC,KAAM;QAER;YACE,MAAM,KAAK,CAAC,IAAI,CAAC;YACjB,KAAM;IACV;IAEA,MAAM,UAAU,IAAI;IACpB,4BAAM,yBAAyB,MAAM,UAAU,EAAE;IACjD,IAAI,QAAQ,AAAC,CAAA,CAAC,YAAY,SAAS,GAAG,KAAK,KAAK,AAAD,KAAM,SAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,MAAM;IACvG,IAAI,QAAQ,QAAQ,QAAQ,MAAM;IAClC,IAAI,MAAM,UAAU,EAAE,QAAQ,QAAQ,CAAC;SAAY,IAAI,IAAI,CAAC,OAAO;IACnE,KAAK,EAAE,CAAC,UAAU;IAElB,SAAS,SAAS,QAAQ,EAAE,UAAU,EAAE;QACtC,4BAAM;QAEN,IAAI,aAAa,KACf;YAAA,IAAI,cAAc,WAAW,UAAU,KAAK,KAAK,EAAE;gBACjD,WAAW,UAAU,GAAG,IAAI;gBAC5B;YACF,CAAC;QAAD,CACD;IACH;IAEA,SAAS,QAAQ;QACf,4BAAM;QACN,KAAK,GAAG;IACV,EAAE,0DAA0D;IAC5D,4DAA4D;IAC5D,2DAA2D;IAC3D,YAAY;IAGZ,IAAI,UAAU,kCAAY;IAC1B,KAAK,EAAE,CAAC,SAAS;IACjB,IAAI,YAAY,KAAK;IAErB,SAAS,UAAU;QACjB,4BAAM,YAAY,iDAAiD;QAEnE,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,QAAQ;QAC3B,YAAY,IAAI,EAAE,uDAAuD;QACzE,yDAAyD;QACzD,iBAAiB;QACjB,6DAA6D;QAC7D,6DAA6D;QAE7D,IAAI,MAAM,UAAU,IAAK,CAAA,CAAC,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,SAAS,AAAD,GAAI;IACnF;IAEA,IAAI,EAAE,CAAC,QAAQ;IAEf,SAAS,OAAO,KAAK,EAAE;QACrB,4BAAM;QACN,IAAI,MAAM,KAAK,KAAK,CAAC;QACrB,4BAAM,cAAc;QAEpB,IAAI,QAAQ,KAAK,EAAE;YACjB,4DAA4D;YAC5D,2DAA2D;YAC3D,uBAAuB;YACvB,yDAAyD;YACzD,IAAI,AAAC,CAAA,MAAM,UAAU,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ,MAAM,UAAU,GAAG,KAAK,8BAAQ,MAAM,KAAK,EAAE,UAAU,EAAC,KAAM,CAAC,WAAW;gBAC/H,4BAAM,+BAA+B,MAAM,UAAU;gBACrD,MAAM,UAAU;YAClB,CAAC;YAED,IAAI,KAAK;QACX,CAAC;IACH,EAAE,sDAAsD;IACxD,0DAA0D;IAG1D,SAAS,QAAQ,EAAE,EAAE;QACnB,4BAAM,WAAW;QACjB;QACA,KAAK,cAAc,CAAC,SAAS;QAC7B,IAAI,sCAAgB,MAAM,aAAa,GAAG,qCAAe,MAAM;IACjE,EAAE,gEAAgE;IAGlE,sCAAgB,MAAM,SAAS,UAAU,8DAA8D;IAEvG,SAAS,UAAU;QACjB,KAAK,cAAc,CAAC,UAAU;QAC9B;IACF;IAEA,KAAK,IAAI,CAAC,SAAS;IAEnB,SAAS,WAAW;QAClB,4BAAM;QACN,KAAK,cAAc,CAAC,SAAS;QAC7B;IACF;IAEA,KAAK,IAAI,CAAC,UAAU;IAEpB,SAAS,SAAS;QAChB,4BAAM;QACN,IAAI,MAAM,CAAC;IACb,EAAE,yCAAyC;IAG3C,KAAK,IAAI,CAAC,QAAQ,MAAM,oDAAoD;IAE5E,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,4BAAM;QACN,IAAI,MAAM;IACZ,CAAC;IAED,OAAO;AACT;AAEA,SAAS,kCAAY,GAAG,EAAE;IACxB,OAAO,SAAS,4BAA4B;QAC1C,IAAI,QAAQ,IAAI,cAAc;QAC9B,4BAAM,eAAe,MAAM,UAAU;QACrC,IAAI,MAAM,UAAU,EAAE,MAAM,UAAU;QAEtC,IAAI,MAAM,UAAU,KAAK,KAAK,sCAAgB,KAAK,SAAS;YAC1D,MAAM,OAAO,GAAG,IAAI;YACpB,2BAAK;QACP,CAAC;IACH;AACF;AAEA,+BAAS,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE;IAC1C,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,aAAa;QACf,YAAY,KAAK;IACnB,GAAG,iDAAiD;IAEpD,IAAI,MAAM,UAAU,KAAK,GAAG,OAAO,IAAI,EAAE,2CAA2C;IAEpF,IAAI,MAAM,UAAU,KAAK,GAAG;QAC1B,6CAA6C;QAC7C,IAAI,QAAQ,SAAS,MAAM,KAAK,EAAE,OAAO,IAAI;QAC7C,IAAI,CAAC,MAAM,OAAO,MAAM,KAAK,EAAE,eAAe;QAE9C,MAAM,KAAK,GAAG,IAAI;QAClB,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG,KAAK;QACrB,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;QACpC,OAAO,IAAI;IACb,CAAC,CAAC,yCAAyC;IAG3C,IAAI,CAAC,MAAM;QACT,cAAc;QACd,IAAI,QAAQ,MAAM,KAAK;QACvB,IAAI,MAAM,MAAM,UAAU;QAC1B,MAAM,KAAK,GAAG,IAAI;QAClB,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG,KAAK;QAErB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IACvB,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;YAC5B,YAAY,KAAK;QACnB;QAGF,OAAO,IAAI;IACb,CAAC,CAAC,6BAA6B;IAG/B,IAAI,QAAQ,8BAAQ,MAAM,KAAK,EAAE;IACjC,IAAI,UAAU,IAAI,OAAO,IAAI;IAC7B,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO;IAC1B,MAAM,UAAU,IAAI;IACpB,IAAI,MAAM,UAAU,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE;IACxD,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;IAC1B,OAAO,IAAI;AACb,GAAG,2CAA2C;AAC9C,qDAAqD;AAGrD,+BAAS,SAAS,CAAC,EAAE,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE;IACxC,IAAI,MAAM,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;IAC7C,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,OAAO,QAAQ;QACjB,2DAA2D;QAC3D,gEAAgE;QAChE,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,mEAAmE;QAEjI,IAAI,MAAM,OAAO,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM;IAC1C,OAAO,IAAI,OAAO,YAChB;QAAA,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,iBAAiB,EAAE;YACjD,MAAM,iBAAiB,GAAG,MAAM,YAAY,GAAG,IAAI;YACnD,MAAM,OAAO,GAAG,KAAK;YACrB,MAAM,eAAe,GAAG,KAAK;YAC7B,4BAAM,eAAe,MAAM,MAAM,EAAE,MAAM,OAAO;YAEhD,IAAI,MAAM,MAAM,EACd,mCAAa,IAAI;iBACZ,IAAI,CAAC,MAAM,OAAO,EACvB,QAAQ,QAAQ,CAAC,wCAAkB,IAAI;QAE3C,CAAC;IAAD,CACD;IAED,OAAO;AACT;AAEA,+BAAS,SAAS,CAAC,WAAW,GAAG,+BAAS,SAAS,CAAC,EAAE;AAEtD,+BAAS,SAAS,CAAC,cAAc,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE;IACpD,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;IAEzD,IAAI,OAAO,YACT,0DAA0D;IAC1D,6DAA6D;IAC7D,+DAA+D;IAC/D,+DAA+D;IAC/D,2CAA2C;IAC3C,UAAU;IACV,QAAQ,QAAQ,CAAC,+CAAyB,IAAI;IAGhD,OAAO;AACT;AAEA,+BAAS,SAAS,CAAC,kBAAkB,GAAG,SAAU,EAAE,EAAE;IACpD,IAAI,MAAM,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE;IAE1D,IAAI,OAAO,cAAc,OAAO,WAC9B,0DAA0D;IAC1D,6DAA6D;IAC7D,+DAA+D;IAC/D,+DAA+D;IAC/D,2CAA2C;IAC3C,UAAU;IACV,QAAQ,QAAQ,CAAC,+CAAyB,IAAI;IAGhD,OAAO;AACT;AAEA,SAAS,8CAAwB,IAAI,EAAE;IACrC,IAAI,QAAQ,KAAK,cAAc;IAC/B,MAAM,iBAAiB,GAAG,KAAK,aAAa,CAAC,cAAc;IAE3D,IAAI,MAAM,eAAe,IAAI,CAAC,MAAM,MAAM,EACxC,iDAAiD;IACjD,qCAAqC;IACrC,MAAM,OAAO,GAAG,IAAI,EAAE,yCAAyC;SAC1D,IAAI,KAAK,aAAa,CAAC,UAAU,GACtC,KAAK,MAAM;AAEf;AAEA,SAAS,uCAAiB,IAAI,EAAE;IAC9B,4BAAM;IACN,KAAK,IAAI,CAAC;AACZ,EAAE,sEAAsE;AACxE,oDAAoD;AAGpD,+BAAS,SAAS,CAAC,MAAM,GAAG,WAAY;IACtC,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,4BAAM,WAAW,4CAA4C;QAC7D,0CAA0C;QAC1C,WAAW;QAEX,MAAM,OAAO,GAAG,CAAC,MAAM,iBAAiB;QACxC,6BAAO,IAAI,EAAE;IACf,CAAC;IAED,MAAM,MAAM,GAAG,KAAK;IACpB,OAAO,IAAI;AACb;AAEA,SAAS,6BAAO,MAAM,EAAE,KAAK,EAAE;IAC7B,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,eAAe,GAAG,IAAI;QAC5B,QAAQ,QAAQ,CAAC,+BAAS,QAAQ;IACpC,CAAC;AACH;AAEA,SAAS,8BAAQ,MAAM,EAAE,KAAK,EAAE;IAC9B,4BAAM,UAAU,MAAM,OAAO;IAE7B,IAAI,CAAC,MAAM,OAAO,EAChB,OAAO,IAAI,CAAC;IAGd,MAAM,eAAe,GAAG,KAAK;IAC7B,OAAO,IAAI,CAAC;IACZ,2BAAK;IACL,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE,OAAO,IAAI,CAAC;AACnD;AAEA,+BAAS,SAAS,CAAC,KAAK,GAAG,WAAY;IACrC,4BAAM,yBAAyB,IAAI,CAAC,cAAc,CAAC,OAAO;IAE1D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,KAAK,EAAE;QACzC,4BAAM;QACN,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,KAAK;QACnC,IAAI,CAAC,IAAI,CAAC;IACZ,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI;IACjC,OAAO,IAAI;AACb;AAEA,SAAS,2BAAK,MAAM,EAAE;IACpB,IAAI,QAAQ,OAAO,cAAc;IACjC,4BAAM,QAAQ,MAAM,OAAO;IAE3B,MAAO,MAAM,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI;AAGhD,EAAE,qDAAqD;AACvD,uDAAuD;AACvD,6CAA6C;AAG7C,+BAAS,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE;IAC1C,IAAI,QAAQ,IAAI;IAEhB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,SAAS,KAAK;IAClB,OAAO,EAAE,CAAC,OAAO,WAAY;QAC3B,4BAAM;QAEN,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE;YACjC,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG;YAC7B,IAAI,SAAS,MAAM,MAAM,EAAE,MAAM,IAAI,CAAC;QACxC,CAAC;QAED,MAAM,IAAI,CAAC,IAAI;IACjB;IACA,OAAO,EAAE,CAAC,QAAQ,SAAU,KAAK,EAAE;QACjC,4BAAM;QACN,IAAI,MAAM,OAAO,EAAE,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC,QAAQ,6CAA6C;QAEpG,IAAI,MAAM,UAAU,IAAK,CAAA,UAAU,IAAI,IAAI,UAAU,SAAQ,GAAI;aAAY,IAAI,CAAC,MAAM,UAAU,IAAK,CAAA,CAAC,SAAS,CAAC,MAAM,MAAM,AAAD,GAAI;QAEjI,IAAI,MAAM,MAAM,IAAI,CAAC;QAErB,IAAI,CAAC,KAAK;YACR,SAAS,IAAI;YACb,OAAO,KAAK;QACd,CAAC;IACH,IAAI,+BAA+B;IACnC,gDAAgD;IAEhD,IAAK,IAAI,KAAK,OACZ,IAAI,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO,MAAM,CAAC,EAAE,KAAK,YAChD,IAAI,CAAC,EAAE,GAAG,SAAS,WAAW,MAAM,EAAE;QACpC,OAAO,SAAS,2BAA2B;YACzC,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ;QACtC;IACF,EAAE;KAEJ,kCAAkC;IAGpC,IAAK,IAAI,IAAI,GAAG,IAAI,mCAAa,MAAM,EAAE,IACvC,OAAO,EAAE,CAAC,kCAAY,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,kCAAY,CAAC,EAAE;KAC/D,6DAA6D;IAC/D,qBAAqB;IAGrB,IAAI,CAAC,KAAK,GAAG,SAAU,CAAC,EAAE;QACxB,4BAAM,iBAAiB;QAEvB,IAAI,QAAQ;YACV,SAAS,KAAK;YACd,OAAO,MAAM;QACf,CAAC;IACH;IAEA,OAAO,IAAI;AACb;;AAEA,IAAI,OAAO,WAAW,YACpB,+BAAS,SAAS,CAAC,OAAO,aAAa,CAAC,GAAG,WAAY;IACrD,IAAI,4DAAsC,WACxC,0DAAoC;IAGtC,OAAO,wDAAkC,IAAI;AAC/C;AAGF,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AACA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;IAC1D;AACF;AACA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,mBAAmB;IAC3D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO;IACpC;IACA,KAAK,SAAS,IAAI,KAAK,EAAE;QACvB,IAAI,IAAI,CAAC,cAAc,EACrB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;IAElC;AACF,IAAI,qCAAqC;AAEzC,+BAAS,SAAS,GAAG;AACrB,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;IACnC;AACF,IAAI,8CAA8C;AAClD,iEAAiE;AACjE,6EAA6E;AAC7E,gCAAgC;AAEhC,SAAS,+BAAS,CAAC,EAAE,KAAK,EAAE;IAC1B,mBAAmB;IACnB,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,IAAI;IACnC,IAAI;IACJ,IAAI,MAAM,UAAU,EAAE,MAAM,MAAM,MAAM,CAAC,KAAK;SAAQ,IAAI,CAAC,KAAK,KAAK,MAAM,MAAM,EAAE;QACjF,iCAAiC;QACjC,IAAI,MAAM,OAAO,EAAE,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC;aAAS,IAAI,MAAM,MAAM,CAAC,MAAM,KAAK,GAAG,MAAM,MAAM,MAAM,CAAC,KAAK;aAAQ,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;QACzJ,MAAM,MAAM,CAAC,KAAK;IACpB,OACE,oBAAoB;IACpB,MAAM,MAAM,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,OAAO;IAE7C,OAAO;AACT;AAEA,SAAS,kCAAY,MAAM,EAAE;IAC3B,IAAI,QAAQ,OAAO,cAAc;IACjC,4BAAM,eAAe,MAAM,UAAU;IAErC,IAAI,CAAC,MAAM,UAAU,EAAE;QACrB,MAAM,KAAK,GAAG,IAAI;QAClB,QAAQ,QAAQ,CAAC,qCAAe,OAAO;IACzC,CAAC;AACH;AAEA,SAAS,oCAAc,KAAK,EAAE,MAAM,EAAE;IACpC,4BAAM,iBAAiB,MAAM,UAAU,EAAE,MAAM,MAAM,GAAG,6CAA6C;IAErG,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG;QAC3C,MAAM,UAAU,GAAG,IAAI;QACvB,OAAO,QAAQ,GAAG,KAAK;QACvB,OAAO,IAAI,CAAC;QAEZ,IAAI,MAAM,WAAW,EAAE;YACrB,oDAAoD;YACpD,wDAAwD;YACxD,IAAI,SAAS,OAAO,cAAc;YAElC,IAAI,CAAC,UAAU,OAAO,WAAW,IAAI,OAAO,QAAQ,EAClD,OAAO,OAAO;QAElB,CAAC;IACH,CAAC;AACH;;AAEA,IAAI,OAAO,WAAW,YACpB,+BAAS,IAAI,GAAG,SAAU,QAAQ,EAAE,IAAI,EAAE;IACxC,IAAI,+BAAS,WACX,6BAAO;IAGT,OAAO,2BAAK,gCAAU,UAAU;AAClC;AAGF,SAAS,8BAAQ,EAAE,EAAE,CAAC,EAAE;IACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAI,GAAG,IAAK;QACzC,IAAI,EAAE,CAAC,EAAE,KAAK,GAAG,OAAO;IAC1B;IAEA,OAAO;AACT;;;;ACnmCA;AAAA,iBAAiB;;;;;ACAjB;AAEA,SAAS,8BAAQ,MAAM,EAAE,cAAc,EAAE;IAAE,IAAI,OAAO,OAAO,IAAI,CAAC;IAAS,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAAS,IAAI,gBAAgB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG,EAAE;YAAE,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;QAAE;QAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IAAU,CAAC;IAAC,OAAO;AAAM;AAEpV,SAAS,oCAAc,MAAM,EAAE;IAAE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC;QAAE,IAAI,IAAI,GAAK,8BAAQ,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,SAAU,GAAG,EAAE;YAAE,sCAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;QAAG;aAAW,IAAI,OAAO,yBAAyB,EAAI,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;aAAmB,8BAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG,EAAE;YAAE,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;QAAO;IAAM;IAAE,OAAO;AAAQ;AAErhB,SAAS,sCAAgB,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IAAE,IAAI,OAAO,KAAO,OAAO,cAAc,CAAC,KAAK,KAAK;QAAE,OAAO;QAAO,YAAY,IAAI;QAAE,cAAc,IAAI;QAAE,UAAU,IAAI;IAAC;SAAa,GAAG,CAAC,IAAI,GAAG;IAAS,OAAO;AAAK;AAEhN,SAAS,sCAAgB,QAAQ,EAAE,WAAW,EAAE;IAAE,IAAI,CAAE,CAAA,oBAAoB,WAAU,GAAM,MAAM,IAAI,UAAU,qCAAqC;AAAG;AAExJ,SAAS,wCAAkB,MAAM,EAAE,KAAK,EAAE;IAAE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI,KAAK;QAAE,WAAW,YAAY,GAAG,IAAI;QAAE,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG,IAAI;QAAE,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAE5T,SAAS,mCAAa,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;IAAE,IAAI,YAAY,wCAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,wCAAkB,aAAa;IAAc,OAAO;AAAa;;AAEtN,IACI,+BAAS;;AAEb,IACI,gCAAU;AAEd,IAAI,+BAAS,iCAAW,8BAAQ,MAAM,IAAI;AAE1C,SAAS,iCAAW,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE;IACvC,6BAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC1C;AAEA,iBACA,WAAW,GACX,WAAY;IACV,SAAS,aAAa;QACpB,sCAAgB,IAAI,EAAE;QAEtB,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,mCAAa,YAAY;QAAC;YACxB,KAAK;YACL,OAAO,SAAS,KAAK,CAAC,EAAE;gBACtB,IAAI,QAAQ;oBACV,MAAM;oBACN,MAAM,IAAI;gBACZ;gBACA,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;qBAAW,IAAI,CAAC,IAAI,GAAG;gBAC7D,IAAI,CAAC,IAAI,GAAG;gBACZ,EAAE,IAAI,CAAC,MAAM;YACf;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ,CAAC,EAAE;gBACzB,IAAI,QAAQ;oBACV,MAAM;oBACN,MAAM,IAAI,CAAC,IAAI;gBACjB;gBACA,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG;gBACnC,IAAI,CAAC,IAAI,GAAG;gBACZ,EAAE,IAAI,CAAC,MAAM;YACf;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ;gBACtB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG;gBACvB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;gBACxB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI;qBAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;gBACnF,EAAE,IAAI,CAAC,MAAM;gBACb,OAAO;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ;gBACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI;gBAC5B,IAAI,CAAC,MAAM,GAAG;YAChB;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,KAAK,CAAC,EAAE;gBACtB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;gBAC9B,IAAI,IAAI,IAAI,CAAC,IAAI;gBACjB,IAAI,MAAM,KAAK,EAAE,IAAI;gBAErB,MAAO,IAAI,EAAE,IAAI,CACf,OAAO,IAAI,EAAE,IAAI;gBAGnB,OAAO;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,OAAO,CAAC,EAAE;gBACxB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO,6BAAO,KAAK,CAAC;gBAC3C,IAAI,MAAM,6BAAO,WAAW,CAAC,MAAM;gBACnC,IAAI,IAAI,IAAI,CAAC,IAAI;gBACjB,IAAI,IAAI;gBAER,MAAO,EAAG;oBACR,iCAAW,EAAE,IAAI,EAAE,KAAK;oBACxB,KAAK,EAAE,IAAI,CAAC,MAAM;oBAClB,IAAI,EAAE,IAAI;gBACZ;gBAEA,OAAO;YACT,EAAE,6EAA6E;QAEjF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ,CAAC,EAAE,UAAU,EAAE;gBACrC,IAAI;gBAEJ,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAC7B,+CAA+C;oBAC/C,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;oBAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;gBACxC,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EACpC,kCAAkC;gBAClC,MAAM,IAAI,CAAC,KAAK;qBAEhB,qCAAqC;gBACrC,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE;gBAG5D,OAAO;YACT;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,QAAQ;gBACtB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;YACvB,EAAE,oEAAoE;QAExE;QAAG;YACD,KAAK;YACL,OAAO,SAAS,WAAW,CAAC,EAAE;gBAC5B,IAAI,IAAI,IAAI,CAAC,IAAI;gBACjB,IAAI,IAAI;gBACR,IAAI,MAAM,EAAE,IAAI;gBAChB,KAAK,IAAI,MAAM;gBAEf,MAAO,IAAI,EAAE,IAAI,CAAE;oBACjB,IAAI,MAAM,EAAE,IAAI;oBAChB,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC;oBACxC,IAAI,OAAO,IAAI,MAAM,EAAE,OAAO;yBAAS,OAAO,IAAI,KAAK,CAAC,GAAG;oBAC3D,KAAK;oBAEL,IAAI,MAAM,GAAG;wBACX,IAAI,OAAO,IAAI,MAAM,EAAE;4BACrB,EAAE;4BACF,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI;iCAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI;wBAClE,OAAO;4BACL,IAAI,CAAC,IAAI,GAAG;4BACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;wBACrB,CAAC;wBAED,KAAM;oBACR,CAAC;oBAED,EAAE;gBACJ;gBAEA,IAAI,CAAC,MAAM,IAAI;gBACf,OAAO;YACT,EAAE,+DAA+D;QAEnE;QAAG;YACD,KAAK;YACL,OAAO,SAAS,WAAW,CAAC,EAAE;gBAC5B,IAAI,MAAM,6BAAO,WAAW,CAAC;gBAC7B,IAAI,IAAI,IAAI,CAAC,IAAI;gBACjB,IAAI,IAAI;gBACR,EAAE,IAAI,CAAC,IAAI,CAAC;gBACZ,KAAK,EAAE,IAAI,CAAC,MAAM;gBAElB,MAAO,IAAI,EAAE,IAAI,CAAE;oBACjB,IAAI,MAAM,EAAE,IAAI;oBAChB,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC;oBACxC,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,GAAG,GAAG,GAAG;oBACjC,KAAK;oBAEL,IAAI,MAAM,GAAG;wBACX,IAAI,OAAO,IAAI,MAAM,EAAE;4BACrB,EAAE;4BACF,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI;iCAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI;wBAClE,OAAO;4BACL,IAAI,CAAC,IAAI,GAAG;4BACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;wBACrB,CAAC;wBAED,KAAM;oBACR,CAAC;oBAED,EAAE;gBACJ;gBAEA,IAAI,CAAC,MAAM,IAAI;gBACf,OAAO;YACT,EAAE,0EAA0E;QAE9E;QAAG;YACD,KAAK;YACL,OAAO,SAAS,MAAM,CAAC,EAAE,OAAO,EAAE;gBAChC,OAAO,8BAAQ,IAAI,EAAE,oCAAc,CAAC,GAAG,SAAS;oBAC9C,0BAA0B;oBAC1B,OAAO;oBACP,yBAAyB;oBACzB,eAAe,KAAK;gBACtB;YACF;QACF;KAAE;IAEF,OAAO;AACT;;;;;ACjNA,cAAc,6DAA6D;AAE3E,SAAS,8BAAQ,GAAG,EAAE,EAAE,EAAE;IACxB,IAAI,QAAQ,IAAI;IAEhB,IAAI,oBAAoB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAC5E,IAAI,oBAAoB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAE5E,IAAI,qBAAqB,mBAAmB;QAC1C,IAAI,IACF,GAAG;aACE,IAAI,KAAK;YACd,IAAI,CAAC,IAAI,CAAC,cAAc,EACtB,QAAQ,QAAQ,CAAC,mCAAa,IAAI,EAAE;iBAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC5C,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI;gBACvC,QAAQ,QAAQ,CAAC,mCAAa,IAAI,EAAE;YACtC,CAAC;QACH,CAAC;QAED,OAAO,IAAI;IACb,CAAC,CAAC,kEAAkE;IACpE,2EAA2E;IAG3E,IAAI,IAAI,CAAC,cAAc,EACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI;IACrC,CAAC,yEAAyE;IAG3E,IAAI,IAAI,CAAC,cAAc,EACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI;IAGtC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,SAAU,GAAG,EAAE;QACxC,IAAI,CAAC,MAAM,KAAK;YACd,IAAI,CAAC,MAAM,cAAc,EACvB,QAAQ,QAAQ,CAAC,2CAAqB,OAAO;iBACxC,IAAI,CAAC,MAAM,cAAc,CAAC,YAAY,EAAE;gBAC7C,MAAM,cAAc,CAAC,YAAY,GAAG,IAAI;gBACxC,QAAQ,QAAQ,CAAC,2CAAqB,OAAO;YAC/C,OACE,QAAQ,QAAQ,CAAC,mCAAa;QAElC,OAAO,IAAI,IAAI;YACb,QAAQ,QAAQ,CAAC,mCAAa;YAC9B,GAAG;QACL,OACE,QAAQ,QAAQ,CAAC,mCAAa;IAElC;IAEA,OAAO,IAAI;AACb;AAEA,SAAS,0CAAoB,IAAI,EAAE,GAAG,EAAE;IACtC,kCAAY,MAAM;IAClB,kCAAY;AACd;AAEA,SAAS,kCAAY,IAAI,EAAE;IACzB,IAAI,KAAK,cAAc,IAAI,CAAC,KAAK,cAAc,CAAC,SAAS,EAAE;IAC3D,IAAI,KAAK,cAAc,IAAI,CAAC,KAAK,cAAc,CAAC,SAAS,EAAE;IAC3D,KAAK,IAAI,CAAC;AACZ;AAEA,SAAS,kCAAY;IACnB,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,KAAK;QACrC,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,KAAK;QACnC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,KAAK;QACjC,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,KAAK;IACxC,CAAC;IAED,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,KAAK;QACrC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,KAAK;QACjC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK;QAClC,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,KAAK;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,KAAK;QACvC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK;QACpC,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,KAAK;IAC1C,CAAC;AACH;AAEA,SAAS,kCAAY,IAAI,EAAE,GAAG,EAAE;IAC9B,KAAK,IAAI,CAAC,SAAS;AACrB;AAEA,SAAS,qCAAe,MAAM,EAAE,GAAG,EAAE;IACnC,kDAAkD;IAClD,sDAAsD;IACtD,kDAAkD;IAClD,gDAAgD;IAChD,4DAA4D;IAC5D,IAAI,SAAS,OAAO,cAAc;IAClC,IAAI,SAAS,OAAO,cAAc;IAClC,IAAI,UAAU,OAAO,WAAW,IAAI,UAAU,OAAO,WAAW,EAAE,OAAO,OAAO,CAAC;SAAU,OAAO,IAAI,CAAC,SAAS;AAClH;AAEA,iBAAiB;IACf,SAAS;IACT,WAAW;IACX,gBAAgB;AAClB;;;;;ACxGA;;;AAEA,IAAI,8CAAwB,aAAiC,qBAAqB;AAElF,SAAS,wCAAkB,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE;IACvD,OAAO,QAAQ,aAAa,IAAI,IAAI,GAAG,QAAQ,aAAa,GAAG,WAAW,OAAO,CAAC,UAAU,GAAG,IAAI;AACrG;AAEA,SAAS,uCAAiB,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE;IAC7D,IAAI,MAAM,wCAAkB,SAAS,UAAU;IAE/C,IAAI,OAAO,IAAI,EAAE;QACf,IAAI,CAAE,CAAA,SAAS,QAAQ,KAAK,KAAK,CAAC,SAAS,GAAE,KAAM,MAAM,GAAG;YAC1D,IAAI,OAAO,WAAW,YAAY,eAAe;YACjD,MAAM,IAAI,4CAAsB,MAAM,KAAK;QAC7C,CAAC;QAED,OAAO,KAAK,KAAK,CAAC;IACpB,CAAC,CAAC,gBAAgB;IAGlB,OAAO,MAAM,UAAU,GAAG,KAAK,KAAS;AAC1C;AAEA,iBAAiB;IACf,kBAAkB;AACpB;;;;;;ACyFA,IAAA;AAnHA;AAEA,MAAM,8BAAQ,CAAC;AAEf,SAAS,sCAAgB,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IAC5C,IAAI,CAAC,MACH,OAAO;IAGT,SAAS,WAAY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;QACrC,IAAI,OAAO,YAAY,UACrB,OAAO;aAEP,OAAO,QAAQ,MAAM,MAAM;IAE/B;IAEA,MAAM,kBAAkB;QACtB,YAAa,IAAI,EAAE,IAAI,EAAE,IAAI,CAAE;YAC7B,KAAK,CAAC,WAAW,MAAM,MAAM;QAC/B;IACF;IAEA,UAAU,SAAS,CAAC,IAAI,GAAG,KAAK,IAAI;IACpC,UAAU,SAAS,CAAC,IAAI,GAAG;IAE3B,2BAAK,CAAC,KAAK,GAAG;AAChB;AAEA,qEAAqE;AACrE,SAAS,4BAAM,QAAQ,EAAE,KAAK,EAAE;IAC9B,IAAI,MAAM,OAAO,CAAC,WAAW;QAC3B,MAAM,MAAM,SAAS,MAAM;QAC3B,WAAW,SAAS,GAAG,CAAC,CAAC,IAAM,OAAO;QACtC,IAAI,MAAM,GACR,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,GAC/D,QAAQ,CAAC,MAAM,EAAE;aACnB,IAAI,QAAQ,GACjB,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;aAEzD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;IAEvC,OACE,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,UAAU,CAAC;AAE5C;AAEA,qGAAqG;AACrG,SAAS,iCAAW,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;IACrC,OAAO,IAAI,MAAM,CAAC,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,OAAO,MAAM,MAAM;AAClE;AAEA,mGAAmG;AACnG,SAAS,+BAAS,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE;IACxC,IAAI,aAAa,aAAa,WAAW,IAAI,MAAM,EAClD,WAAW,IAAI,MAAM;IAEtB,OAAO,IAAI,SAAS,CAAC,WAAW,OAAO,MAAM,EAAE,cAAc;AAC9D;AAEA,mGAAmG;AACnG,SAAS,+BAAS,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE;IACpC,IAAI,OAAO,UAAU,UACnB,QAAQ;IAGV,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,MAAM,EACpC,OAAO,KAAK;SAEZ,OAAO,IAAI,OAAO,CAAC,QAAQ,WAAW;AAE1C;AAEA,sCAAgB,yBAAyB,SAAU,IAAI,EAAE,KAAK,EAAE;IAC9D,OAAO,gBAAgB,QAAQ,8BAA8B,OAAO;AACtE,GAAG;AACH,sCAAgB,wBAAwB,SAAU,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE;IACxE,yCAAyC;IACzC,IAAI;IACJ,IAAI,OAAO,aAAa,YAAY,iCAAW,UAAU,SAAS;QAChE,aAAa;QACb,WAAW,SAAS,OAAO,CAAC,SAAS;IACvC,OACE,aAAa;IAGf,IAAI;IACJ,IAAI,+BAAS,MAAM,cACjB,kCAAkC;IAClC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,4BAAM,UAAU,QAAQ,CAAC;SACvD;QACL,MAAM,OAAO,+BAAS,MAAM,OAAO,aAAa,UAAU;QAC1D,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,4BAAM,UAAU,QAAQ,CAAC;IACxE,CAAC;IAED,OAAO,CAAC,gBAAgB,EAAE,OAAO,OAAO,CAAC;IACzC,OAAO;AACT,GAAG;AACH,sCAAgB,6BAA6B;AAC7C,sCAAgB,8BAA8B,SAAU,IAAI,EAAE;IAC5D,OAAO,SAAS,OAAO;AACzB;AACA,sCAAgB,8BAA8B;AAC9C,sCAAgB,wBAAwB,SAAU,IAAI,EAAE;IACtD,OAAO,iBAAiB,OAAO;AACjC;AACA,sCAAgB,yBAAyB;AACzC,sCAAgB,0BAA0B;AAC1C,sCAAgB,8BAA8B;AAC9C,sCAAgB,0BAA0B,uCAAuC;AACjF,sCAAgB,wBAAwB,SAAU,GAAG,EAAE;IACrD,OAAO,uBAAuB;AAChC,GAAG;AACH,sCAAgB,sCAAsC;AAEtD,4CAAuB;;;;;;ACnHvB;;AAAA,IAAI;IACF,IAAI,6BAAO;IACX,wBAAwB,GACxB,IAAI,OAAO,2BAAK,QAAQ,KAAK,YAAY,MAAM,GAAG;IAClD,iBAAiB,2BAAK,QAAQ;AAChC,EAAE,OAAO,GAAG;IACV,wBAAwB,GACxB,iBAAiB;AACnB;;;;ACRA,IAAI,OAAO,OAAO,MAAM,KAAK,YAC3B,qDAAqD;AACrD,iBAAiB,SAAS,SAAS,IAAI,EAAE,SAAS,EAAE;IAClD,IAAI,WAAW;QACb,KAAK,MAAM,GAAG;QACd,KAAK,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE;YAClD,aAAa;gBACX,OAAO;gBACP,YAAY,KAAK;gBACjB,UAAU,IAAI;gBACd,cAAc,IAAI;YACpB;QACF;IACF,CAAC;AACH;KAEA,mCAAmC;AACnC,iBAAiB,SAAS,SAAS,IAAI,EAAE,SAAS,EAAE;IAClD,IAAI,WAAW;QACb,KAAK,MAAM,GAAG;QACd,IAAI,WAAW,WAAY,CAAC;QAC5B,SAAS,SAAS,GAAG,UAAU,SAAS;QACxC,KAAK,SAAS,GAAG,IAAI;QACrB,KAAK,SAAS,CAAC,WAAW,GAAG;IAC/B,CAAC;AACH;;;;;;ACzBF,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,uEAAuE;AACvE,oEAAoE;AACpE,mEAAmE;AACnE,YAAY;AACZ;AACA,eAAe,GAEf,IAAI,mCAAa,OAAO,IAAI,IAAI,SAAU,GAAG,EAAE;IAC7C,IAAI,OAAO,EAAE;IAEb,IAAK,IAAI,OAAO,IACd,KAAK,IAAI,CAAC;IAGZ,OAAO;AACT;AACA,gBAAgB,GAGhB,iBAAiB;;;;;;AAMjB,yBAAoB,8BAAQ;AAG1B,oCAAoC;AACpC,IAAI,6BAAO,iCAAW;AAEtB,IAAK,IAAI,0BAAI,GAAG,0BAAI,2BAAK,MAAM,EAAE,0BAAK;IACpC,IAAI,+BAAS,0BAAI,CAAC,wBAAE;IACpB,IAAI,CAAC,6BAAO,SAAS,CAAC,6BAAO,EAAE,6BAAO,SAAS,CAAC,6BAAO,GAAG,gBAAkB,CAAC,6BAAO;AACtF;AAGF,SAAS,6BAAO,OAAO,EAAE;IACvB,IAAI,CAAE,CAAA,IAAI,YAAY,4BAAK,GAAI,OAAO,IAAI,6BAAO;IACjD,OAAS,IAAI,CAAC,IAAI,EAAE;IACpB,YAAc,IAAI,EAAE;IACpB,IAAI,CAAC,aAAa,GAAG,IAAI;IAEzB,IAAI,SAAS;QACX,IAAI,QAAQ,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrD,IAAI,QAAQ,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK;QAErD,IAAI,QAAQ,aAAa,KAAK,KAAK,EAAE;YACnC,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B,IAAI,CAAC,IAAI,CAAC,OAAO;QACnB,CAAC;IACH,CAAC;AACH;AAEA,OAAO,cAAc,CAAC,6BAAO,SAAS,EAAE,yBAAyB;IAC/D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AACA,OAAO,cAAc,CAAC,6BAAO,SAAS,EAAE,kBAAkB;IACxD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7D;AACF;AACA,OAAO,cAAc,CAAC,6BAAO,SAAS,EAAE,kBAAkB;IACxD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;IACnC;AACF,IAAI,4BAA4B;AAEhC,SAAS,8BAAQ;IACf,6CAA6C;IAC7C,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,+BAA+B;IACtE,gDAAgD;IAEhD,QAAQ,QAAQ,CAAC,+BAAS,IAAI;AAChC;AAEA,SAAS,8BAAQ,IAAI,EAAE;IACrB,KAAK,GAAG;AACV;AAEA,OAAO,cAAc,CAAC,6BAAO,SAAS,EAAE,aAAa;IACnD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAC/D,OAAO,KAAK;QAGd,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IACvE;IACA,KAAK,SAAS,IAAI,KAAK,EAAE;QACvB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAC/D;QACD,CAAC,iDAAiD;QACnD,qBAAqB;QAGrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;;;;AC1IA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,uCAAuC;AACvC,wEAAwE;AACxE,0CAA0C;AAC1C;AAEA,iBAAiB;AACjB,iBAAiB,GAEjB,SAAS,+BAAS,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IACrC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,IAAI,GAAG,IAAI;AAClB,EAAE,uCAAuC;AACzC,gDAAgD;AAGhD,SAAS,oCAAc,KAAK,EAAE;IAC5B,IAAI,QAAQ,IAAI;IAEhB,IAAI,CAAC,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC,MAAM,GAAG,WAAY;QACxB,qCAAe,OAAO;IACxB;AACF;AACA,kBAAkB,GAElB,eAAe,GAGf,IAAI;AACJ,gBAAgB,GAEhB,+BAAS,aAAa,GAAG;;AACzB,eAAe,GAEf,IAAI,qCAAe;IACjB,WAAW;AACb;;;;uCASI;AAEJ,IAAI,sCAAgB,eAAO,UAAU,IAAI,WAAY,CAAC;AAEtD,SAAS,0CAAoB,KAAK,EAAE;IAClC,OAAO,iCAAO,IAAI,CAAC;AACrB;AAEA,SAAS,oCAAc,GAAG,EAAE;IAC1B,OAAO,iCAAO,QAAQ,CAAC,QAAQ,eAAe;AAChD;;;;;AAIA,IACI,yCAAmB;;;+CAEnB;AAAJ,IACI,6CAAuB,yCAAe,oBAAoB,EAC1D,mDAA6B,yCAAe,0BAA0B,EACtE,8CAAwB,yCAAe,qBAAqB,EAC5D,+CAAyB,yCAAe,sBAAsB,EAC9D,6CAAuB,yCAAe,oBAAoB,EAC1D,+CAAyB,yCAAe,sBAAsB,EAC9D,mDAA6B,yCAAe,0BAA0B,EACtE,6CAAuB,yCAAe,oBAAoB;AAE9D,IAAI,uCAAiB;;AAErB,yBAAoB,gCAAU;AAE9B,SAAS,4BAAM,CAAC;;AAEhB,SAAS,oCAAc,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;IAChD,+BAAS,gCAAU;IACnB,UAAU,WAAW,CAAC,GAAG,2DAA2D;IACpF,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,uEAAuE;IAEvE,IAAI,OAAO,aAAa,WAAW,WAAW,kBAAkB,8BAAQ,4DAA4D;IACpI,+BAA+B;IAE/B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;IACtC,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,kBAAkB,EAAE,oDAAoD;IACrI,iEAAiE;IACjE,0DAA0D;IAE1D,IAAI,CAAC,aAAa,GAAG,uCAAiB,IAAI,EAAE,SAAS,yBAAyB,WAAW,4BAA4B;IAErH,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,oBAAoB;IAE9C,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,gCAAgC;IAExD,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE,2CAA2C;IAEhE,IAAI,CAAC,KAAK,GAAG,KAAK,EAAE,2BAA2B;IAE/C,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,wBAAwB;IAE/C,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,kEAAkE;IAC1F,kEAAkE;IAClE,6BAA6B;IAE7B,IAAI,WAAW,QAAQ,aAAa,KAAK,KAAK;IAC9C,IAAI,CAAC,aAAa,GAAG,CAAC,UAAU,sEAAsE;IACtG,6DAA6D;IAC7D,uDAAuD;IAEvD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI,QAAQ,2DAA2D;IACrH,6DAA6D;IAC7D,kBAAkB;IAElB,IAAI,CAAC,MAAM,GAAG,GAAG,qDAAqD;IAEtE,IAAI,CAAC,OAAO,GAAG,KAAK,EAAE,6DAA6D;IAEnF,IAAI,CAAC,MAAM,GAAG,GAAG,qEAAqE;IACtF,iEAAiE;IACjE,oEAAoE;IACpE,0CAA0C;IAE1C,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,sEAAsE;IACxF,oEAAoE;IACpE,6CAA6C;IAE7C,IAAI,CAAC,gBAAgB,GAAG,KAAK,EAAE,iDAAiD;IAEhF,IAAI,CAAC,OAAO,GAAG,SAAU,EAAE,EAAE;QAC3B,8BAAQ,QAAQ;IAClB,GAAG,kEAAkE;IAGrE,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,0DAA0D;IAE/E,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,EAAE,kDAAkD;IACnF,gDAAgD;IAEhD,IAAI,CAAC,SAAS,GAAG,GAAG,mEAAmE;IACvF,qDAAqD;IAErD,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,uEAAuE;IAEjG,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,wDAAwD;IAEnF,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,KAAK,KAAK,EAAE,qEAAqE;IAEnH,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,QAAQ,WAAW,EAAE,0BAA0B;IAEpE,IAAI,CAAC,oBAAoB,GAAG,GAAG,oDAAoD;IACnF,6DAA6D;IAE7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,oCAAc,IAAI;AAClD;AAEA,oCAAc,SAAS,CAAC,SAAS,GAAG,SAAS,YAAY;IACvD,IAAI,UAAU,IAAI,CAAC,eAAe;IAClC,IAAI,MAAM,EAAE;IAEZ,MAAO,QAAS;QACd,IAAI,IAAI,CAAC;QACT,UAAU,QAAQ,IAAI;IACxB;IAEA,OAAO;AACT;AAEC,CAAA,WAAY;IACX,IAAI;QACF,OAAO,cAAc,CAAC,oCAAc,SAAS,EAAE,UAAU;YACvD,KAAK,mCAAa,SAAS,CAAC,SAAS,4BAA4B;gBAC/D,OAAO,IAAI,CAAC,SAAS;YACvB,GAAG,8EAAmF;QACxF;IACF,EAAE,OAAO,GAAG,CAAC;AACf,CAAA,KAAM,qEAAqE;AAC3E,iDAAiD;AAGjD,IAAI;AAEJ,IAAI,OAAO,WAAW,cAAc,OAAO,WAAW,IAAI,OAAO,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC,KAAK,YAAY;IACtH,wCAAkB,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC;IACxD,OAAO,cAAc,CAAC,gCAAU,OAAO,WAAW,EAAE;QAClD,OAAO,SAAS,MAAM,MAAM,EAAE;YAC5B,IAAI,sCAAgB,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO,IAAI;YACnD,IAAI,IAAI,KAAK,gCAAU,OAAO,KAAK;YACnC,OAAO,UAAU,OAAO,cAAc,YAAY;QACpD;IACF;AACF,OACE,wCAAkB,SAAS,gBAAgB,MAAM,EAAE;IACjD,OAAO,kBAAkB,IAAI;AAC/B;;AAGF,SAAS,+BAAS,OAAO,EAAE;IACzB,+BAAS,gCAAU,0BAA6B,6CAA6C;IAC7F,kEAAkE;IAClE,mEAAmE;IACnE,8EAA8E;IAC9E,2EAA2E;IAC3E,0DAA0D;IAC1D,yEAAyE;IACzE,sDAAsD;IAEtD,IAAI,WAAW,IAAI,YAAY;IAC/B,IAAI,CAAC,YAAY,CAAC,sCAAgB,IAAI,CAAC,gCAAU,IAAI,GAAG,OAAO,IAAI,+BAAS;IAC5E,IAAI,CAAC,cAAc,GAAG,IAAI,oCAAc,SAAS,IAAI,EAAE,WAAW,UAAU;IAE5E,IAAI,CAAC,QAAQ,GAAG,IAAI;IAEpB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QACpE,IAAI,OAAO,QAAQ,MAAM,KAAK,YAAY,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QACvE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;QAC1E,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE,CAAC;IAED,OAAO,IAAI,CAAC,IAAI;AAClB,EAAE,mEAAmE;AAGrE,+BAAS,SAAS,CAAC,IAAI,GAAG,WAAY;IACpC,qCAAe,IAAI,EAAE,IAAI;AAC3B;AAEA,SAAS,oCAAc,MAAM,EAAE,EAAE,EAAE;IACjC,IAAI,KAAK,IAAI,oDAA8B,oEAAoE;IAE/G,qCAAe,QAAQ;IACvB,QAAQ,QAAQ,CAAC,IAAI;AACvB,EAAE,4EAA4E;AAC9E,4EAA4E;AAC5E,mEAAmE;AAGnE,SAAS,iCAAW,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE;IAC5C,IAAI;IAEJ,IAAI,UAAU,IAAI,EAChB,KAAK,IAAI;SACJ,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,UAAU,EACvD,KAAK,IAAI,2CAAqB,SAAS;QAAC;QAAU;KAAS,EAAE;IAG/D,IAAI,IAAI;QACN,qCAAe,QAAQ;QACvB,QAAQ,QAAQ,CAAC,IAAI;QACrB,OAAO,KAAK;IACd,CAAC;IAED,OAAO,IAAI;AACb;AAEA,+BAAS,SAAS,CAAC,KAAK,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IACxD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,MAAM,KAAK;IAEf,IAAI,QAAQ,CAAC,MAAM,UAAU,IAAI,oCAAc;IAE/C,IAAI,SAAS,CAAC,iCAAO,QAAQ,CAAC,QAC5B,QAAQ,0CAAoB;IAG9B,IAAI,OAAO,aAAa,YAAY;QAClC,KAAK;QACL,WAAW,IAAI;IACjB,CAAC;IAED,IAAI,OAAO,WAAW;SAAc,IAAI,CAAC,UAAU,WAAW,MAAM,eAAe;IACnF,IAAI,OAAO,OAAO,YAAY,KAAK;IACnC,IAAI,MAAM,MAAM,EAAE,oCAAc,IAAI,EAAE;SAAS,IAAI,SAAS,iCAAW,IAAI,EAAE,OAAO,OAAO,KAAK;QAC9F,MAAM,SAAS;QACf,MAAM,oCAAc,IAAI,EAAE,OAAO,OAAO,OAAO,UAAU;IAC3D,CAAC;IACD,OAAO;AACT;AAEA,+BAAS,SAAS,CAAC,IAAI,GAAG,WAAY;IACpC,IAAI,CAAC,cAAc,CAAC,MAAM;AAC5B;AAEA,+BAAS,SAAS,CAAC,MAAM,GAAG,WAAY;IACtC,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM;QACZ,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,eAAe,EAAE,kCAAY,IAAI,EAAE;IAC7G,CAAC;AACH;AAEA,+BAAS,SAAS,CAAC,kBAAkB,GAAG,SAAS,mBAAmB,QAAQ,EAAE;IAC5E,6CAA6C;IAC7C,IAAI,OAAO,aAAa,UAAU,WAAW,SAAS,WAAW;IACjE,IAAI,CAAE,CAAA;QAAC;QAAO;QAAQ;QAAS;QAAS;QAAU;QAAU;QAAQ;QAAS;QAAW;QAAY;KAAM,CAAC,OAAO,CAAC,AAAC,CAAA,WAAW,EAAC,EAAG,WAAW,MAAM,EAAC,GAAI,MAAM,IAAI,2CAAqB,UAAU;IAClM,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG;IACtC,OAAO,IAAI;AACb;AAEA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7D;AACF;AAEA,SAAS,kCAAY,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3C,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,aAAa,KAAK,KAAK,IAAI,OAAO,UAAU,UACzE,QAAQ,iCAAO,IAAI,CAAC,OAAO;IAG7B,OAAO;AACT;AAEA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF,IAAI,yDAAyD;AAC7D,2DAA2D;AAC3D,oEAAoE;AAEpE,SAAS,oCAAc,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAChE,IAAI,CAAC,OAAO;QACV,IAAI,WAAW,kCAAY,OAAO,OAAO;QAEzC,IAAI,UAAU,UAAU;YACtB,QAAQ,IAAI;YACZ,WAAW;YACX,QAAQ;QACV,CAAC;IACH,CAAC;IAED,IAAI,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;IAC7C,MAAM,MAAM,IAAI;IAChB,IAAI,MAAM,MAAM,MAAM,GAAG,MAAM,aAAa,EAAE,qEAAqE;IAEnH,IAAI,CAAC,KAAK,MAAM,SAAS,GAAG,IAAI;IAEhC,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE;QACjC,IAAI,OAAO,MAAM,mBAAmB;QACpC,MAAM,mBAAmB,GAAG;YAC1B,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM,IAAI;QACZ;QAEA,IAAI,MACF,KAAK,IAAI,GAAG,MAAM,mBAAmB;aAErC,MAAM,eAAe,GAAG,MAAM,mBAAmB;QAGnD,MAAM,oBAAoB,IAAI;IAChC,OACE,8BAAQ,QAAQ,OAAO,KAAK,EAAE,KAAK,OAAO,UAAU;IAGtD,OAAO;AACT;AAEA,SAAS,8BAAQ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAChE,MAAM,QAAQ,GAAG;IACjB,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG,IAAI;IACpB,MAAM,IAAI,GAAG,IAAI;IACjB,IAAI,MAAM,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,2CAAqB;SAAe,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,MAAM,OAAO;SAAO,OAAO,MAAM,CAAC,OAAO,UAAU,MAAM,OAAO;IAC7K,MAAM,IAAI,GAAG,KAAK;AACpB;AAEA,SAAS,mCAAa,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;IACjD,EAAE,MAAM,SAAS;IAEjB,IAAI,MAAM;QACR,0DAA0D;QAC1D,yCAAyC;QACzC,QAAQ,QAAQ,CAAC,IAAI,KAAK,kDAAkD;QAC5E,cAAc;QAEd,QAAQ,QAAQ,CAAC,mCAAa,QAAQ;QACtC,OAAO,cAAc,CAAC,YAAY,GAAG,IAAI;QACzC,qCAAe,QAAQ;IACzB,OAAO;QACL,6CAA6C;QAC7C,cAAc;QACd,GAAG;QACH,OAAO,cAAc,CAAC,YAAY,GAAG,IAAI;QACzC,qCAAe,QAAQ,KAAK,wCAAwC;QACpE,sBAAsB;QAEtB,kCAAY,QAAQ;IACtB,CAAC;AACH;AAEA,SAAS,yCAAmB,KAAK,EAAE;IACjC,MAAM,OAAO,GAAG,KAAK;IACrB,MAAM,OAAO,GAAG,IAAI;IACpB,MAAM,MAAM,IAAI,MAAM,QAAQ;IAC9B,MAAM,QAAQ,GAAG;AACnB;AAEA,SAAS,8BAAQ,MAAM,EAAE,EAAE,EAAE;IAC3B,IAAI,QAAQ,OAAO,cAAc;IACjC,IAAI,OAAO,MAAM,IAAI;IACrB,IAAI,KAAK,MAAM,OAAO;IACtB,IAAI,OAAO,OAAO,YAAY,MAAM,IAAI,8CAAwB;IAChE,yCAAmB;IACnB,IAAI,IAAI,mCAAa,QAAQ,OAAO,MAAM,IAAI;SAAS;QACrD,8DAA8D;QAC9D,IAAI,WAAW,iCAAW,UAAU,OAAO,SAAS;QAEpD,IAAI,CAAC,YAAY,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,eAAe,EAChF,kCAAY,QAAQ;QAGtB,IAAI,MACF,QAAQ,QAAQ,CAAC,kCAAY,QAAQ,OAAO,UAAU;aAEtD,iCAAW,QAAQ,OAAO,UAAU;IAExC,CAAC;AACH;AAEA,SAAS,iCAAW,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC/C,IAAI,CAAC,UAAU,mCAAa,QAAQ;IACpC,MAAM,SAAS;IACf;IACA,kCAAY,QAAQ;AACtB,EAAE,iEAAiE;AACnE,mEAAmE;AACnE,wDAAwD;AAGxD,SAAS,mCAAa,MAAM,EAAE,KAAK,EAAE;IACnC,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,SAAS,EAAE;QACzC,MAAM,SAAS,GAAG,KAAK;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;AACH,EAAE,8DAA8D;AAGhE,SAAS,kCAAY,MAAM,EAAE,KAAK,EAAE;IAClC,MAAM,gBAAgB,GAAG,IAAI;IAC7B,IAAI,QAAQ,MAAM,eAAe;IAEjC,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,IAAI,EAAE;QACzC,8CAA8C;QAC9C,IAAI,IAAI,MAAM,oBAAoB;QAClC,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,SAAS,MAAM,kBAAkB;QACrC,OAAO,KAAK,GAAG;QACf,IAAI,QAAQ;QACZ,IAAI,aAAa,IAAI;QAErB,MAAO,MAAO;YACZ,MAAM,CAAC,MAAM,GAAG;YAChB,IAAI,CAAC,MAAM,KAAK,EAAE,aAAa,KAAK;YACpC,QAAQ,MAAM,IAAI;YAClB,SAAS;QACX;QAEA,OAAO,UAAU,GAAG;QACpB,8BAAQ,QAAQ,OAAO,IAAI,EAAE,MAAM,MAAM,EAAE,QAAQ,IAAI,OAAO,MAAM,GAAG,oEAAoE;QAC3I,oCAAoC;QAEpC,MAAM,SAAS;QACf,MAAM,mBAAmB,GAAG,IAAI;QAEhC,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,kBAAkB,GAAG,OAAO,IAAI;YACtC,OAAO,IAAI,GAAG,IAAI;QACpB,OACE,MAAM,kBAAkB,GAAG,IAAI,oCAAc;QAG/C,MAAM,oBAAoB,GAAG;IAC/B,OAAO;QACL,qCAAqC;QACrC,MAAO,MAAO;YACZ,IAAI,QAAQ,MAAM,KAAK;YACvB,IAAI,WAAW,MAAM,QAAQ;YAC7B,IAAI,KAAK,MAAM,QAAQ;YACvB,IAAI,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;YAC7C,8BAAQ,QAAQ,OAAO,KAAK,EAAE,KAAK,OAAO,UAAU;YACpD,QAAQ,MAAM,IAAI;YAClB,MAAM,oBAAoB,IAAI,kDAAkD;YAChF,+CAA+C;YAC/C,uDAAuD;YACvD,yDAAyD;YAEzD,IAAI,MAAM,OAAO,EACf,KAAM;QAEV;QAEA,IAAI,UAAU,IAAI,EAAE,MAAM,mBAAmB,GAAG,IAAI;IACtD,CAAC;IAED,MAAM,eAAe,GAAG;IACxB,MAAM,gBAAgB,GAAG,KAAK;AAChC;AAEA,+BAAS,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IACzD,GAAG,IAAI,iDAA2B;AACpC;AAEA,+BAAS,SAAS,CAAC,OAAO,GAAG,IAAI;AAEjC,+BAAS,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IACtD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,OAAO,UAAU,YAAY;QAC/B,KAAK;QACL,QAAQ,IAAI;QACZ,WAAW,IAAI;IACjB,OAAO,IAAI,OAAO,aAAa,YAAY;QACzC,KAAK;QACL,WAAW,IAAI;IACjB,CAAC;IAED,IAAI,UAAU,IAAI,IAAI,UAAU,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO,WAAW,uBAAuB;IAE/F,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM,GAAG;QACf,IAAI,CAAC,MAAM;IACb,CAAC,CAAC,kCAAkC;IAGpC,IAAI,CAAC,MAAM,MAAM,EAAE,kCAAY,IAAI,EAAE,OAAO;IAC5C,OAAO,IAAI;AACb;AAEA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,kBAAkB;IAC1D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;IACnC;AACF;AAEA,SAAS,iCAAW,KAAK,EAAE;IACzB,OAAO,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,eAAe,KAAK,IAAI,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,OAAO;AAClH;AAEA,SAAS,gCAAU,MAAM,EAAE,KAAK,EAAE;IAChC,OAAO,MAAM,CAAC,SAAU,GAAG,EAAE;QAC3B,MAAM,SAAS;QAEf,IAAI,KACF,qCAAe,QAAQ;QAGzB,MAAM,WAAW,GAAG,IAAI;QACxB,OAAO,IAAI,CAAC;QACZ,kCAAY,QAAQ;IACtB;AACF;AAEA,SAAS,gCAAU,MAAM,EAAE,KAAK,EAAE;IAChC,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,WAAW;QAC1C,IAAI,OAAO,OAAO,MAAM,KAAK,cAAc,CAAC,MAAM,SAAS,EAAE;YAC3D,MAAM,SAAS;YACf,MAAM,WAAW,GAAG,IAAI;YACxB,QAAQ,QAAQ,CAAC,iCAAW,QAAQ;QACtC,OAAO;YACL,MAAM,WAAW,GAAG,IAAI;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;KACF;AACH;AAEA,SAAS,kCAAY,MAAM,EAAE,KAAK,EAAE;IAClC,IAAI,OAAO,iCAAW;IAEtB,IAAI,MAAM;QACR,gCAAU,QAAQ;QAElB,IAAI,MAAM,SAAS,KAAK,GAAG;YACzB,MAAM,QAAQ,GAAG,IAAI;YACrB,OAAO,IAAI,CAAC;YAEZ,IAAI,MAAM,WAAW,EAAE;gBACrB,oDAAoD;gBACpD,wDAAwD;gBACxD,IAAI,SAAS,OAAO,cAAc;gBAElC,IAAI,CAAC,UAAU,OAAO,WAAW,IAAI,OAAO,UAAU,EACpD,OAAO,OAAO;YAElB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO;AACT;AAEA,SAAS,kCAAY,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE;IACtC,MAAM,MAAM,GAAG,IAAI;IACnB,kCAAY,QAAQ;IAEpB,IAAI;QACF,IAAI,MAAM,QAAQ,EAAE,QAAQ,QAAQ,CAAC;aAAS,OAAO,IAAI,CAAC,UAAU;KACrE;IAED,MAAM,KAAK,GAAG,IAAI;IAClB,OAAO,QAAQ,GAAG,KAAK;AACzB;AAEA,SAAS,qCAAe,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;IAC3C,IAAI,QAAQ,QAAQ,KAAK;IACzB,QAAQ,KAAK,GAAG,IAAI;IAEpB,MAAO,MAAO;QACZ,IAAI,KAAK,MAAM,QAAQ;QACvB,MAAM,SAAS;QACf,GAAG;QACH,QAAQ,MAAM,IAAI;IACpB,EAAE,0BAA0B;IAG5B,MAAM,kBAAkB,CAAC,IAAI,GAAG;AAClC;AAEA,OAAO,cAAc,CAAC,+BAAS,SAAS,EAAE,aAAa;IACrD,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY,KAAK;IACjB,KAAK,SAAS,MAAM;QAClB,IAAI,IAAI,CAAC,cAAc,KAAK,WAC1B,OAAO,KAAK;QAGd,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;IACtC;IACA,KAAK,SAAS,IAAI,KAAK,EAAE;QACvB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EACtB;QACD,CAAC,iDAAiD;QACnD,qBAAqB;QAGrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AACA,+BAAS,SAAS,CAAC,OAAO,GAAG;AAC7B,+BAAS,SAAS,CAAC,UAAU,GAAG;AAEhC,+BAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE,EAAE;IAC/C,GAAG;AACL;;;;ACvrBA;;CAEC,GAED;AAAA,iBAAiB;;;;;;;;;ACLjB,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAuDzC,4EAA4E;AAC5E,wEAAwE;AACxE,cAAc;AACd,IAAA;AAxDA;;;uCAII;AACJ,gBAAgB,GAEhB,IAAI,mCAAa,iCAAO,UAAU,IAAI,SAAU,QAAQ,EAAE;IACxD,WAAW,KAAK;IAChB,OAAQ,YAAY,SAAS,WAAW;QACtC,KAAK;QAAM,KAAK;QAAO,KAAK;QAAQ,KAAK;QAAQ,KAAK;QAAS,KAAK;QAAS,KAAK;QAAO,KAAK;QAAQ,KAAK;QAAU,KAAK;QAAW,KAAK;YACxI,OAAO,IAAI;QACb;YACE,OAAO,KAAK;IAChB;AACF;AAEA,SAAS,yCAAmB,GAAG,EAAE;IAC/B,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI;IACJ,MAAO,IAAI,CACT,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,IAAI,SAAS,QAAQ,YAAY;YACjC,MAAM,AAAC,CAAA,KAAK,GAAE,EAAG,WAAW;YAC5B,UAAU,IAAI;IAClB;AAEJ;AAEA,wEAAwE;AACxE,0DAA0D;AAC1D,SAAS,wCAAkB,GAAG,EAAE;IAC9B,IAAI,OAAO,yCAAmB;IAC9B,IAAI,OAAO,SAAS,YAAa,CAAA,iCAAO,UAAU,KAAK,oCAAc,CAAC,iCAAW,IAAG,GAAI,MAAM,IAAI,MAAM,uBAAuB,KAAK;IACpI,OAAO,QAAQ;AACjB;AAKA,4CAAwB;AACxB,SAAS,oCAAc,QAAQ,EAAE;IAC/B,IAAI,CAAC,QAAQ,GAAG,wCAAkB;IAClC,IAAI;IACJ,OAAQ,IAAI,CAAC,QAAQ;QACnB,KAAK;YACH,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,GAAG,GAAG;YACX,KAAK;YACL,KAAM;QACR,KAAK;YACH,IAAI,CAAC,QAAQ,GAAG;YAChB,KAAK;YACL,KAAM;QACR,KAAK;YACH,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,GAAG,GAAG;YACX,KAAK;YACL,KAAM;QACR;YACE,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,GAAG,GAAG;YACX;IACJ;IACA,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,QAAQ,GAAG,iCAAO,WAAW,CAAC;AACrC;AAEA,oCAAc,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG,EAAE;IAC7C,IAAI,IAAI,MAAM,KAAK,GAAG,OAAO;IAC7B,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,IAAI,CAAC,QAAQ,CAAC;QAClB,IAAI,MAAM,WAAW,OAAO;QAC5B,IAAI,IAAI,CAAC,QAAQ;QACjB,IAAI,CAAC,QAAQ,GAAG;IAClB,OACE,IAAI;IAEN,IAAI,IAAI,IAAI,MAAM,EAAE,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;IACxE,OAAO,KAAK;AACd;AAEA,oCAAc,SAAS,CAAC,GAAG,GAAG;AAE9B,+CAA+C;AAC/C,oCAAc,SAAS,CAAC,IAAI,GAAG;AAE/B,+EAA+E;AAC/E,oCAAc,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE;IAChD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;QAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;QACxE,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,SAAS;IAChE,CAAC;IACD,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,MAAM;IACrE,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM;AAC7B;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,SAAS,oCAAc,IAAI,EAAE;IAC3B,IAAI,QAAQ,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;SAAO,IAAI,QAAQ,MAAM,MAAM,OAAO;IAC3I,OAAO,QAAQ,MAAM,OAAO,KAAK,EAAE;AACrC;AAEA,sEAAsE;AACtE,gFAAgF;AAChF,uEAAuE;AACvE,SAAS,0CAAoB,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IACzC,IAAI,IAAI,IAAI,MAAM,GAAG;IACrB,IAAI,IAAI,GAAG,OAAO;IAClB,IAAI,KAAK,oCAAc,GAAG,CAAC,EAAE;IAC7B,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG,KAAK,QAAQ,GAAG,KAAK;QACjC,OAAO;IACT,CAAC;IACD,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,OAAO;IACjC,KAAK,oCAAc,GAAG,CAAC,EAAE;IACzB,IAAI,MAAM,GAAG;QACX,IAAI,KAAK,GAAG,KAAK,QAAQ,GAAG,KAAK;QACjC,OAAO;IACT,CAAC;IACD,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,OAAO;IACjC,KAAK,oCAAc,GAAG,CAAC,EAAE;IACzB,IAAI,MAAM,GAAG;QACX,IAAI,KAAK;YACP,IAAI,OAAO,GAAG,KAAK;iBAAO,KAAK,QAAQ,GAAG,KAAK;SAChD;QACD,OAAO;IACT,CAAC;IACD,OAAO;AACT;AAEA,2EAA2E;AAC3E,6EAA6E;AAC7E,4EAA4E;AAC5E,gFAAgF;AAChF,4EAA4E;AAC5E,gFAAgF;AAChF,+EAA+E;AAC/E,QAAQ;AACR,SAAS,0CAAoB,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;IACzC,IAAI,AAAC,CAAA,GAAG,CAAC,EAAE,GAAG,IAAG,MAAO,MAAM;QAC5B,KAAK,QAAQ,GAAG;QAChB,OAAO;IACT,CAAC;IACD,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,MAAM,GAAG,GAAG;QACvC,IAAI,AAAC,CAAA,GAAG,CAAC,EAAE,GAAG,IAAG,MAAO,MAAM;YAC5B,KAAK,QAAQ,GAAG;YAChB,OAAO;QACT,CAAC;QACD,IAAI,KAAK,QAAQ,GAAG,KAAK,IAAI,MAAM,GAAG,GACpC;YAAA,IAAI,AAAC,CAAA,GAAG,CAAC,EAAE,GAAG,IAAG,MAAO,MAAM;gBAC5B,KAAK,QAAQ,GAAG;gBAChB,OAAO;YACT,CAAC;QAAD,CACD;IACH,CAAC;AACH;AAEA,+EAA+E;AAC/E,SAAS,mCAAa,GAAG,EAAE;IACzB,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;IACtC,IAAI,IAAI,0CAAoB,IAAI,EAAE,KAAK;IACvC,IAAI,MAAM,WAAW,OAAO;IAC5B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;QAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ;QAC3C,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,SAAS;IAChE,CAAC;IACD,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI,MAAM;IACxC,IAAI,CAAC,QAAQ,IAAI,IAAI,MAAM;AAC7B;AAEA,8EAA8E;AAC9E,2EAA2E;AAC3E,iCAAiC;AACjC,SAAS,+BAAS,GAAG,EAAE,CAAC,EAAE;IACxB,IAAI,QAAQ,0CAAoB,IAAI,EAAE,KAAK;IAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,QAAQ,CAAC,QAAQ;IAChD,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,MAAM,IAAI,MAAM,GAAI,CAAA,QAAQ,IAAI,CAAC,QAAQ,AAAD;IAC5C,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG;IAC3B,OAAO,IAAI,QAAQ,CAAC,QAAQ,GAAG;AACjC;AAEA,uEAAuE;AACvE,aAAa;AACb,SAAS,8BAAQ,GAAG,EAAE;IACpB,IAAI,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;IAChD,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI;IAC9B,OAAO;AACT;AAEA,gFAAgF;AAChF,0EAA0E;AAC1E,8EAA8E;AAC9E,sCAAsC;AACtC,SAAS,gCAAU,GAAG,EAAE,CAAC,EAAE;IACzB,IAAI,AAAC,CAAA,IAAI,MAAM,GAAG,CAAA,IAAK,MAAM,GAAG;QAC9B,IAAI,IAAI,IAAI,QAAQ,CAAC,WAAW;QAChC,IAAI,GAAG;YACL,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG;YAChC,IAAI,KAAK,UAAU,KAAK,QAAQ;gBAC9B,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;gBACtC,OAAO,EAAE,KAAK,CAAC,GAAG;YACpB,CAAC;QACH,CAAC;QACD,OAAO;IACT,CAAC;IACD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;IACtC,OAAO,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,MAAM,GAAG;AACjD;AAEA,gFAAgF;AAChF,4DAA4D;AAC5D,SAAS,+BAAS,GAAG,EAAE;IACrB,IAAI,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;IAChD,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;QACxC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,GAAG;IAClD,CAAC;IACD,OAAO;AACT;AAEA,SAAS,iCAAW,GAAG,EAAE,CAAC,EAAE;IAC1B,IAAI,IAAI,AAAC,CAAA,IAAI,MAAM,GAAG,CAAA,IAAK;IAC3B,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,CAAC,UAAU;IAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,MAAM,GACR,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;SACjC;QACL,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;IACxC,CAAC;IACD,OAAO,IAAI,QAAQ,CAAC,UAAU,GAAG,IAAI,MAAM,GAAG;AAChD;AAEA,SAAS,gCAAU,GAAG,EAAE;IACtB,IAAI,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;IAChD,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,QAAQ;IACnF,OAAO;AACT;AAEA,4EAA4E;AAC5E,SAAS,kCAAY,GAAG,EAAE;IACxB,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ;AACnC;AAEA,SAAS,gCAAU,GAAG,EAAE;IACtB,OAAO,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACjD;;;;ACvSA,kFAAkF,GAClF,yCAAyC,GACzC;AACA,IAAI,+BAAS,cAAO,MAAM;AAE1B,oDAAoD;AACpD,SAAS,gCAAW,GAAG,EAAE,GAAG,EAAE;IAC5B,IAAK,IAAI,OAAO,IACd,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;AAEvB;AACA,IAAI,6BAAO,IAAI,IAAI,6BAAO,KAAK,IAAI,6BAAO,WAAW,IAAI,6BAAO,eAAe,EAC7E,iBAAiB;KACZ;IACL,yCAAyC;IACzC,gCAAU,eAAQ;IAClB,eAAQ,MAAM,GAAG;AACnB,CAAC;AAED,SAAS,iCAAY,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE;IAClD,OAAO,6BAAO,KAAK,kBAAkB;AACvC;AAEA,iCAAW,SAAS,GAAG,OAAO,MAAM,CAAC,6BAAO,SAAS;AAErD,kCAAkC;AAClC,gCAAU,8BAAQ;AAElB,iCAAW,IAAI,GAAG,SAAU,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE;IACzD,IAAI,OAAO,QAAQ,UACjB,MAAM,IAAI,UAAU,iCAAgC;IAEtD,OAAO,6BAAO,KAAK,kBAAkB;AACvC;AAEA,iCAAW,KAAK,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IACjD,IAAI,OAAO,SAAS,UAClB,MAAM,IAAI,UAAU,6BAA4B;IAElD,IAAI,MAAM,6BAAO;IACjB,IAAI,SAAS;QACX,IAAI,OAAO,aAAa,UACtB,IAAI,IAAI,CAAC,MAAM;aAEf,IAAI,IAAI,CAAC;WAGX,IAAI,IAAI,CAAC;IAEX,OAAO;AACT;AAEA,iCAAW,WAAW,GAAG,SAAU,IAAI,EAAE;IACvC,IAAI,OAAO,SAAS,UAClB,MAAM,IAAI,UAAU,6BAA4B;IAElD,OAAO,6BAAO;AAChB;AAEA,iCAAW,eAAe,GAAG,SAAU,IAAI,EAAE;IAC3C,IAAI,OAAO,SAAS,UAClB,MAAM,IAAI,UAAU,6BAA4B;IAElD,OAAO,cAAO,UAAU,CAAC;AAC3B;;;;;;AChEA;AAEA,IAAI;AAEJ,SAAS,sCAAgB,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IAAE,IAAI,OAAO,KAAO,OAAO,cAAc,CAAC,KAAK,KAAK;QAAE,OAAO;QAAO,YAAY,IAAI;QAAE,cAAc,IAAI;QAAE,UAAU,IAAI;IAAC;SAAa,GAAG,CAAC,IAAI,GAAG;IAAS,OAAO;AAAK;;;AAIhN,IAAI,qCAAe,OAAO;AAC1B,IAAI,oCAAc,OAAO;AACzB,IAAI,+BAAS,OAAO;AACpB,IAAI,+BAAS,OAAO;AACpB,IAAI,qCAAe,OAAO;AAC1B,IAAI,uCAAiB,OAAO;AAC5B,IAAI,gCAAU,OAAO;AAErB,SAAS,uCAAiB,KAAK,EAAE,IAAI,EAAE;IACrC,OAAO;QACL,OAAO;QACP,MAAM;IACR;AACF;AAEA,SAAS,qCAAe,IAAI,EAAE;IAC5B,IAAI,UAAU,IAAI,CAAC,mCAAa;IAEhC,IAAI,YAAY,IAAI,EAAE;QACpB,IAAI,OAAO,IAAI,CAAC,8BAAQ,CAAC,IAAI,IAAI,2BAA2B;QAC5D,sCAAsC;QACtC,UAAU;QAEV,IAAI,SAAS,IAAI,EAAE;YACjB,IAAI,CAAC,mCAAa,GAAG,IAAI;YACzB,IAAI,CAAC,mCAAa,GAAG,IAAI;YACzB,IAAI,CAAC,kCAAY,GAAG,IAAI;YACxB,QAAQ,uCAAiB,MAAM,KAAK;QACtC,CAAC;IACH,CAAC;AACH;AAEA,SAAS,iCAAW,IAAI,EAAE;IACxB,8CAA8C;IAC9C,sCAAsC;IACtC,QAAQ,QAAQ,CAAC,sCAAgB;AACnC;AAEA,SAAS,kCAAY,WAAW,EAAE,IAAI,EAAE;IACtC,OAAO,SAAU,OAAO,EAAE,MAAM,EAAE;QAChC,YAAY,IAAI,CAAC,WAAY;YAC3B,IAAI,IAAI,CAAC,6BAAO,EAAE;gBAChB,QAAQ,uCAAiB,WAAW,IAAI;gBACxC;YACF,CAAC;YAED,IAAI,CAAC,qCAAe,CAAC,SAAS;QAChC,GAAG;IACL;AACF;AAEA,IAAI,+CAAyB,OAAO,cAAc,CAAC,WAAY,CAAC;AAChE,IAAI,6DAAuC,OAAO,cAAc,CAAE,CAAA,8CAAwB;IACxF,IAAI,UAAS;QACX,OAAO,IAAI,CAAC,8BAAQ;IACtB;IAEA,MAAM,SAAS,OAAO;QACpB,IAAI,QAAQ,IAAI;QAEhB,gDAAgD;QAChD,uBAAuB;QACvB,IAAI,QAAQ,IAAI,CAAC,6BAAO;QAExB,IAAI,UAAU,IAAI,EAChB,OAAO,QAAQ,MAAM,CAAC;QAGxB,IAAI,IAAI,CAAC,6BAAO,EACd,OAAO,QAAQ,OAAO,CAAC,uCAAiB,WAAW,IAAI;QAGzD,IAAI,IAAI,CAAC,8BAAQ,CAAC,SAAS,EACzB,4DAA4D;QAC5D,sDAAsD;QACtD,8DAA8D;QAC9D,yBAAyB;QACzB,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM,EAAE;YAC5C,QAAQ,QAAQ,CAAC,WAAY;gBAC3B,IAAI,KAAK,CAAC,6BAAO,EACf,OAAO,KAAK,CAAC,6BAAO;qBAEpB,QAAQ,uCAAiB,WAAW,IAAI;YAE5C;QACF;QACD,CAAC,mCAAmC;QACrC,kDAAkD;QAClD,sDAAsD;QACtD,6CAA6C;QAG7C,IAAI,cAAc,IAAI,CAAC,mCAAa;QACpC,IAAI;QAEJ,IAAI,aACF,UAAU,IAAI,QAAQ,kCAAY,aAAa,IAAI;aAC9C;YACL,mDAAmD;YACnD,sCAAsC;YACtC,IAAI,OAAO,IAAI,CAAC,8BAAQ,CAAC,IAAI;YAE7B,IAAI,SAAS,IAAI,EACf,OAAO,QAAQ,OAAO,CAAC,uCAAiB,MAAM,KAAK;YAGrD,UAAU,IAAI,QAAQ,IAAI,CAAC,qCAAe;QAC5C,CAAC;QAED,IAAI,CAAC,mCAAa,GAAG;QACrB,OAAO;IACT;AACF,GAAG,sCAAgB,6CAAuB,OAAO,aAAa,EAAE,WAAY;IAC1E,OAAO,IAAI;AACb,IAAI,sCAAgB,6CAAuB,UAAU,SAAS,UAAU;IACtE,IAAI,SAAS,IAAI;IAEjB,oCAAoC;IACpC,6DAA6D;IAC7D,qCAAqC;IACrC,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM,EAAE;QAC5C,MAAM,CAAC,8BAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,SAAU,GAAG,EAAE;YAC3C,IAAI,KAAK;gBACP,OAAO;gBACP;YACF,CAAC;YAED,QAAQ,uCAAiB,WAAW,IAAI;QAC1C;IACF;AACF,IAAI,2CAAqB,AAAD,GAAI;AAE5B,IAAI,0DAAoC,SAAS,kCAAkC,MAAM,EAAE;IACzF,IAAI;IAEJ,IAAI,WAAW,OAAO,MAAM,CAAC,4DAAuC,CAAA,iBAAiB,CAAC,GAAG,sCAAgB,gBAAgB,+BAAS;QAChI,OAAO;QACP,UAAU,IAAI;IAChB,IAAI,sCAAgB,gBAAgB,oCAAc;QAChD,OAAO,IAAI;QACX,UAAU,IAAI;IAChB,IAAI,sCAAgB,gBAAgB,mCAAa;QAC/C,OAAO,IAAI;QACX,UAAU,IAAI;IAChB,IAAI,sCAAgB,gBAAgB,8BAAQ;QAC1C,OAAO,IAAI;QACX,UAAU,IAAI;IAChB,IAAI,sCAAgB,gBAAgB,8BAAQ;QAC1C,OAAO,OAAO,cAAc,CAAC,UAAU;QACvC,UAAU,IAAI;IAChB,IAAI,sCAAgB,gBAAgB,sCAAgB;QAClD,OAAO,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE;YACrC,IAAI,OAAO,QAAQ,CAAC,8BAAQ,CAAC,IAAI;YAEjC,IAAI,MAAM;gBACR,QAAQ,CAAC,mCAAa,GAAG,IAAI;gBAC7B,QAAQ,CAAC,mCAAa,GAAG,IAAI;gBAC7B,QAAQ,CAAC,kCAAY,GAAG,IAAI;gBAC5B,QAAQ,uCAAiB,MAAM,KAAK;YACtC,OAAO;gBACL,QAAQ,CAAC,mCAAa,GAAG;gBACzB,QAAQ,CAAC,kCAAY,GAAG;YAC1B,CAAC;QACH;QACA,UAAU,IAAI;IAChB,IAAI,cAAc,AAAD;IACjB,QAAQ,CAAC,mCAAa,GAAG,IAAI;IAC7B,OAAS,QAAQ,SAAU,GAAG,EAAE;QAC9B,IAAI,OAAO,IAAI,IAAI,KAAK,8BAA8B;YACpD,IAAI,SAAS,QAAQ,CAAC,kCAAY,EAAE,mDAAmD;YACvF,yCAAyC;YAEzC,IAAI,WAAW,IAAI,EAAE;gBACnB,QAAQ,CAAC,mCAAa,GAAG,IAAI;gBAC7B,QAAQ,CAAC,mCAAa,GAAG,IAAI;gBAC7B,QAAQ,CAAC,kCAAY,GAAG,IAAI;gBAC5B,OAAO;YACT,CAAC;YAED,QAAQ,CAAC,6BAAO,GAAG;YACnB;QACF,CAAC;QAED,IAAI,UAAU,QAAQ,CAAC,mCAAa;QAEpC,IAAI,YAAY,IAAI,EAAE;YACpB,QAAQ,CAAC,mCAAa,GAAG,IAAI;YAC7B,QAAQ,CAAC,mCAAa,GAAG,IAAI;YAC7B,QAAQ,CAAC,kCAAY,GAAG,IAAI;YAC5B,QAAQ,uCAAiB,WAAW,IAAI;QAC1C,CAAC;QAED,QAAQ,CAAC,6BAAO,GAAG,IAAI;IACzB;IACA,OAAO,EAAE,CAAC,YAAY,iCAAW,IAAI,CAAC,IAAI,EAAE;IAC5C,OAAO;AACT;AAEA,iBAAiB;;;;AC9MjB,8DAA8D;AAC9D,yDAAyD;AACzD;;;AAEA,IAAI,mDAA6B,aAAiC,0BAA0B;AAE5F,SAAS,2BAAK,QAAQ,EAAE;IACtB,IAAI,SAAS,KAAK;IAClB,OAAO,WAAY;QACjB,IAAI,QAAQ;QACZ,SAAS,IAAI;QAEb,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAC/E,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAG9B,SAAS,KAAK,CAAC,IAAI,EAAE;IACvB;AACF;AAEA,SAAS,6BAAO,CAAC;AAEjB,SAAS,gCAAU,MAAM,EAAE;IACzB,OAAO,OAAO,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK;AACrD;AAEA,SAAS,0BAAI,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;IACnC,IAAI,OAAO,SAAS,YAAY,OAAO,0BAAI,QAAQ,IAAI,EAAE;IACzD,IAAI,CAAC,MAAM,OAAO,CAAC;IACnB,WAAW,2BAAK,YAAY;IAC5B,IAAI,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ;IAC1E,IAAI,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ;IAE1E,IAAI,iBAAiB,SAAS,iBAAiB;QAC7C,IAAI,CAAC,OAAO,QAAQ,EAAE;IACxB;IAEA,IAAI,gBAAgB,OAAO,cAAc,IAAI,OAAO,cAAc,CAAC,QAAQ;IAE3E,IAAI,WAAW,SAAS,WAAW;QACjC,WAAW,KAAK;QAChB,gBAAgB,IAAI;QACpB,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC;IAC/B;IAEA,IAAI,gBAAgB,OAAO,cAAc,IAAI,OAAO,cAAc,CAAC,UAAU;IAE7E,IAAI,QAAQ,SAAS,QAAQ;QAC3B,WAAW,KAAK;QAChB,gBAAgB,IAAI;QACpB,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC;IAC/B;IAEA,IAAI,UAAU,SAAS,QAAQ,GAAG,EAAE;QAClC,SAAS,IAAI,CAAC,QAAQ;IACxB;IAEA,IAAI,UAAU,SAAS,UAAU;QAC/B,IAAI;QAEJ,IAAI,YAAY,CAAC,eAAe;YAC9B,IAAI,CAAC,OAAO,cAAc,IAAI,CAAC,OAAO,cAAc,CAAC,KAAK,EAAE,MAAM,IAAI;YACtE,OAAO,SAAS,IAAI,CAAC,QAAQ;QAC/B,CAAC;QAED,IAAI,YAAY,CAAC,eAAe;YAC9B,IAAI,CAAC,OAAO,cAAc,IAAI,CAAC,OAAO,cAAc,CAAC,KAAK,EAAE,MAAM,IAAI;YACtE,OAAO,SAAS,IAAI,CAAC,QAAQ;QAC/B,CAAC;IACH;IAEA,IAAI,YAAY,SAAS,YAAY;QACnC,OAAO,GAAG,CAAC,EAAE,CAAC,UAAU;IAC1B;IAEA,IAAI,gCAAU,SAAS;QACrB,OAAO,EAAE,CAAC,YAAY;QACtB,OAAO,EAAE,CAAC,SAAS;QACnB,IAAI,OAAO,GAAG,EAAE;aAAiB,OAAO,EAAE,CAAC,WAAW;IACxD,OAAO,IAAI,YAAY,CAAC,OAAO,cAAc,EAAE;QAC7C,iBAAiB;QACjB,OAAO,EAAE,CAAC,OAAO;QACjB,OAAO,EAAE,CAAC,SAAS;IACrB,CAAC;IAED,OAAO,EAAE,CAAC,OAAO;IACjB,OAAO,EAAE,CAAC,UAAU;IACpB,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,OAAO,EAAE,CAAC,SAAS;IAC7C,OAAO,EAAE,CAAC,SAAS;IACnB,OAAO,WAAY;QACjB,OAAO,cAAc,CAAC,YAAY;QAClC,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,WAAW;QACjC,IAAI,OAAO,GAAG,EAAE,OAAO,GAAG,CAAC,cAAc,CAAC,UAAU;QACpD,OAAO,cAAc,CAAC,OAAO;QAC7B,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,UAAU;QAChC,OAAO,cAAc,CAAC,OAAO;QAC7B,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,SAAS;IACjC;AACF;AAEA,iBAAiB;;;;;;ACvGjB;AAEA,SAAS,yCAAmB,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;IAAE,IAAI;QAAE,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC;QAAM,IAAI,QAAQ,KAAK,KAAK;IAAE,EAAE,OAAO,OAAO;QAAE,OAAO;QAAQ;IAAQ;IAAE,IAAI,KAAK,IAAI,EAAI,QAAQ;SAAiB,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO;AAAW;AAExQ,SAAS,wCAAkB,EAAE,EAAE;IAAE,OAAO,WAAY;QAAE,IAAI,OAAO,IAAI,EAAE,OAAO;QAAW,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM,EAAE;YAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;YAAO,SAAS,MAAM,KAAK,EAAE;gBAAE,yCAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,QAAQ;YAAQ;YAAE,SAAS,OAAO,GAAG,EAAE;gBAAE,yCAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,SAAS;YAAM;YAAE,MAAM;QAAY;IAAI;AAAG;AAEpY,SAAS,8BAAQ,MAAM,EAAE,cAAc,EAAE;IAAE,IAAI,OAAO,OAAO,IAAI,CAAC;IAAS,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAAS,IAAI,gBAAgB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG,EAAE;YAAE,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;QAAE;QAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IAAU,CAAC;IAAC,OAAO;AAAM;AAEpV,SAAS,oCAAc,MAAM,EAAE;IAAE,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC;QAAE,IAAI,IAAI,GAAK,8BAAQ,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,SAAU,GAAG,EAAE;YAAE,sCAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;QAAG;aAAW,IAAI,OAAO,yBAAyB,EAAI,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;aAAmB,8BAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG,EAAE;YAAE,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;QAAO;IAAM;IAAE,OAAO;AAAQ;AAErhB,SAAS,sCAAgB,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IAAE,IAAI,OAAO,KAAO,OAAO,cAAc,CAAC,KAAK,KAAK;QAAE,OAAO;QAAO,YAAY,IAAI;QAAE,cAAc,IAAI;QAAE,UAAU,IAAI;IAAC;SAAa,GAAG,CAAC,IAAI,GAAG;IAAS,OAAO;AAAK;;;AAEhN,IAAI,6CAAuB,aAAiC,oBAAoB;AAEhF,SAAS,2BAAK,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;IACtC,IAAI;IAEJ,IAAI,YAAY,OAAO,SAAS,IAAI,KAAK,YACvC,WAAW;SACN,IAAI,YAAY,QAAQ,CAAC,OAAO,aAAa,CAAC,EAAE,WAAW,QAAQ,CAAC,OAAO,aAAa,CAAC;SAAQ,IAAI,YAAY,QAAQ,CAAC,OAAO,QAAQ,CAAC,EAAE,WAAW,QAAQ,CAAC,OAAO,QAAQ,CAAC;SAAQ,MAAM,IAAI,2CAAqB,YAAY;QAAC;KAAW,EAAE,UAAU;IAElQ,IAAI,WAAW,IAAI,SAAS,oCAAc;QACxC,YAAY,IAAI;IAClB,GAAG,QAAQ,2CAA2C;IACtD,iDAAiD;IAEjD,IAAI,UAAU,KAAK;IAEnB,SAAS,KAAK,GAAG,WAAY;QAC3B,IAAI,CAAC,SAAS;YACZ,UAAU,IAAI;YACd;QACF,CAAC;IACH;IAEA,SAAS,OAAO;QACd,OAAO,OAAO,KAAK,CAAC,IAAI,EAAE;IAC5B;IAEA,SAAS,SAAS;QAChB,SAAS,wCAAkB,YAAa;YACtC,IAAI;gBACF,IAAI,OAAO,MAAM,SAAS,IAAI,IAC1B,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI;gBAEpB,IAAI,MACF,SAAS,IAAI,CAAC,IAAI;qBACb,IAAI,SAAS,IAAI,CAAE,CAAA,MAAM,KAAI,IAClC;qBAEA,UAAU,KAAK;YAEnB,EAAE,OAAO,KAAK;gBACZ,SAAS,OAAO,CAAC;YACnB;QACF;QACA,OAAO,OAAO,KAAK,CAAC,IAAI,EAAE;IAC5B;IAEA,OAAO;AACT;AAEA,iBAAiB;;;;;;AC/DjB,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,gEAAgE;AAChE,8DAA8D;AAC9D,uEAAuE;AACvE,sEAAsE;AACtE,iDAAiD;AACjD,EAAE;AACF,gEAAgE;AAChE,qEAAqE;AACrE,kEAAkE;AAClE,0DAA0D;AAC1D,EAAE;AACF,yBAAyB;AACzB,EAAE;AACF,wEAAwE;AACxE,sEAAsE;AACtE,mEAAmE;AACnE,gEAAgE;AAChE,oDAAoD;AACpD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,qEAAqE;AACrE,uEAAuE;AACvE,qEAAqE;AACrE,sEAAsE;AACtE,EAAE;AACF,sEAAsE;AACtE,0EAA0E;AAC1E,yEAAyE;AACzE,oEAAoE;AACpE,sEAAsE;AACtE,sEAAsE;AACtE,wEAAwE;AACxE,sEAAsE;AACtE,qEAAqE;AACrE,oEAAoE;AACpE,yCAAyC;AACzC,EAAE;AACF,yEAAyE;AACzE,yEAAyE;AACzE,+DAA+D;AAC/D;AAEA,iBAAiB;;;+CAEb;AAAJ,IACI,mDAA6B,yCAAe,0BAA0B,EACtE,8CAAwB,yCAAe,qBAAqB,EAC5D,2DAAqC,yCAAe,kCAAkC,EACtF,oDAA8B,yCAAe,2BAA2B;;;;AAI5E,yBAAoB,iCAAW;AAE/B,SAAS,qCAAe,EAAE,EAAE,IAAI,EAAE;IAChC,IAAI,KAAK,IAAI,CAAC,eAAe;IAC7B,GAAG,YAAY,GAAG,KAAK;IACvB,IAAI,KAAK,GAAG,OAAO;IAEnB,IAAI,OAAO,IAAI,EACb,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI;IAGhC,GAAG,UAAU,GAAG,IAAI;IACpB,GAAG,OAAO,GAAG,IAAI;IACjB,IAAI,QAAQ,IAAI,EACd,IAAI,CAAC,IAAI,CAAC;IACZ,GAAG;IACH,IAAI,KAAK,IAAI,CAAC,cAAc;IAC5B,GAAG,OAAO,GAAG,KAAK;IAElB,IAAI,GAAG,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG,aAAa,EACjD,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa;AAE/B;AAEA,SAAS,gCAAU,OAAO,EAAE;IAC1B,IAAI,CAAE,CAAA,IAAI,YAAY,+BAAQ,GAAI,OAAO,IAAI,gCAAU;IACvD,OAAO,IAAI,CAAC,IAAI,EAAE;IAClB,IAAI,CAAC,eAAe,GAAG;QACrB,gBAAgB,qCAAe,IAAI,CAAC,IAAI;QACxC,eAAe,KAAK;QACpB,cAAc,KAAK;QACnB,SAAS,IAAI;QACb,YAAY,IAAI;QAChB,eAAe,IAAI;IACrB,GAAG,kEAAkE;IAErE,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,EAAE,kEAAkE;IAC3G,gEAAgE;IAChE,mBAAmB;IAEnB,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,KAAK;IAEhC,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QAChF,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE,CAAC,CAAC,sEAAsE;IAGxE,IAAI,CAAC,EAAE,CAAC,aAAa;AACvB;AAEA,SAAS,kCAAY;IACnB,IAAI,QAAQ,IAAI;IAEhB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EACrE,IAAI,CAAC,MAAM,CAAC,SAAU,EAAE,EAAE,IAAI,EAAE;QAC9B,2BAAK,OAAO,IAAI;IAClB;SAEA,2BAAK,IAAI,EAAE,IAAI,EAAE,IAAI;AAEzB;AAEA,gCAAU,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE;IACpD,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,KAAK;IAC1C,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;AACjD,GAAG,uCAAuC;AAC1C,oDAAoD;AACpD,6BAA6B;AAC7B,EAAE;AACF,yDAAyD;AACzD,iEAAiE;AACjE,EAAE;AACF,iEAAiE;AACjE,sEAAsE;AACtE,wDAAwD;AAGxD,gCAAU,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC9D,GAAG,IAAI,iDAA2B;AACpC;AAEA,gCAAU,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC1D,IAAI,KAAK,IAAI,CAAC,eAAe;IAC7B,GAAG,OAAO,GAAG;IACb,GAAG,UAAU,GAAG;IAChB,GAAG,aAAa,GAAG;IAEnB,IAAI,CAAC,GAAG,YAAY,EAAE;QACpB,IAAI,KAAK,IAAI,CAAC,cAAc;QAC5B,IAAI,GAAG,aAAa,IAAI,GAAG,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa;IACtG,CAAC;AACH,GAAG,yCAAyC;AAC5C,gCAAgC;AAChC,iEAAiE;AAGjE,gCAAU,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC,EAAE;IACvC,IAAI,KAAK,IAAI,CAAC,eAAe;IAE7B,IAAI,GAAG,UAAU,KAAK,IAAI,IAAI,CAAC,GAAG,YAAY,EAAE;QAC9C,GAAG,YAAY,GAAG,IAAI;QAEtB,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,aAAa,EAAE,GAAG,cAAc;IACpE,OACE,gEAAgE;IAChE,mDAAmD;IACnD,GAAG,aAAa,GAAG,IAAI;AAE3B;AAEA,gCAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE,EAAE;IAChD,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,SAAU,IAAI,EAAE;QACxD,GAAG;IACL;AACF;AAEA,SAAS,2BAAK,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE;IAC9B,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,SAAS;IACpC,IAAI,QAAQ,IAAI,EACd,OAAO,IAAI,CAAC,OAAO,yDAAyD;IAC9E,0DAA0D;IAC1D,0CAA0C;IAE1C,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,MAAM,IAAI,oDAA8B;IAC1E,IAAI,OAAO,eAAe,CAAC,YAAY,EAAE,MAAM,IAAI,2DAAqC;IACxF,OAAO,OAAO,IAAI,CAAC,IAAI;AACzB;;;;;ACxMA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AACzC,wBAAwB;AACxB,4DAA4D;AAC5D,yCAAyC;AACzC;AAEA,iBAAiB;;;;AAIjB,yBAAoB,mCAAa;AAEjC,SAAS,kCAAY,OAAO,EAAE;IAC5B,IAAI,CAAE,CAAA,IAAI,YAAY,iCAAU,GAAI,OAAO,IAAI,kCAAY;IAC3D,OAAU,IAAI,CAAC,IAAI,EAAE;AACvB;AAEA,kCAAY,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAChE,GAAG,IAAI,EAAE;AACX;;;;;ACtCA,qDAAqD;AACrD,yDAAyD;AACzD;AAEA,IAAI;AAEJ,SAAS,2BAAK,QAAQ,EAAE;IACtB,IAAI,SAAS,KAAK;IAClB,OAAO,WAAY;QACjB,IAAI,QAAQ;QACZ,SAAS,IAAI;QACb,SAAS,KAAK,CAAC,KAAK,GAAG;IACzB;AACF;;;+CAEI;AAAJ,IACI,yCAAmB,yCAAe,gBAAgB,EAClD,6CAAuB,yCAAe,oBAAoB;AAE9D,SAAS,2BAAK,GAAG,EAAE;IACjB,wDAAwD;IACxD,IAAI,KAAK,MAAM,IAAI;AACrB;AAEA,SAAS,gCAAU,MAAM,EAAE;IACzB,OAAO,OAAO,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK;AACrD;;AAEA,SAAS,gCAAU,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;IACrD,WAAW,2BAAK;IAChB,IAAI,SAAS,KAAK;IAClB,OAAO,EAAE,CAAC,SAAS,WAAY;QAC7B,SAAS,IAAI;IACf;IACA,IAAI,8BAAQ,WAAW,4BAAM;IAC7B,0BAAI,QAAQ;QACV,UAAU;QACV,UAAU;IACZ,GAAG,SAAU,GAAG,EAAE;QAChB,IAAI,KAAK,OAAO,SAAS;QACzB,SAAS,IAAI;QACb;IACF;IACA,IAAI,YAAY,KAAK;IACrB,OAAO,SAAU,GAAG,EAAE;QACpB,IAAI,QAAQ;QACZ,IAAI,WAAW;QACf,YAAY,IAAI,EAAE,wDAAwD;QAE1E,IAAI,gCAAU,SAAS,OAAO,OAAO,KAAK;QAC1C,IAAI,OAAO,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,OAAO;QAC/D,SAAS,OAAO,IAAI,2CAAqB;IAC3C;AACF;AAEA,SAAS,2BAAK,EAAE,EAAE;IAChB;AACF;AAEA,SAAS,2BAAK,IAAI,EAAE,EAAE,EAAE;IACtB,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA,SAAS,kCAAY,OAAO,EAAE;IAC5B,IAAI,CAAC,QAAQ,MAAM,EAAE,OAAO;IAC5B,IAAI,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,KAAK,YAAY,OAAO;IAC9D,OAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,iCAAW;IAClB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,UAAU,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAClF,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAGjC,IAAI,WAAW,kCAAY;IAC3B,IAAI,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG,UAAU,OAAO,CAAC,EAAE;IAEnD,IAAI,QAAQ,MAAM,GAAG,GACnB,MAAM,IAAI,uCAAiB,WAAW;IAGxC,IAAI;IACJ,IAAI,WAAW,QAAQ,GAAG,CAAC,SAAU,MAAM,EAAE,CAAC,EAAE;QAC9C,IAAI,UAAU,IAAI,QAAQ,MAAM,GAAG;QACnC,IAAI,UAAU,IAAI;QAClB,OAAO,gCAAU,QAAQ,SAAS,SAAS,SAAU,GAAG,EAAE;YACxD,IAAI,CAAC,OAAO,QAAQ;YACpB,IAAI,KAAK,SAAS,OAAO,CAAC;YAC1B,IAAI,SAAS;YACb,SAAS,OAAO,CAAC;YACjB,SAAS;QACX;IACF;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAEA,iBAAiB;;;;;;;;ACpFjB;;;;;;;;;;;;;;;;;AAoBA,gGAAA;AACA,MAAMgB,QAAQ,IAAIC;AAClB,MAAMC,WAAW,IAAID,OAErB,iFAFA;AAOO,MAAME;IAMXC,qBAAiD,IAAIH,MAArDG;IAEAC,YACEC,EADS,EAETC,WAFS,EAGTC,SAHS,CAIT;QACA,IAAA,CAAKF,EAAL,GAAUA;QACV,IAAA,CAAKC,WAAL,GAAmBA;QACnB,IAAA,CAAKC,SAAL,GAAiBA;QACjB,IAAA,CAAKC,QAAL,GAAgB,IAAIX,CAAAA,GAAAA,mBAAAA,EAAa,IAAA,CAAKQ,EAAtB,EAA0BC;QAC1C,IAAA,CAAKG,YAAL,GAAoB,IAAIX,CAAAA,GAAAA,uBAAAA,EAAiB,IAAA,CAAKO,EAA1B,EAA8BC;IACnD;IAEiB,OAAXI,YAAYC,IAAD,EAAgC;QAChD,OAAO,IAAIT,mBAAmBS,KAAKN,EAA5B,EAAgCM,KAAKL,WAArC,EAAkDK,KAAKJ,SAAvD;IACR;IAEDK,YAKG;QACD,OAAO;YACLC,OAAO,KADF;YAELR,IAAI,IAAA,CAAKA,EAFJ;YAGLC,aAAa,IAAA,CAAKA,WAHb;YAILC,WAAW,IAAA,CAAKA,SAAhBA;QAJK;IAMR;IAEY,MAAPO,QACJC,IADW,EAEXC,IAFW,EAGXL,IAHW,EAQG;QACd,IAAI,EAACM,SAAAA,EAAD,GAAa,MAAM,IAAA,CAAKC,OAAL,CAAaH,MAAMC,MAAML;QAChD,OAAO,IAAA,CAAKQ,IAAL,CAAUF,UAAUD;IAC5B;IAEDI,YAAYL,IAAD,EAA4BC,IAA5B,EAAiD;QAC1D,IAAI,EAACC,SAAAA,EAAD,GAAa,IAAA,CAAKI,WAAL,CAAiBN,MAAMC;QACxC,OAAO,IAAA,CAAKG,IAAL,CAAUF,UAAUD;IAC5B;IAEDG,KAAKG,QAAD,EAAqBN,IAArB,EAA0C;QAC5C,IAAI,CAACzB,CAAAA,GAAAA,qCAAAA,EAAKgC,UAAL,CAAgBD,WACnB,sBAAA;QACA,aAAA;QACA,OAAOR,QAAQQ;QAJ2B,CAO5C,2BAFC;QAGD,MAAME,eAAelC,CAAAA,GAAAA,uCAAM,AAANA,EAAOmC,MAAP,CAAcH,SAAnC;QACA,IAAIE,iBAAiBE,WACnB,OAAOF,aAAaG,OAApB;QAV0C,CAa5C,aAFC;QAGD,IAAIC,IAAI,IAAItC,CAAAA,GAAAA,uCAAAA,EAAOgC,UAAUhC,CAAAA,GAAAA,uCAAM,AAANA,EAAOmC,MAAP,CAAcT,KAAd,IAAuBa,OAAOC,MAAnD,GACR,2BADA;QAEAxC,CAAAA,GAAAA,uCAAM,AAANA,EAAOmC,MAAP,CAAcH,SAAd,GAA0BM,GAE1B,kEAFAtC;QAGAsC,EAAEd,OAAF,GAAYiB,CAAAA,KAAM;YAChB,OAAO,IAAA,CAAKX,WAAL,CAAiBW,IAAIT;QAC7B,GAED,8EAFC;QAGD,IAAIU,eAAe3C,CAAAA,GAAAA,mCAAQ,AAARA,EAAS2C,YAA5B,EACA,aADA;QAEA3C,CAAAA,GAAAA,mCAAAA,EAAS2C,YAAT,GAAwB,CAACC,UAAUC,WAAa;YAC9C,aAAA;YACA7C,CAAAA,GAAAA,mCAAAA,EAAS2C,YAAT,GAAwBA;YACxB,OAAO,IAAA,CAAK3B,EAAL,CAAQ2B,YAAR,CAAqBC,UAAUC;QACvC;QAED,IAAI;YACFN,EAAET,IAAF,CAAOG;QACR,EAAC,OAAOa,KAAK;YACZ,2BAAA;YACA,OAAO7C,CAAAA,GAAAA,uCAAAA,EAAOmC,MAAP,CAAcH,SAArB;YACA,MAAMa,IAAN;QACD;QAED,OAAOP,EAAED,OAAT;IACD;IAEY,MAAPT,QACJa,EADW,EAEXf,IAFW,EAGXoB,OAHW,EAQa;QACxB,IAAIC,UAAU9C,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAatB;QAC3B,IAAIuB,MAAMF,UAAU,MAAMN;QAC1B,IAAId,WAAWlB,MAAMyC,GAAN,CAAUD;QACzB,IAAI,CAACtB,UAAU;YACb,IAAI,CAACF,KAAD,GAAStB,CAAAA,GAAAA,iCAAAA,EAAesC;YAC5B,IAAI;gBACFd,WAAW,MAAM,IAAA,CAAKT,QAAL,CAAcU,OAAd,CAAsBa,IAAIf;YAC5C,EAAC,OAAOyB,GAAG;gBACV,IACEA,EAAEC,IAAF,KAAW,sBACXN,CAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASO,iBAAT,AAASA,MAAsB,IAFjC,EAGE;oBACA,IACEF,EAAEC,IAAF,KAAW,sBACXN,CAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASO,iBAAT,AAASA,MAAsB,IAFjC,EAGE;wBACA,IAAIR,MAAM,IAAInD,CAAAA,GAAAA,iDAAAA,EAAoB;4BAChC4D,YAAY;gCACVC,SAAS3D,CAAAA,GAAAA,sCAAc,AAAdA,EAAeuD,EAAEI,OAAH;gCACvBC,OAAO;oCACL;iCADFA;4BAFU;wBADoB,IAQlC,2CARkC;wBASlCX,IAAIO,IAAJ,GAAW;wBACX,MAAMP,IAAN;oBACD,OACC,MAAMM,EAAN;gBAEH,CAAA;gBAED,IAAIM,YAAY,MAAMrD,CAAAA,GAAAA,sCAAAA,EACpB,IAAA,CAAKW,EAD8C,EAEnDU,MACAC,MACA,IAAA,CAAKV,WAJ8C;gBAOrD,IAAIyC,aAAa,IAAjB,EAAuB;wBAEVX;oBADX,MAAM,IAAA,CAAKY,OAAL,CAAa;wBAAC;kCAACjC;4BAAMkC,OAAOb,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASa,KAAhBA;wBAAP;qBAAd,EAA8CjC,MAAM;wBACxDkC,SAASd,CAAAA,mBAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASc,OAAT,cAAAd,8BAAAA,mBAAoB,IAA7Bc;oBADwD;oBAI1D,OAAO,IAAA,CAAKhC,OAAL,CAAaa,IAAIf,MAAM;wBAC5B,GAAGoB,OADyB;wBAE5BO,mBAAmB,KAAnBA;oBAF4B;gBAI/B,CAAA;gBAED,MAAM,IAAI3D,CAAAA,GAAAA,iDAAAA,EAAoB;oBAC5B4D,YAAYG,UAAUI,MAAV,CAAiBC,GAAjB,CAAqBC,CAAAA,QAAU,CAAA;4BACzCR,SAASzD,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,uBAAA,EAAyB2B,KAAK,qEAAA,CADD;4BAEzCuC,QAAQ;4BACRC,YAAY;gCACV;oCACEjC,UAAUyB,UAAUzB,QADtB;oCAEEkC,UAAU;oCACVd,MAAMK,UAAUU,IAHlB;oCAIEC,gBAAgBvE,CAAAA,GAAAA,kDAAAA,EAA2B4D,UAAUU,IAAX,EAAiB;wCACzD;4CACElB,KAAM,CAAA,CAAA,EAAGc,MAAM,CAAA,EAAGpE,CAAAA,GAAAA,8CAAAA,EAAuB8B,MAAM,CADjD;4CAEE4C,MAAM;4CACNd,SAAS;wCAHX;qCADwC;gCAJ5C;6BAAA;wBAJuC,CAAA;gBADf,GAAA;YAoB/B;YAED,IAAII,QAAQb,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASa,KAArB;YACA,IAAIA,SAAS,IAAb,EAAmB;gBACjB,IAAIrD,MAAMqB,SAASrB,GAAnB;gBACA,IAAIA,OAAO,IAAP,IAAe,CAACJ,CAAAA,GAAAA,uCAAM,AAANA,EAAOoE,SAAP,CAAiBhE,IAAIiE,OAArB,EAA8BZ,QAAQ;oBACxD,IAAIF,YAAY,MAAMrD,CAAAA,GAAAA,sCAAAA,EACpB,IAAA,CAAKW,EAD8C,EAEnDU,MACAC,MACA,IAAA,CAAKV,WAJ8C;oBAOrD,IAAIyC,aAAa,IAAb,IAAqBX,CAAAA,oBAAAA,qBAAAA,KAAAA,IAAAA,QAASO,iBAAT,AAASA,MAAsB,IAAxD,EAA8D;wBAC5D,MAAM,IAAA,CAAKK,OAAL,CAAa;4BAAC;sCAACjC;uCAAMkC;4BAAP;yBAAd,EAA8BjC;wBACpC,OAAO,IAAA,CAAKE,OAAL,CAAaa,IAAIf,MAAM;4BAC5B,GAAGoB,OADyB;4BAE5BO,mBAAmB,KAAnBA;wBAF4B;oBAI/B,OAAM,IAAII,aAAa,IAAjB,EACL,MAAM,IAAI/D,CAAAA,GAAAA,iDAAJ,AAAIA,EAAoB;wBAC5B4D,YAAY;4BACVC,SAASzD,CAAAA,GAAAA,0BAAG,AAAHA,CAAG,CAAA,uBAAA,EAAyB2B,KAAK,aAAA,EAAekC,MAAM,CAAA,CADrD;4BAEVK,QAAQ;4BACRC,YAAY;gCACV;oCACEjC,UAAUyB,UAAUzB,QADtB;oCAEEkC,UAAU;oCACVd,MAAMK,UAAUU,IAHlB;oCAIEC,gBAAgBvE,CAAAA,GAAAA,kDAA0B,AAA1BA,EACd4D,UAAUU,IAD8B,EAExCV,UAAUI,MAAV,CAAiBC,GAAjB,CAAqBC,CAAAA,QAAU,CAAA;4CAC7Bd,KAAM,CAAA,CAAA,EAAGc,MAAM,CAAA,EAAGpE,CAAAA,GAAAA,8CAAAA,EAAuB8B,MAAM,CADlB;4CAE7B4C,MAAM;4CACNd,SAAS;wCAHoB,CAAA;gCANnC;6BAAA;wBAJQ;oBADgB,GAAA;oBAuBhC,IAAIgB,UAAUjE,gBAAAA,iBAAAA,KAAAA,IAAAA,IAAKiE,OAAnB;oBACA,IAAIhB,UAAUzD,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,2BAAA,EAA6B2B,KAAK,iBAAA,EAAmBkC,MAAM,CAAA,CAA5E;oBACA,IAAIY,WAAW,IAAf,EACEhB,WAAWzD,CAAAA,GAAAA,0BAAG,AAAHA,CAAG,CAAA,OAAA,EAASyE,QAAQ,CAAA,CAA/B;oBAGF,MAAM,IAAI7E,CAAAA,GAAAA,iDAAAA,EAAoB;wBAC5B4D,YAAY;qCACVC;4BACAC,OAAO;gCACL;6BADFA;wBAFU;oBADgB,GAAA;gBAQ/B,CAAA;YACF,CAAA;YAED/C,MAAM+D,GAAN,CAAUvB,KAAKtB;YACf,IAAA,CAAKd,kBAAL,CAAwB4D,KAAxB,IAEA,qDAFA;YAGA,sGAAA;YACA,2HAAA;YACA,IAAI,CAACxE,CAAAA,GAAAA,qCAAI,AAAJA,EAAKgC,UAAL,CAAgBR,OAAO;gBAC1B,IAAIiD,iBAAiB/D,SAASuC,GAAT,CAAaxB;gBAClC,IAAI,CAACgD,gBAAgB;oBACnBA,iBAAiB,IAAIC;oBACrBhE,SAAS6D,GAAT,CAAa9C,MAAMgD;gBACpB,CAAA;gBAEDA,eAAeE,GAAf,CAAmBnD;YACpB,CAAA;QACF,CAAA;QAED,OAAOE;IACR;IAEDI,YAAYN,IAAD,EAA4BC,IAA5B,EAA2D;QACpE,IAAIqB,UAAU9C,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAatB;QAC3B,IAAIuB,MAAMF,UAAU,MAAMtB;QAC1B,IAAIE,WAAWlB,MAAMyC,GAAN,CAAUD;QACzB,IAAI,CAACtB,UAAU;YACbA,WAAW,IAAA,CAAKR,YAAL,CAAkBS,OAAlB,CAA0BH,MAAMC;YAC3CjB,MAAM+D,GAAN,CAAUvB,KAAKtB;YACf,IAAA,CAAKd,kBAAL,CAAwB4D,KAAxB;YAEA,IAAI,CAACxE,CAAAA,GAAAA,qCAAI,AAAJA,EAAKgC,UAAL,CAAgBR,OAAO;gBAC1B,IAAIiD,iBAAiB/D,SAASuC,GAAT,CAAaxB;gBAClC,IAAI,CAACgD,gBAAgB;oBACnBA,iBAAiB,IAAIC;oBACrBhE,SAAS6D,GAAT,CAAa9C,MAAMgD;gBACpB,CAAA;gBAEDA,eAAeE,GAAf,CAAmBnD;YACpB,CAAA;QACF,CAAA;QAED,OAAOE;IACR;IAEY,MAAP+B,QACJmB,OADW,EAEXnD,IAFW,EAGXL,IAHW,EAIX;QACA,MAAMhB,CAAAA,GAAAA,qBAAc,AAAdA,EAAe,IAAA,CAAKU,EAAN,EAAU,IAAV,EAAgB8D,SAASnD,MAAM,IAAA,CAAKV,WAApC,EAAiD;YACnE8D,kBAAkB,IAAA,CAAK7D,SAD4C;YAEnE,GAAGI,IAAH;QAFmE;IAItE;IAED0D,iBAAiBtD,IAAD,EAA4BC,IAA5B,EAA2D;QACzE,IAAIuB,MAAMxB,OAAO,MAAMC;QACvB,IAAIsD,SAAS,IAAA,CAAKnE,kBAAL,CAAwBqC,GAAxB,CAA4BD;QACzC,IAAI+B,UAAU,IAAd,EACE,OAAOA;QAGT,IAAIC,MAAM;YACRC,wBAAwB,EADhB;YAERC,wBAAwB,IAAIR;QAFpB;QAKV,IAAIS,OAAO,IAAIT;QACf,IAAIU,SAAS,CAAC5D,MAAMC,OAAS;YAC3B,IAAIqB,UAAU9C,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAatB;YAC3B,IAAIuB,MAAMF,UAAU,MAAMtB;YAC1B,IAAI2D,KAAKE,GAAL,CAASrC,MACX;YAGFmC,KAAKR,GAAL,CAAS3B;YACT,IAAItB,WAAWlB,MAAMyC,GAAN,CAAUD;YACzB,IAAI,CAACtB,YAAY,CAAC1B,CAAAA,GAAAA,qCAAI,AAAJA,EAAKgC,UAAL,CAAgBN,SAASA,QAAzB,GAChB;YAGFsD,IAAIC,sBAAJ,CAA2BK,IAA3B,IAAmC5D,SAASuD,sBAA5C;YACAD,IAAIE,sBAAJ,CAA2BP,GAA3B,CAA+BjD,SAASA,QAAxC;YAEA,KAAK,IAAI6D,QAAQ7D,SAASwD,sBAA1B,CACEF,IAAIE,sBAAJ,CAA2BP,GAA3B,CAA+BY;YAGjC,IAAId,iBAAiB/D,SAASuC,GAAT,CAAavB,SAASA,QAAtB;YACrB,IAAI+C,gBACF,KAAK,IAAIe,aAAaf,eACpBW,OAAOI,WAAW9D,SAASA,QAArB;QAGX;QAED0D,OAAO5D,MAAMC;QACb,IAAA,CAAKb,kBAAL,CAAwB2D,GAAxB,CAA4BvB,KAAKgC;QACjC,OAAOA;IACR;IAEDS,WAAWjE,IAAD,EAA4BC,IAA5B,EAA4C;QACpD,IAAI0D,OAAO,IAAIT;QAEf,IAAIe,aAAa,CAACjE,MAAMC,OAAS;YAC/B,IAAIqB,UAAU9C,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAatB;YAC3B,IAAIuB,MAAMF,UAAU,MAAMtB;YAC1B,IAAI2D,KAAKE,GAAL,CAASrC,MACX;YAGFmC,KAAKR,GAAL,CAAS3B;YACT,IAAItB,WAAWlB,MAAMyC,GAAN,CAAUD;YACzB,IAAI,CAACtB,YAAY,CAAC1B,CAAAA,GAAAA,qCAAI,AAAJA,EAAKgC,UAAL,CAAgBN,SAASA,QAAzB,GAChB;YAV6B,CAa/B,aAFC;YAGD,IAAIY,UAASvC,CAAAA,GAAAA,uCAAAA,EAAOmC,MAAP,CAAcR,SAASA,QAAvB,CAAb;YACA,IAAIY,SACF,aAAA;YACA,OAAOvC,CAAAA,GAAAA,uCAAAA,EAAOmC,MAAP,CAAcR,SAASA,QAAvB,CAAP;YAGF,IAAI+C,iBAAiB/D,SAASuC,GAAT,CAAavB,SAASA,QAAtB;YACrB,IAAI+C,gBACF,KAAK,IAAIe,aAAaf,eACpBgB,WAAWD,WAAW9D,SAASA,QAArB;YAIdhB,SAASgF,MAAT,CAAgBhE,SAASA,QAAzB;YACAlB,MAAMkF,MAAN,CAAa1C;YACb,IAAA,CAAK/B,QAAL,CAAcwE,UAAd,CAAyB/D,SAASA,QAAlC;YACA,IAAA,CAAKR,YAAL,CAAkBuE,UAAlB,CAA6B/D,SAASA,QAAtC;QACD;QAED+D,WAAWjE,MAAMC;IAClB;AAvXuD;AA0X1DjC,CAAAA,GAAAA,2CAAAA,EACG,CAAA,EAAEa,CAAAA,GAAAA,6CAAAA,EAAIiE,OAAQ,CAAA,mBAAA,CADQ,EAEvB3D;;;;;;;;AC/ZF;;;AAIO,SAASkF,0CACdC,aADK,EAEG;IACR,OAAOA,cAAcpC,KAAd,IAAuB,IAAvB,GACH;QAACoC,cAActE,IAAf;QAAqBsE,cAAcpC,KAAnC;KAAA,CAA0CqC,IAA1C,CAA+C,OAC/CD,cAActE,IAFlB;AAGD;AAEM,SAASwE,0CAAgCC,aAAzC,EAEmB;IACxB,OAAOC,OAAOC,OAAP,CAAeF,eAAepC,GAA9B,CAAkC,CAAC,CAACrC,MAAMkC,MAAR,GAAmB;QAC1DiC,CAAAA,GAAAA,uCAAAA,EAAU,OAAOjC,UAAU;QAC3B,OAAO;kBACLlC;mBACAkC;QAFK;IAIR;AACF;AAEM,eAAevD,0CACpBW,EADK,EAELU,IAFK,EAGL4E,KAHK,EAILrF,WAJK,EAKkE;IACvE,IAAIsF,UAAU,MAAMT,CAAAA,GAAAA,gCAAAA,EAAc9E,IAAIsF,OAAO;QAAC;KAAb,EAA8BrF;IAC/D,IAAIsF,WAAW,IAAf,EACE;IAGF,IAAIC,SAAS,MAAMxF,GAAGyF,QAAH,CAAYF,SAAS;IACxC,IAAIhG;IACJ,IAAI;QACFA,MAAMmG,KAAKC,KAAL,CAAWH;IAClB,EAAC,OAAOpD,GAAG;QACV,kBAAA;QACA,MAAM,IAAIzD,CAAAA,GAAAA,iDAAAA,EAAoB;YAC5B4D,YAAY;gBACVC,SAAS;gBACTS,QAAQ;YAFE;QADgB,GAAA;IAM/B;IAED,IAAI,OAAO1D,QAAQ,YAAYA,OAAO,IAAtC,EACE,kBAAA;IACA,MAAM,IAAIZ,CAAAA,GAAAA,iDAAAA,EAAoB;QAC5B4D,YAAY;YACVC,SAAS;YACTS,QAAQ;QAFE;IADgB,GAAA;IAQhC,IAAIH,SAAS,EAAb;IACA,KAAK,IAAIE,SAAS;QAAC;QAAgB;QAAmB;KAAtD,CACE,IACE,OAAOzD,GAAG,CAACyD,MAAX,KAAsB,YACtBzD,GAAG,CAACyD,MAAJ,IAAc,IADd,IAEAzD,GAAG,CAACyD,MAAJ,CAAWtC,KAAX,IAAoB,IAHtB,EAKEoC,OAAO0B,IAAP,CAAYxB;IAIhB,IAAIF,OAAO8C,MAAP,GAAgB,GAClB,OAAO;QACL3E,UAAUsE;QACVnC,MAAMoC;gBACN1C;IAHK;AAMV;;;;;;;;ACxED;;;;;;;;;;;;;;;;;;;;;;;AAmBA,eAAeH,8BACb3C,EADF,EAEEsG,cAFF,EAGExC,OAHF,EAIEnD,IAJF,EAKEV,WALF,EAME8B,UAA0B,CAAA,CAN5B,EAOiB;IACf,IAAI,gBAACwE,eAAe,IAAhB,YAAsB1D,UAAU,IAAhC,qBAAsCkB,iBAAAA,EAAtC,GAA0DhC;IAC9D,IAAIyE,cAAc1C,QAAQf,GAAR,CAAYxB,CAAAA,IAAKA,EAAEb,IAAnB,EAAyBuE,IAAzB,CAA8B;IAEhDa,CAAAA,GAAAA,6CAAM,AAANA,EAAOW,QAAP,CAAiB,CAAA,WAAA,EAAaD,YAAY,GAAA,CAA1C;IAEA,IAAIE,cAAc,MAAM5B,CAAAA,GAAAA,gCAAAA,EACtB9E,IACAW,MACA;QAAC;KAHkC,EAInCV;IAEF,IAAI0G,MAAMD,cAAcxH,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAL,CAAayE,eAAe1G,GAAG2G,GAAH,EAApD;IAEA,IAAI,CAAC5C,kBACHA,mBAAmB,MAAM6C,gDAA0B5G,IAAIW,MAAMV;IAG/D,IAAI;QACF,MAAM8D,iBAAiBpB,OAAjB,CAAyB;qBAC7BmB;qBACAjB;iBACA8D;YACAE,aAAaH;gBACb1G;QAL6B;IAOhC,EAAC,OAAO8B,KAAK;QACZ,MAAM,IAAIgF,MAAO,CAAA,kBAAA,EAAoBN,YAAY,EAAA,EAAI1E,IAAIU,OAAQ,CAAA,CAA3D,EAAN;IACD;IAED,IAAI+D,cACF,MAAMQ,QAAQC,GAAR,CACJlD,QAAQf,GAAR,CAAYxB,CAAAA,IACV0F,8CACEjH,IACAsG,gBACA/E,GACAZ,MACAV,aACA8B;AAKT;AAED,eAAekF,8CACbjH,EADF,EAEEsG,cAFF,EAGE9E,MAHF,EAIEb,IAJF,EAKEV,WALF,EAME8B,OANF,EAOE;IACA,MAAM,YAACnB,SAAAA,EAAD,GAAa,MAAM0F,eAAezF,OAAf,CAAuBW,OAAOd,IAA9B,EAAoCC;IAC7D,MAAMuG,YAAyBrB,CAAAA,GAAAA,6CAAAA,EAC7B,MAAME,CAAAA,GAAAA,6BAAAA,EAAW/F,IAAIY,UAAU;QAAC;KAAhB,EAAiCX,cACjDkH,MAFF;IAGA,MAAMC,QAAQF,UAAUG,gBAAV,IAA8B,CAAA;IAE5C,IAAIvD,UAAgC,EAApC;IACA,KAAK,IAAI,CAACpD,MAAMkC,MAAhB,IAA0BwC,OAAOC,OAAP,CAAe+B,OAAQ;QAC/CvC,CAAAA,GAAAA,uCAAAA,EAAU,OAAOjC,UAAU;QAE3B,IAAIF,YAAY,MAAMrD,CAAAA,GAAAA,sCAA+B,AAA/BA,EACpBW,IACAU,MACAC,MACAV;QAEF,IAAIyC,WAAW;YACb,IAAI,OAACnD,IAAAA,EAAD,GAAQ,MAAM+G,eAAezF,OAAf,CAAuBH,MAAMC;YAC/CkE,CAAAA,GAAAA,uCAAS,AAATA,EAAUtF;YACV,IAAI,CAACJ,CAAAA,GAAAA,uCAAM,AAANA,EAAOoE,SAAP,CAAiBhE,IAAIiE,OAArB,EAA8BZ,QACjC,MAAM,IAAIjE,CAAAA,GAAAA,iDAAAA,EAAoB;gBAC5B4D,YAAY;oBACVC,SAASzD,CAAAA,GAAAA,0BAAAA,CAAG,CAAA,uCAAA,EAAyC2B,KAAK,OAAA,EAASc,OAAOd,IAAK,CAAA,qBAAA,EAAuBnB,IAAIiE,OAAQ,CAAA,sBAAA,EAAwBZ,MAAM,CADtI;oBAEVK,QAAQ;oBACRC,YAAY;wBACV;4BACEjC,UAAUyB,UAAUzB,QADtB;4BAEEkC,UAAU;4BACVd,MAAMK,UAAUU,IAHlB;4BAIEC,gBAAgBvE,CAAAA,GAAAA,kDAA0B,AAA1BA,EACd4D,UAAUU,IAD8B,EAExCV,UAAUI,MAAV,CAAiBC,GAAjB,CAAqBC,CAAAA,QAAU,CAAA;oCAC7Bd,KAAM,CAAA,CAAA,EAAGc,MAAM,CAAA,EAAGpE,CAAAA,GAAAA,8CAAAA,EAAuB8B,MAAM,CADlB;oCAE7B4C,MAAM;oCACNd,SAAS;gCAHoB,CAAA;wBANnC;qBAAA;gBAJQ;YADgB,GAAA;YAuBhC,QAAA;QACD,CAAA;QACDsB,QAAQU,IAAR,CAAa;kBAAC9D;mBAAMkC;QAAP;IACd;IAED,IAAIkB,QAAQ8B,MAAZ,EACE,MAAMjD,8BACJ3C,IACAsG,gBACAxC,SACAnD,MACAV,aACAmF,OAAOkC,MAAP,CAAc,CAAA,GAAIvF,SAAS;QAACwE,cAAc,KAAdA;IAAD;AAGhC;AAED,eAAeK,gDACb5G,EADF,EAEEuH,QAFF,EAGEtH,WAHF,EAI6B;IAC3B,IAAIuH,aAAa,MAAM1C,CAAAA,GAAAA,gCAAAA,EACrB9E,IACAuH,UACA;QAAC;QAAqB;QAAkB;KAHN,EAIlCtH;IAGF,IAAIwH,aAAaD,cAActI,CAAAA,GAAAA,qCAAI,AAAJA,EAAKwI,QAAL,CAAcF,aAE7C,uEAFA;IAGA,yEAAA;IACA,IAAIC,eAAe,qBACjB,OAAO,IAAIvB,CAAAA,GAAAA,UAAJ,AAAIA;SACN,IAAIuB,eAAe,kBACxB,OAAO,IAAIrB,CAAAA,GAAAA,WAAJ,AAAIA;SACN,IAAIqB,eAAe,aACxB,OAAO,IAAItB,CAAAA,GAAAA,WAAJ,AAAIA;IAGb,IAAI,MAAMA,CAAAA,GAAAA,WAAAA,EAAKwB,MAAL,IACR,OAAO,IAAIxB,CAAAA,GAAAA,WAAJ,AAAIA;SACN,IAAI,MAAMC,CAAAA,GAAAA,WAAAA,EAAKuB,MAAL,IACf,OAAO,IAAIvB,CAAAA,GAAAA,WAAJ,AAAIA;SAEX,OAAO,IAAIF,CAAAA,GAAAA,UAAJ,AAAIA;AAEd;AAED,IAAI0B,8BAAQ,IAAI5B,CAAAA,GAAAA,+BAAAA,EAAa;IAAC6B,eAAe;AAAhB;AAC7B,IAAIC,0CAAiC,IAAIlE,OAEzC,gEAFA;AAKO,SAASmE,0CACd/H,EADK,EAELsG,cAFK,EAGLxC,OAHK,EAIL7C,QAJK,EAKLhB,WALK,EAML8B,OANK,EAOW;IAChB+B,UAAUA,QAAQf,GAAR,CAAYiF,CAAAA,UAAY,CAAA;YAChCtH,MAAM2F,CAAAA,GAAAA,cAAuB,AAAvBA,EAAwB2B,QAAQtH,IAAT;YAC7BkC,OAAOoF,QAAQpF,KAAfA;QAFgC,CAAA,IAKlC,qEALkC;IAMlC,gFAAA;IACA,gBAAA;IACA,IAAIqF,mBAAmBnE,QAAQoE,MAAR,CACrB3G,CAAAA,IAAK,CAACuG,wCAAkBvD,GAAlB,CAAsB4D,0CAAoB5G;IAElD,IAAI0G,iBAAiBrC,MAArB,EAA6B;QAC3B,KAAK,IAAIrE,KAAK0G,iBACZH,wCAAkBjE,GAAlB,CAAsBsE,0CAAoB5G;QAG5CqG,4BACG/D,GADH,CACO,IACHlB,8BACE3C,IACAsG,gBACA2B,kBACAhH,UACAhB,aACA8B,SACAqG,IAPF,CAOO,IAAM;gBACX,KAAK,IAAI7G,KAAK0G,iBACZH,wCAAkBlD,MAAlB,CAAyBuD,0CAAoB5G;YAEhD,IAEF6G,IAfH,CAgBI,IAAM,CAAE,GACR,IAAM,CAAE;IAEb,CAAA;IAED,OAAOR,4BAAMS,GAAN;AACR;AAEM,SAAS/I,0CACdU,EADK,EAELsG,cAFK,EAGLxC,OAHK,EAIL7C,QAJK,EAKLhB,WALK,EAML8B,OANK,EAOW;IAChB,IAAIkE,CAAAA,GAAAA,8CAAU,AAAVA,EAAWqC,QAAX,IAAuB;QACzB,IAAIC,YAAYtC,CAAAA,GAAAA,8CAAU,AAAVA,EAAWuC,YAAX,IAChB,qEADA;QAEA,IAAIC,aACF,AACA,CAACC,QAAQC,GAAR,CAAYC,iBADb,GAEI1J,CAAAA,GAAAA,qCAAAA,EAAK+F,IAAL,CAAU4D,yCAAW,MAAM,kBAC3BC,wCAJN;QAKA,OAAOP,UAAUQ,UAAV,CAAqB;YAC1BC,UAAUP;YACVQ,MAAM;gBAACjJ;gBAAIsG;gBAAgBxC;gBAAS7C;gBAAUhB;gBAAa8B;aAFjC;YAG1BmH,QAAQ;QAHkB;IAK7B,CAAA;IAED,OAAOnB,0CACL/H,IACAsG,gBACAxC,SACA7C,UACAhB,aACA8B;AAEH;AAED,SAASoG,0CAAoBnD,aAA7B,EAAmE;IACjE,OAAO;QAACA,cAActE,IAAf;QAAqBsE,cAAcpC,KAAnC;KAAA,CAA0CqC,IAA1C,CAA+C;AACvD;;;;ACjRD;AAEA,SAAS,iCAAW,CAAC,EAAE,OAAO,EAAE;IAC9B,IAAI,KAAK,IAAI,EACX,OAAO;IAET,IAAI,QAAQ,IAAI,MAAM,YAAY,YAAY,UAAU,oBAAoB,CAAC;IAC7E,MAAM,WAAW,GAAG,GAAG,qCAAqC;IAC5D,MAAM,MAAM;AACd;AAEA,iBAAiB;AACjB,eAAe,OAAO,GAAG;AAEzB,OAAO,cAAc,CAAC,gBAAgB,cAAc;IAAC,OAAO,IAAI;AAAA;;;;;;;ACVhE;;;;;;;;;;;AAUA,MAAMoE,gCAAU;AAET,MAAMnD;IACE,MAAPvD,QAAQ,WACZmB,QADY,OAEZ6C,IAFY,MAGZ3G,GAHY,eAIZ6G,YAJY,WAKZhE,UAAU,IAAVA,GALW,EAMuB;QAClC,0DAAA;QACA,oCAAA;QACA,IAAIgE,eAAe,IAAnB,EACE,MAAM7G,GAAGsJ,SAAH,CAAapK,CAAAA,GAAAA,qCAAAA,EAAK+F,IAAL,CAAU0B,KAAK,iBAAiB;QAGrD,IAAIsC,OAAO;YAAC;YAAW;YAAUpG,UAAU,eAAe,QAA/C;SAAA,CAAyD0G,MAAzD,CACTzF,QAAQf,GAAR,CAAYgC,CAAAA,GAAAA,oCAAAA,KAGd,+FAJA;QAKA,4FAAA;QACA,4CAAA;QACA,IAAI4D,MAAM,CAAA;QACV,IAAK,IAAIzG,OAAOwG,QAAQC,GAAxB,CACE,IAAI,CAACzG,IAAIsH,UAAJ,CAAe,WAAWtH,QAAQ,cAAcA,QAAQ,YAC3DyG,GAAG,CAACzG,IAAJ,GAAWwG,QAAQC,GAAR,CAAYzG,IAAvB;QAIJ,IAAIuH,iBAAiBN,CAAAA,GAAAA,6CAAK,AAALA,EAAME,+BAASJ,MAAM;iBAACtC;iBAAKgC;QAAN;QAC1C,IAAIe,SAAS;QACbD,eAAeC,MAAf,CAAsBC,EAAtB,CAAyB,QAASC,CAAAA,MAAgB;YAChDF,UAAUE,IAAIC,QAAJ;QACX;QAED,IAAIC,SAAS,EAAb;QACAL,eAAeK,MAAf,CAAsBH,EAAtB,CAAyB,QAASC,CAAAA,MAAgB;YAChDE,OAAOtF,IAAP,CAAYoF,IAAIC,QAAJ,GAAeE,IAAf;QACb;QAED,IAAI;YACF,MAAMX,CAAAA,GAAAA,cAAAA,EAAmBK;YAEzB,IAAIO,UAAsBtE,KAAKC,KAAL,CAAW+D;YACrC,IAAIO,aAAaD,QAAQE,KAAR,CAActE,MAA/B;YACA,IAAIqE,aAAa,GACfnE,CAAAA,GAAAA,6CAAAA,EAAOqE,GAAP,CAAW;gBACTlH,QAAQ;gBACRT,SAAU,CAAA,MAAA,EAAQyH,WAAlBzH,iBAAAA,CAAAA;YAFS;YANX,CAYF,wEAFC;YAGD,yEAAA;YACA,+BAAA;YACA,KAAK,IAAIA,WAAWsH,OAClB,IAAItH,QAAQoD,MAAR,GAAiB,GACnBE,CAAAA,GAAAA,6CAAAA,EAAOqE,GAAP,CAAW;gBACTlH,QAAQ;yBACRT;YAFS;QAMhB,EAAC,OAAOJ,GAAG;YACV,MAAM,IAAI0E,MACR,oCACE1E,EAAEI,OADJ,GAEE,QACAsH,OAAO7E,IAAP,CAAY,OAJhB;QAMD;IACF;AAtE0C;AA6E7CvG,CAAAA,GAAAA,2CAAAA,EAA2B,CAAA,EAAEa,CAAAA,GAAAA,6CAAAA,EAAIiE,OAAQ,CAAA,IAAA,CAAhB,EAAuB0C;;;;AC7FhD;;;;;;AAMA,SAAS,4BAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;IACnC,sBAAsB;IACtB,MAAM,SAAS,OAAM,SAAS,MAAM;IAEpC,0BAA0B;IAC1B,MAAM,UAAU,2BAAS,OAAO,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO;IAEpE,uEAAuE;IACvE,mFAAmF;IACnF,OAAO,gBAAgB,CAAC,SAAS;IAEjC,OAAO;AACX;AAEA,SAAS,gCAAU,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;IACvC,sBAAsB;IACtB,MAAM,SAAS,OAAM,SAAS,MAAM;IAEpC,0BAA0B;IAC1B,MAAM,SAAS,+BAAa,OAAO,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO;IAEvE,yGAAyG;IACzG,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI,OAAO,gBAAgB,CAAC,OAAO,MAAM,EAAE;IAEtE,OAAO;AACX;AAEA,iBAAiB;AACjB,eAAe,KAAK,GAAG;AACvB,eAAe,IAAI,GAAG;AAEtB,eAAe,MAAM,GAAG;AACxB,eAAe,OAAO,GAAG;;;;ACtCzB;;;;;;;;;;;AASA,MAAM,8BAAQ,QAAQ,QAAQ,KAAK;AACnC,MAAM,2CAAqB;AAC3B,MAAM,wCAAkB;AAExB,mEAAmE;AACnE,MAAM,4CAAsB,OAAQ,IAAM,wBAAiB,QAAQ,OAAO,EAAE,gCAAgC,IAAI,MAAM,KAAK;AAE3H,SAAS,oCAAc,MAAM,EAAE;IAC3B,OAAO,IAAI,GAAG,OAAe;IAE7B,MAAM,UAAU,OAAO,IAAI,IAAI,OAAY,OAAO,IAAI;IAEtD,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI;QAC/B,OAAO,OAAO,GAAG;QAEjB,OAAO,OAAe;IAC1B,CAAC;IAED,OAAO,OAAO,IAAI;AACtB;AAEA,SAAS,oCAAc,MAAM,EAAE;IAC3B,IAAI,CAAC,6BACD,OAAO;IAGX,oCAAoC;IACpC,MAAM,cAAc,oCAAc;IAElC,iEAAiE;IACjE,MAAM,aAAa,CAAC,yCAAmB,IAAI,CAAC;IAE5C,qFAAqF;IACrF,gEAAgE;IAChE,IAAI,OAAO,OAAO,CAAC,UAAU,IAAI,YAAY;QACzC,gGAAgG;QAChG,4FAA4F;QAC5F,4FAA4F;QAC5F,gCAAgC;QAChC,MAAM,6BAA6B,sCAAgB,IAAI,CAAC;QAExD,4EAA4E;QAC5E,6EAA6E;QAC7E,OAAO,OAAO,GAAG,sBAAe,OAAO,OAAO;QAE9C,6BAA6B;QAC7B,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO;QAC9C,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAQ,gBAAgB,KAAK;QAE5D,MAAM,eAAe;YAAC,OAAO,OAAO;SAAC,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC;QAE/D,OAAO,IAAI,GAAG;YAAC;YAAM;YAAM;YAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;SAAC;QACrD,OAAO,OAAO,GAAG,QAAQ,GAAG,CAAC,OAAO,IAAI;QACxC,OAAO,OAAO,CAAC,wBAAwB,GAAG,IAAI,EAAE,2DAA2D;IAC/G,CAAC;IAED,OAAO;AACX;AAEA,SAAS,iCAAW,MAAM,EAAE;IACxB,2EAA2E;IAC3E,IAAI,2CACA,OAAO;IAGX,0BAA0B;IAC1B,6GAA6G;IAC7G,MAAM,eAAe;QAAC,OAAO,OAAO;KAAC,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC;IAE/D,IAAI,6BAAO;QACP,OAAO,OAAO,GAAG,OAAO,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC,OAAO,IAAI,SAAS;QACnH,OAAO,IAAI,GAAG;YAAC;YAAM;YAAM;YAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;SAAC;QACrD,OAAO,OAAO,CAAC,wBAAwB,GAAG,IAAI,EAAE,2DAA2D;IAC/G,OAAO;QACH,IAAI,OAAO,OAAO,OAAO,CAAC,KAAK,KAAK,UAChC,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,KAAK;aAClC,IAAI,QAAQ,QAAQ,KAAK,WAC5B,OAAO,OAAO,GAAG;aAEjB,OAAO,OAAO,GAAG;QAGrB,OAAO,IAAI,GAAG;YAAC;YAAM;SAAa;IACtC,CAAC;IAED,OAAO;AACX;AAEA,SAAS,4BAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;IACnC,yCAAyC;IACzC,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;QAC9B,UAAU;QACV,OAAO,IAAI;IACf,CAAC;IAED,OAAO,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE,EAAE,6CAA6C;IAC/E,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,8CAA8C;IAEpF,0BAA0B;IAC1B,MAAM,SAAS;iBACX;cACA;iBACA;QACA,MAAM;QACN,UAAU;qBACN;kBACA;QACJ;IACJ;IAEA,iDAAiD;IACjD,OAAO,QAAQ,KAAK,GAAG,iCAAW,UAAU,oCAAc,OAAO;AACrE;AAEA,iBAAiB;;;;AC5HjB;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,EAAE,EAAE;IAE7B,IAAI;QAAE,OAAO;IAAK,EAAE,OAAO,GAAG,CAAC;AAEhC;;;;;ACXA;;;;;AAIA,MAAM,gCAAU;AAEhB,SAAS,4CAAsB,MAAM,EAAE,cAAc,EAAE;IACnD,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,eAAe,OAAO,OAAO,CAAC,GAAG,IAAI,IAAI;IAE/C,qEAAqE;IACrE,uEAAuE;IACvE,IAAI,cACA,IAAI;QACA,QAAQ,KAAK,CAAC,OAAO,OAAO,CAAC,GAAG;IACpC,EAAE,OAAO,KAAK;IACV,SAAS,GACb;IAGJ,IAAI;IAEJ,IAAI;QACA,WAAW,YAAW,OAAO,OAAO,EAAE;YAClC,MAAM,AAAC,CAAA,OAAO,OAAO,CAAC,GAAG,IAAI,QAAQ,GAAG,AAAD,CAAE,CAAC,8BAAQ;YAClD,SAAS,iBAAiB,wBAAiB,SAAS;QACxD;IACJ,EAAE,OAAO,GAAG;IACR,SAAS,GACb,SAAU;QACN,QAAQ,KAAK,CAAC;IAClB;IAEA,wEAAwE;IACxE,6FAA6F;IAC7F,IAAI,UACA,WAAW,oBAAa,eAAe,OAAO,OAAO,CAAC,GAAG,GAAG,EAAE,EAAE;IAGpE,OAAO;AACX;AAEA,SAAS,qCAAe,MAAM,EAAE;IAC5B,OAAO,4CAAsB,WAAW,4CAAsB,QAAQ,IAAI;AAC9E;AAEA,iBAAiB;;;;AC9CjB,iBAAiB;AACjB,4BAAM,IAAI,GAAG;AAEb,IAAI,kCAAY,QAAQ,QAAQ,KAAK,WACjC,QAAQ,GAAG,CAAC,MAAM,KAAK,YACvB,QAAQ,GAAG,CAAC,MAAM,KAAK;;AAG3B,IAAI,8BAAQ,kCAAY,MAAM,GAAG;;;AAGjC,SAAS,uCAAkB,GAAG,EAAE;IAC9B,IAAI,KAAK,IAAI,MAAM,gBAAgB;IACnC,GAAG,IAAI,GAAG;IAEV,OAAO;AACT;AAEA,SAAS,kCAAa,GAAG,EAAE,GAAG,EAAE;IAC9B,IAAI,QAAQ,IAAI,KAAK,IAAI;IACzB,IAAI,UAAU,IAAI,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC9C,IAAI,UAAU;QAAC;KAAG;IAElB,UAAU,QAAQ,KAAK,CAAC;IAExB,IAAI,aAAa;IACjB,IAAI,iCAAW;QACb,QAAQ,OAAO,CAAC,QAAQ,GAAG;QAC3B,aAAc,IAAI,OAAO,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI;QACpD,UAAU,WAAW,KAAK,CAAC;QAG3B,mEAAmE;QACnE,iCAAiC;QACjC,IAAI,IAAI,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,EAAE,KAAK,IAC5C,QAAQ,OAAO,CAAC;IACpB,CAAC;IAED,iEAAiE;IACjE,6CAA6C;IAC7C,IAAI,IAAI,KAAK,CAAC,SAAS,mCAAa,IAAI,KAAK,CAAC,OAC5C,UAAU;QAAC;KAAG;IAEhB,OAAO;QACL,KAAK;QACL,KAAK;QACL,QAAQ;IACV;AACF;AAEA,SAAS,4BAAO,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE;IAC5B,IAAI,OAAO,QAAQ,YAAY;QAC7B,KAAK;QACL,MAAM,CAAC;IACT,CAAC;IAED,IAAI,OAAO,kCAAY,KAAK;IAC5B,IAAI,UAAU,KAAK,GAAG;IACtB,IAAI,UAAU,KAAK,GAAG;IACtB,IAAI,aAAa,KAAK,MAAM;IAC5B,IAAI,QAAQ,EAAE;IAEZ,CAAA,SAAS,EAAG,CAAC,EAAE,CAAC,EAAE;QAClB,IAAI,MAAM,GAAG;YACX,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EACzB,OAAO,GAAG,IAAI,EAAE;iBAEhB,OAAO,GAAG,uCAAiB;QAC/B,CAAC;QAED,IAAI,WAAW,OAAO,CAAC,EAAE;QACzB,IAAI,SAAS,MAAM,CAAC,OAAO,OAAO,SAAS,KAAK,CAAC,QAAQ,KACvD,WAAW,SAAS,KAAK,CAAC,GAAG;QAE/B,IAAI,IAAI,iBAAU,UAAU;QAC5B,IAAI,CAAC,YAAY,AAAC,YAAa,IAAI,CAAC,MAClC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK;QAEtB,CAAA,SAAS,EAAG,EAAE,EAAE,EAAE,EAAE;YACpB,IAAI,OAAO,IAAI,OAAO,EAAE,IAAI,GAAG;YAC/B,IAAI,MAAM,OAAO,CAAC,GAAG;YACrB,OAAM,IAAI,KAAK;gBAAE,SAAS;YAAW,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE;gBACxD,IAAI,CAAC,MAAM,IAAI;oBACb,IAAI,IAAI,GAAG,EACT,MAAM,IAAI,CAAC,IAAI;yBAEf,OAAO,GAAG,IAAI,EAAE,IAAI;gBACxB,CAAC;gBACD,OAAO,EAAE,KAAK,GAAG;YACnB;QACF,CAAA,EAAG,GAAG,QAAQ,MAAM;IACtB,CAAA,EAAG,GAAG,QAAQ,MAAM;AACtB;AAEA,SAAS,gCAAW,GAAG,EAAE,GAAG,EAAE;IAC5B,MAAM,OAAO,CAAC;IAEd,IAAI,OAAO,kCAAY,KAAK;IAC5B,IAAI,UAAU,KAAK,GAAG;IACtB,IAAI,UAAU,KAAK,GAAG;IACtB,IAAI,aAAa,KAAK,MAAM;IAC5B,IAAI,QAAQ,EAAE;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,GAAG,IAAM;QAC/C,IAAI,WAAW,OAAO,CAAC,EAAE;QACzB,IAAI,SAAS,MAAM,CAAC,OAAO,OAAO,SAAS,KAAK,CAAC,QAAQ,KACvD,WAAW,SAAS,KAAK,CAAC,GAAG;QAE/B,IAAI,IAAI,iBAAU,UAAU;QAC5B,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,MAChC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK;QAExB,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,MAAM,EAAE,IAAI,IAAI,IAAM;YACjD,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE;YACxB,IAAI;YACJ,IAAI;gBACF,KAAK,OAAM,IAAI,CAAC,KAAK;oBAAE,SAAS;gBAAW;gBAC3C,IAAI,IAAI;oBACN,IAAI,IAAI,GAAG,EACT,MAAM,IAAI,CAAC;yBAEX,OAAO;gBACX,CAAC;YACH,EAAE,OAAO,IAAI,CAAC;QAChB;IACF;IAEA,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EACzB,OAAO;IAET,IAAI,IAAI,OAAO,EACb,OAAO,IAAI;IAEb,MAAM,uCAAiB,KAAI;AAC7B;;;;ACtIA;AACA,IAAI;;;AACJ,IAAI,QAAQ,QAAQ,KAAK,WAAW,eAAO,eAAe,EACxD,6BAAO;KAEP,6BAAO;AAGT,iBAAiB;AACjB,4BAAM,IAAI,GAAG;AAEb,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;IACjC,IAAI,OAAO,YAAY,YAAY;QACjC,KAAK;QACL,UAAU,CAAC;IACb,CAAC;IAED,IAAI,CAAC,IAAI;QACP,IAAI,OAAO,YAAY,YACrB,MAAM,IAAI,UAAU,yBAAwB;QAG9C,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM,EAAE;YAC5C,4BAAM,MAAM,WAAW,CAAC,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE;gBAC3C,IAAI,IACF,OAAO;qBAEP,QAAQ;YAEZ;QACF;IACF,CAAC;IAED,2BAAK,MAAM,WAAW,CAAC,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE;QAC1C,oEAAoE;QACpE,IAAI,IACF;YAAA,IAAI,GAAG,IAAI,KAAK,YAAY,WAAW,QAAQ,YAAY,EAAE;gBAC3D,KAAK,IAAI;gBACT,KAAK,KAAK;YACZ,CAAC;QAAD,CACD;QACD,GAAG,IAAI;IACT;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO,EAAE;IAC5B,kCAAkC;IAClC,IAAI;QACF,OAAO,2BAAK,IAAI,CAAC,MAAM,WAAW,CAAC;IACrC,EAAE,OAAO,IAAI;QACX,IAAI,WAAW,QAAQ,YAAY,IAAI,GAAG,IAAI,KAAK,UACjD,OAAO,KAAK;aAEZ,MAAM,GAAE;IAEZ;AACF;;;;;;;;;;ACbA,IAAA;AACA,IAAA;AA5CA;AAEA,oDAAoD;AACpD,MAAM,wCAAkB;AAExB,SAAS,oCAAc,GAAG,EAAE;IACxB,oBAAoB;IACpB,MAAM,IAAI,OAAO,CAAC,uCAAiB;IAEnC,OAAO;AACX;AAEA,SAAS,qCAAe,GAAG,EAAE,qBAAqB,EAAE;IAChD,oBAAoB;IACpB,MAAM,CAAC,EAAE,IAAI,CAAC;IAEd,mDAAmD;IAEnD,sDAAsD;IACtD,4DAA4D;IAC5D,MAAM,IAAI,OAAO,CAAC,WAAW;IAE7B,4DAA4D;IAC5D,4CAA4C;IAC5C,gCAAgC;IAChC,MAAM,IAAI,OAAO,CAAC,UAAU;IAE5B,wCAAwC;IAExC,yBAAyB;IACzB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAEhB,oBAAoB;IACpB,MAAM,IAAI,OAAO,CAAC,uCAAiB;IAEnC,wCAAwC;IACxC,IAAI,uBACA,MAAM,IAAI,OAAO,CAAC,uCAAiB;IAGvC,OAAO;AACX;AAEA,4CAAyB;AACzB,4CAA0B;;;;;AC5C1B;;;;AAKA,SAAS,kCAAY,OAAO,EAAE;IAC1B,yCAAyC;IACzC,MAAM,OAAO;IACb,IAAI;IAEJ,IAAI,OAAO,KAAK,EACZ,yBAAyB;IACzB,SAAS,OAAO,KAAK,CAAC;SACnB;QACH,kBAAkB;QAClB,SAAS,IAAI,OAAO;QACpB,OAAO,IAAI,CAAC,IAAI,YAAY;IAChC,CAAC;IAED,IAAI;IAEJ,IAAI;QACA,KAAK,mBAAY,SAAS;QAC1B,mBAAY,IAAI,QAAQ,GAAG,MAAM;QACjC,oBAAa;IACjB,EAAE,OAAO,GAAG,CAAc;IAE1B,iEAAiE;IACjE,OAAO,OAAe,OAAO,QAAQ;AACzC;AAEA,iBAAiB;;;;AC/BjB;;;AAGA,iBAAiB,SAAU,GAAG,EAAE;IAC/B,IAAI,QAAQ,IAAI,KAAK,CAAC;IAEtB,IAAI,CAAC,OACJ,OAAO,IAAI;IAGZ,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;IAC7C,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG;IAC/B,IAAI,MAAM,GAAG,CAAC,EAAE;IAEhB,OAAQ,QAAQ,QACf,MACA,MAAO,CAAA,MAAM,MAAM,MAAM,EAAE,AAAD,CAAE;AAE9B;;;;AClBA;AACA,iBAAiB;;;;;;;;ACDjB;AAEA,MAAM,8BAAQ,QAAQ,QAAQ,KAAK;AAEnC,SAAS,oCAAc,QAAQ,EAAE,OAAO,EAAE;IACtC,OAAO,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG;QACrE,MAAM;QACN,OAAO;QACP,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,OAAO,CAAC,CAAC;QACzC,MAAM,SAAS,OAAO;QACtB,WAAW,SAAS,IAAI;IAC5B;AACJ;AAEA,SAAS,uCAAiB,EAAE,EAAE,MAAM,EAAE;IAClC,IAAI,CAAC,6BACD;IAGJ,MAAM,eAAe,GAAG,IAAI;IAE5B,GAAG,IAAI,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE;QAC5B,mEAAmE;QACnE,iDAAiD;QACjD,iEAAiE;QACjE,IAAI,SAAS,QAAQ;YACjB,MAAM,MAAM,mCAAa,MAAM,QAAQ;YAEvC,IAAI,KACA,OAAO,aAAa,IAAI,CAAC,IAAI,SAAS;QAE9C,CAAC;QAED,OAAO,aAAa,KAAK,CAAC,IAAI,YAAY,yCAAyC;IACvF;AACJ;AAEA,SAAS,mCAAa,MAAM,EAAE,MAAM,EAAE;IAClC,IAAI,+BAAS,WAAW,KAAK,CAAC,OAAO,IAAI,EACrC,OAAO,oCAAc,OAAO,QAAQ,EAAE;IAG1C,OAAO,IAAI;AACf;AAEA,SAAS,uCAAiB,MAAM,EAAE,MAAM,EAAE;IACtC,IAAI,+BAAS,WAAW,KAAK,CAAC,OAAO,IAAI,EACrC,OAAO,oCAAc,OAAO,QAAQ,EAAE;IAG1C,OAAO,IAAI;AACf;AAEA,iBAAiB;sBACb;kBACA;sBACA;mBACA;AACJ;;;;;;;;ACtDe,kDACbkE,YADa,EAEE;IACf,OAAO,IAAIrD,QAAQ,CAAClG,SAASwJ,SAAW;QACtCD,aAAaT,EAAb,CAAgB,SAASU;QACzBD,aAAaT,EAAb,CAAgB,SAAStH,CAAAA,OAAQ;YAC/B,IAAIA,SAAS,GAAG;gBACdgI,OAAO,IAAIvD,MAAM;gBACjB;YACD,CAAA;YAEDjG;QACD;IACF;AACF;;;;;AClBDW,iBAAiBkE,KAAKC,KAAL,CAAW;;;;;;;;;ACI5B;;;;;;;;;;;;;;;;;AAcA,MAAMiF,iCAAW;AACjB,MAAML,6BAAOE,CAAAA,GAAAA,qBAAAA,EAAUD,CAAAA,GAAAA,yBAAAA;AAoBvB,IAAIK;AACJ,IAAIC;AAEG,MAAM3E;IACQ,aAANwB,SAA2B;QACtC,IAAIkD,iCAAW,IAAf,EACE,OAAOA;QAGT,IAAI;YACFA,gCAAUE,QAAQ,MAAMT,CAAAA,GAAAA,6CAAAA,EAAc;QACvC,EAAC,OAAOxI,KAAK;YACZ+I,gCAAU,KAAV;QACD;QAED,OAAOA;IACR;IAEY,MAAPlI,QAAQ,WACZmB,QADY,OAEZ6C,IAFY,WAGZ9D,UAAU,IAAVA,GAHW,EAIuB;QAClC,IAAIiI,qCAAe,IAAnB,EAAyB;YACvB,IAAItH,UAAU,MAAM+G,2BAAK;YACzBO,oCAAcE,SAASxH,QAAQkG,MAAT,EAAiB;QACxC,CAAA;QAED,IAAIT,OAAO;YAAC;YAAO;SAAR,CAAkBM,MAAlB,CACTzF,QAAQf,GAAR,CAAYgC,CAAAA,GAAAA,oCAAAA;QAGd,IAAIlC,SAAS;YACXoG,KAAKzE,IAAL,CAAU;YACV,IAAIsG,oCAAc,GAChB7B,KAAKzE,IAAL,CAAU;QAEb,CAfiC,CAiBlC,gGAFC;QAGD,8FAAA;QACA,4CAAA;QACA,IAAImE,MAAM,CAAA;QACV,IAAK,IAAIzG,OAAOwG,QAAQC,GAAxB,CACE,IACE,CAACzG,IAAIsH,UAAJ,CAAe,WAChBtH,QAAQ,sBACRA,QAAQ,cACRA,QAAQ,YAERyG,GAAG,CAACzG,IAAJ,GAAWwG,QAAQC,GAAR,CAAYzG,IAAvB;QAIJ,IAAIuH,iBAAiBN,CAAAA,GAAAA,6CAAK,AAALA,EAAMyB,gCAAU3B,MAAM;iBAACtC;iBAAKgC;QAAN;QAC3Cc,eAAeC,MAAf,CACE,+EADFD;SAEGwB,IAFH,CAEQP,CAAAA,GAAAA,6CAAK,AAALA,KACLO,IAHH,CAGQ,IAAIN,CAAAA,GAAAA,cAAJ,AAAIA,KACThB,EAJH,CAIM,SAASvH,CAAAA,IAAK;YAChB0D,CAAAA,GAAAA,6CAAAA,EAAOoF,KAAP,CAAa9I,GAAG;QACjB,GACAuH,EAPH,CAOM,QAASnH,CAAAA,UAA+B;YAC1C,OAAQA,QAAQc,IAAhB;gBACE,KAAK;oBACHwC,CAAAA,GAAAA,6CAAAA,EAAOW,QAAP,CACE0E,6BACG,CAAA,CAAA,EAAG3I,QAAQ4I,IAAR,CAAaC,OAAQ,CAAA,CAAA,EAAG7I,QAAQ4I,IAAR,CAAaE,KAAM,CAAA,EAAA,EAAI9I,QAAQ4I,IAAR,CAAa5I,OAAQ,CAAA,CADpE;oBAIR;gBACF,KAAK;gBACL,KAAK;oBACHsD,CAAAA,GAAAA,6CAAAA,EAAOyF,IAAP,CAAY;wBACVtI,QAAQ;wBACRT,SAAS2I,6BAAO3I,QAAQ4I,IAAT;oBAFL;oBAIZ;gBACF;YAfF;QAkBD;QAEH3B,eAAeK,MAAf,CACGmB,IADH,CACQP,CAAAA,GAAAA,6CAAAA,KACLO,IAFH,CAEQ,IAAIN,CAAAA,GAAAA,cAAAA,KACThB,EAHH,CAGM,SAASvH,CAAAA,IAAK;YAChB0D,CAAAA,GAAAA,6CAAAA,EAAOoF,KAAP,CAAa9I,GAAG;QACjB,GACAuH,EANH,CAMM,QAASnH,CAAAA,UAA+B;YAC1C,OAAQA,QAAQc,IAAhB;gBACE,KAAK;oBACHwC,CAAAA,GAAAA,6CAAAA,EAAO0F,IAAP,CAAY;wBACVvI,QAAQ;wBACRT,SAAS2I,6BAAO3I,QAAQ4I,IAAT;oBAFL;oBAIZ;gBACF,KAAK;oBACHtF,CAAAA,GAAAA,6CAAAA,EAAOoF,KAAP,CAAa;wBACXjI,QAAQ;wBACRT,SAAS2I,6BAAO3I,QAAQ4I,IAAT;oBAFJ;oBAIb;gBACF;YAbF;QAgBD;QAEH,IAAI;YACF,OAAO,MAAMhC,CAAAA,GAAAA,cAAAA,EAAmBK;QACjC,EAAC,OAAOrH,GAAG;YACV,MAAM,IAAI0E,MAAM,oCAAoC1E,EAAEI,OAAhD,EAAN;QACD;IACF;AA9G2C;AAiH9C,SAAS2I,6BAAO3I,OAAhB,EAAyC;IACvC,OAAO,WAAWA;AACnB;AAED9D,CAAAA,GAAAA,2CAAAA,EAA2B,CAAA,EAAEa,CAAAA,GAAAA,6CAAAA,EAAIiE,OAAQ,CAAA,KAAA,CAAhB,EAAwB2C;;;;AC/JjD;AAAA,iBAAiB;;;;;ACAjB;;;;;;;;;;;;;;AAcA,GAEA;;;0CAEM;;8CACA;AACN,MAAM,8BAAQ,OAAO;AACrB,MAAM,iCAAW,OAAO;AAExB,SAAS,gCAAW,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;IAClC,IAAI;IACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,MAAM,IAAI,CAAC,+BAAS,CAAC,KAAK,CAAC;QAC/B,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO;QAE7B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,KAAK,+CAA+C;;QAElF,0FAA0F;QAC1F,KAAK,KAAK;QACV,IAAI,CAAC,QAAQ,GAAG,KAAK;IACvB,OAAO;QACL,IAAI,CAAC,4BAAM,IAAI,IAAI,CAAC,+BAAS,CAAC,KAAK,CAAC;QACpC,OAAO,IAAI,CAAC,4BAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO;IACvC,CAAC;IAED,IAAI,CAAC,4BAAM,GAAG,KAAK,GAAG;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAC/B,IAAI;QACF,2BAAK,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,OAAO,GAAG;IACZ;IAGF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,4BAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IACnD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,GAAG,IAAI,MAAM;IAE7D;AACF;AAEA,SAAS,4BAAO,EAAE,EAAE;IAClB,sCAAsC;IACtC,IAAI,CAAC,4BAAM,IAAI,IAAI,CAAC,+BAAS,CAAC,GAAG;IAEjC,IAAI,IAAI,CAAC,4BAAM,EACb,IAAI;QACF,2BAAK,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAAM;IACpC,EAAE,OAAO,OAAO;QACd,OAAO,GAAG;IACZ;IAGF;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,GAAG,EAAE;IACxB,IAAI,QAAQ,WACV,KAAK,IAAI,CAAC;AAEd;AAEA,SAAS,2BAAM,QAAQ,EAAE;IACvB,OAAO;AACT;AAEA,SAAS,4BAAO,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;IACxC,+CAA+C;IAC/C,UAAU,WAAW;IACrB,SAAS,UAAU;IACnB,UAAU,WAAW,CAAC;IAEtB,6BAA6B;IAC7B,OAAQ,UAAU,MAAM;QACtB,KAAK;YACH,8BAA8B;YAC9B,IAAI,OAAO,YAAY,YAAY;gBACjC,SAAS;gBACT,UAAU;YACZ,+BAA+B;YAC/B,OAAO,IAAI,OAAO,YAAY,YAAY,CAAE,CAAA,mBAAmB,MAAK,GAAI;gBACtE,UAAU;gBACV,UAAU;YACZ,CAAC;YACD,KAAK;QAEP,KAAK;YACH,uCAAuC;YACvC,IAAI,OAAO,YAAY,YAAY;gBACjC,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ,wCAAwC;YACxC,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,UAAU;gBACV,SAAS;YACX,CAAC;IACL;IAEA,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG;IAC5B,QAAQ,SAAS,GAAG;IACpB,QAAQ,KAAK,GAAG;IAChB,QAAQ,kBAAkB,GAAG,IAAI;IAEjC,MAAM,SAAS,wCAAc;IAE7B,MAAM,CAAC,4BAAM,GAAG;IAChB,MAAM,CAAC,+BAAS,GAAG,4CAAkB;IACrC,OAAO,OAAO,GAAG;IACjB,OAAO,MAAM,GAAG;IAChB,OAAO,SAAS,GAAG,QAAQ,SAAS;IACpC,OAAO,YAAY,GAAG,QAAQ,YAAY;IAC1C,OAAO,QAAQ,GAAG,KAAK;IAEvB,OAAO;AACT;AAEA,iBAAiB;;;;;;;AC/HjB;;AAKe,uDAA8BsF,CAAAA,GAAAA,uBAAAA;IAC3C1L,YAAYgC,OAAD,CAAiB;QAC1B,KAAA,CAAM;YAAC,GAAGA,OAAJ;YAAa2J,YAAY,IAAZA;QAAb;IACP;IAGDC,WACEC,KADQ,EAER/J,QAFQ,EAGRgK,QAHQ,EAIR;QACA,IAAI;YACF,IAAIC;YACJ,IAAI;gBACFA,SAASpG,KAAKC,KAAL,CAAWiG,MAAM/B,QAAN;YACrB,EAAC,OAAOzH,GAAG;gBACV,8DAAA;gBACA,mDAAA;gBACA0D,CAAAA,GAAAA,6CAAAA,EAAOiG,OAAP,CAAe;oBACbvJ,SAAS,mCAAmCoJ,MAAM/B,QAAN;oBAC5C5G,QAAQ;gBAFK;gBAIf;YACD;YACD4I,SAAS,IAAD,EAAOC;QAChB,EAAC,OAAOhK,KAAK;YACZ+J,SAAS/J;QACV;IACF;AA5BoD;;;;;;;;;ACLvD;;;;;;;;;;;;;;;AAYA,MAAMkK,iCAAW;AA+CjB,IAAIC;AACG,MAAM7F;IACQ,aAANuB,SAA2B;QACtC,IAAIsE,iCAAW,IAAf,EACE,OAAOA;QAGT,IAAI;YACFA,gCAAUlB,QAAQ,MAAMT,CAAAA,GAAAA,6CAAAA,EAAc;QACvC,EAAC,OAAOxI,KAAK;YACZmK,gCAAU,KAAV;QACD;QAED,OAAOA;IACR;IAEY,MAAPtJ,QAAQ,WACZmB,QADY,OAEZ6C,IAFY,WAGZ9D,UAAU,IAAVA,GAHW,EAIuB;QAClC,IAAIoG,OAAO;YAAC;YAAO;YAAc;SAAjC;QACA,IAAIpG,SACFoG,KAAKzE,IAAL,CAAU,MAAM;QAElByE,OAAOA,KAAKM,MAAL,CAAYzF,QAAQf,GAAR,CAAYgC,CAAAA,GAAAA,oCAAAA;QAE/B,IAAI4D,MAAM,CAAA;QACV,IAAK,IAAIzG,OAAOwG,QAAQC,GAAxB,CACE,IAAI,CAACzG,IAAIsH,UAAJ,CAAe,WAAWtH,QAAQ,cAAcA,QAAQ,YAC3DyG,GAAG,CAACzG,IAAJ,GAAWwG,QAAQC,GAAR,CAAYzG,IAAvB;QAIJ,IAAI+H,aAAa,GACfiC,eAAe;QAEjB,IAAIzC,iBAAiBN,CAAAA,GAAAA,6CAAK,AAALA,EAAM6C,gCAAU/C,MAAM;iBACzCtC;iBACAgC;QAFyC;QAI3Cc,eAAeC,MAAf,CACGuB,IADH,CACQP,CAAAA,GAAAA,6CAAAA,KACLO,IAFH,CAEQ,IAAIN,CAAAA,GAAAA,cAAAA,KACThB,EAHH,CAGM,SAASvH,CAAAA,IAAK;YAChB0D,CAAAA,GAAAA,6CAAAA,EAAO0F,IAAP,CAAY;gBACVvI,QAAQ;gBACRT,SAASJ,EAAEwJ,KAFD;gBAGVO,OAAO/J,EAAE+J,KAATA;YAHU;QAKb,GACAxC,EAVH,CAUM,QAASvG,CAAAA,OAAsB;YACjC,IAAIA,KAAKgJ,KAAL,KAAe,SACjBtG,CAAAA,GAAAA,6CAAM,AAANA,EAAOoF,KAAP,CAAa;gBACXjI,QAAQ;gBACRT,SAASY,KAAKtB,GAAL,CAASU,OAFP;gBAGX2J,OAAO/I,KAAKtB,GAAL,CAASqK,KAAhBA;YAHW;iBAKR,IAAI/I,KAAKgJ,KAAL,KAAe,UAAU,OAAOhJ,KAAKZ,OAAZ,KAAwB,UAC1DsD,CAAAA,GAAAA,6CAAAA,EAAOyF,IAAP,CAAY;gBACVtI,QAAQ;gBACRT,SAAS2I,6BAAO/H,KAAKZ,OAAN;YAFL;iBAIP,IAAIY,KAAK1C,IAAL,KAAc,cAAc;oBACvB0C;gBAAd6G,cAAc7G,CAAAA,cAAAA,KAAK8G,KAAL,cAAA9G,yBAAAA,cAAc,CAA5B;oBACgBA;gBAAhB8I,gBAAgB9I,CAAAA,gBAAAA,KAAKiJ,OAAL,cAAAjJ,2BAAAA,gBAAgB,CAAhC;YACD,CAAA;QACF;QAEH,IAAI0G,SAAS,EAAb;QACAL,eAAeK,MAAf,CACGH,EADH,CACM,QAAQ2C,CAAAA,MAAO;YACjBxC,OAAOtF,IAAP,CAAY8H,IAAIzC,QAAJ;QACb,GACAF,EAJH,CAIM,SAASvH,CAAAA,IAAK;YAChB0D,CAAAA,GAAAA,6CAAAA,EAAO0F,IAAP,CAAY;gBACVvI,QAAQ;gBACRT,SAASJ,EAAEI,OAAXA;YAFU;QAIb;QAEH,IAAI;YACF,MAAM4G,CAAAA,GAAAA,cAAAA,EAAmBK;YAEzB,IAAIQ,aAAa,KAAKiC,eAAe,GACnCpG,CAAAA,GAAAA,6CAAM,AAANA,EAAOqE,GAAP,CAAW;gBACTlH,QAAQ;gBACRT,SAAU,CAAA,MAAA,EAAQyH,WAAW,KAAA,EAC3BiC,eAAe,IAAK,CAAA,QAAA,EAAUA,aAAa,CAA3C,GAA+C,EAChD,CAFD1J,kBAAAA,CAAAA;YAFS;YAJX,CAYF,wEAFC;YAGD,yEAAA;YACA,+BAAA;YACA,KAAK,IAAIA,WAAWsH,OAClBhE,CAAAA,GAAAA,6CAAAA,EAAOqE,GAAP,CAAW;gBACTlH,QAAQ;yBACRT;YAFS;QAKd,EAAC,OAAOJ,GAAG;YACV,MAAM,IAAI0E,MAAM,kCAAhB;QACD;IACF;AAxG2C;AA2G9C,SAASqE,6BAAO3I,OAAhB,EAAyC;IACvC,OAAO,WAAWA;AACnB;AAED9D,CAAAA,GAAAA,2CAAAA,EAA2B,CAAA,EAAEa,CAAAA,GAAAA,6CAAAA,EAAIiE,OAAQ,CAAA,KAAA,CAAhB,EAAwB4C;;;;;;;AC7KjD,MAAMmG,qCAAe;AAEN,kDAAiCC,UAAjC,EAA6D;IAC1E,IAAIC,UAAUF,mCAAahC,IAAb,CAAkBiC;IAChC,IAAIC,SACF,OAAOA,OAAO,CAAC,EAAf;IAGF,OAAO;AACR;;;;;;;;ACLD;;;AAGO,MAAMjN,kDAAqBkN,CAAAA,GAAAA,uBAA3B,AAA2BA;IACnB,MAAP7L,QACJa,EADW,EAEXf,IAFW,EAGa;QACxB,IAAIgM,MAAM;YACRxI,wBAAwB,EADhB;YAERC,wBAAwB,IAAIR;QAFpB;QAKV,IAAIlC,EAAE,CAAC,EAAH,KAAU,KACZA,KAAKxC,CAAAA,GAAAA,qCAAAA,EAAK2B,OAAL,CAAa3B,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAL,CAAatB,OAAOe;QAGxC,IAAIwC,MAAMhF,CAAAA,GAAAA,qCAAAA,EAAKgC,UAAL,CAAgBQ,MACtB,MAAM,IAAA,CAAKkL,YAAL,CAAkBlL,IAAIiL,OAC5B,MAAM,IAAA,CAAKE,eAAL,CAAqBnL,IAAIf,MAAMgM,IAFzC;QAIA,IAAI,CAACzI,KAAK;YACR,IAAI9B,IAAI,IAAI0E,MAAO,CAAA,0BAAA,EAA4BpF,GAAG,QAAA,EAAUf,KAAK,CAAA,CAAzD,GACR,2BADA;YAEAyB,EAAEC,IAAF,GAAS;YACT,MAAMD,EAAN;QACD,CAAA;QAED,IAAIlD,CAAAA,GAAAA,qCAAAA,EAAKgC,UAAL,CAAgBgD,IAAItD,QAApB,GACFsD,IAAItD,QAAJ,GAAe,MAAM,IAAA,CAAKZ,EAAL,CAAQ8M,QAAR,CAAiB5I,IAAItD,QAArB;QAGvB,OAAOsD;IACR;IAEiB,MAAZ0I,aACJlL,EADgB,EAEhBiL,GAFgB,EAGS;QACzB,4CAAA;QACA,OACE,AAAC,MAAM,IAAA,CAAKI,UAAL,CAAgBrL,IAAI,IAApB,EAA0BiL,QAChC,MAAM,IAAA,CAAKK,aAAL,CAAmBtL,IAAI,IAAvB,EAA6BiL,KAAM,sCAF5C;;IAID;IAEDM,YACEC,UADS,EAETP,GAFS,EAGc;QACvB,2FAAA;QACA,IAAIpH,UAAU,IAAA,CAAK4H,yBAAL,CAA+BD;QAC7C,IAAI3H,SACF,OAAO,IAAA,CAAK6H,WAAL,CAAiB7H,SAASoH;QAGnCA,IAAIxI,sBAAJ,CAA2BK,IAA3B,CAAgC;YAC9B6I,UAAU;YACVC,eAAeJ;QAFe;QAKhC,IAAIK,MAAMrO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAaiL;QACvB,IAAIM,UAAU,IAAA,CAAKxN,EAAL,CAAQyN,gBAAR,CACZ;YAAC;SADW,EAEZF,KACA,IAAA,CAAKtN,WAHO;QAKd,IAAIuN,WAAW,IAAf,EACE,OAAO,IAAA,CAAKJ,WAAL,CAAiBI,SAASb;QAGnC,OAAO5F,QAAQlG,OAAR,CAAgB,IAAhB;IACR;IAEgB,MAAXuM,YACJ3I,IADe,EAEfkI,GAFe,EAGO;QACtB,IAAI1I,SAAS,IAAA,CAAKyJ,YAAL,CAAkBvL,GAAlB,CAAsBsC;QAEnC,IAAIR,QAAQ;YACV0I,IAAIvI,sBAAJ,CAA2BP,GAA3B,CAA+BY;YAC/B,OAAOR;QACR,CAAA;QAED,IAAIb;QACJ,IAAI;YACFA,OAAO,MAAM,IAAA,CAAKpD,EAAL,CAAQyF,QAAR,CAAiBhB,MAAM;QACrC,EAAC,OAAO3C,KAAK;YACZ6K,IAAIxI,sBAAJ,CAA2BK,IAA3B,CAAgC;gBAC9BvD,UAAUwD;YADoB;YAGhC,MAAM3C,IAAN;QACD,EAED,2EAFC;QAGD,wEAAA;QACA6K,IAAIvI,sBAAJ,CAA2BP,GAA3B,CAA+BY;QAE/B,IAAIlF,MAAMmG,KAAKC,KAAL,CAAWvC;QACrB,IAAA,CAAKsK,YAAL,CAAkBjK,GAAlB,CAAsBgB,MAAMlF;QAC5B,OAAOA;IACR;IAEe,MAAVwN,WACJtI,IADc,EAEdlF,GAFc,EAGdoN,GAHc,EAIW;QACzB,+BAAA;QACA,IAAIgB,QAAQ,IAAA,CAAKC,UAAL,CAAgBnJ;QAC5B,IAAIoJ,QAAQ,IAAA,CAAK7N,EAAL,CAAQ8N,aAAR,CAAsBH,QAElC,oDAFA;QAGA,yCAAA;QACA,KAAK,IAAIlJ,QAAQkJ,MAAO;YACtB,IAAIlJ,SAASoJ,OACX,KAAA;YAGFlB,IAAIxI,sBAAJ,CAA2BK,IAA3B,CAAgC;gBAC9BvD,UAAUwD;YADoB;QAGjC;QAED,IAAIoJ,OACF,OAAO;YACLjN,UAAU,MAAM,IAAA,CAAKZ,EAAL,CAAQ8M,QAAR,CAAiBe;YACjC,mDAAA;YACAtO,KAAKA,gBAAAA,iBAAAA,MAAQ,MAAM,IAAA,CAAK0N,WAAL,CAAiBxI,MAAMkI,IAHrC;YAILxI,wBAAwBwI,IAAIxI,sBAJvB;YAKLC,wBAAwBuI,IAAIvI,sBAA5BA;QALK;QAST,OAAO,IAAP;IACD;IAEkB,MAAb4I,cACJO,GADiB,EAEjBhO,MAAoB,IAFH,EAGjBoN,GAHiB,EAIQ;QACzB,IAAI;YACFpN,MAAM,MAAM,IAAA,CAAK6N,WAAL,CAAiBlO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUsI,KAAK,iBAAiBZ,MAE7D,+CAFApN;YAGA,IAAI8F,UAAU,IAAA,CAAK0I,iBAAL,CAAuBR,KAAKhO;YAE1C,KAAK,IAAIkF,QAAQY,QAAS;gBACxB,qEAAA;gBACA,MAAMnB,MACJ,AAAC,MAAM,IAAA,CAAK6I,UAAL,CAAgBtI,MAAMlF,KAAKoN,QACjC,MAAM,IAAA,CAAKK,aAAL,CAAmBvI,MAAMlF,KAAKoN;gBACvC,IAAIzI,KACF,OAAOA;YAEV;QACF,EAAC,OAAOpC,KAAK,CAEb,EAED,mDAFC;QAGD,OAAO,IAAA,CAAKiL,UAAL,CAAgB7N,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUsI,KAAK,UAAUhO,KAAKoN;IACtD;IAEoB,MAAfE,gBACJnL,EADmB,EAEnBf,IAFmB,EAGnBgM,GAHmB,EAIM;QACzB,IAAI;YACF,IAAInL,SAAS,IAAA,CAAKwM,kBAAL,CAAwBtM,IAAIf,MAAMgM;YAC/C,IAAI,CAACnL,UAAUA,OAAOZ,QAAtB,EACE,OAAOY;YAHP,CAMF,+EAFC;YAGD,uDAAA;YACA,IAAIA,OAAOyM,OAAX,EAAoB;gBAClB,IAAI1O,MAAM,MAAM,IAAA,CAAK6N,WAAL,CACdlO,CAAAA,GAAAA,qCAAAA,EAAK+F,IAAL,CAAUzD,OAAO0M,SAAjB,EAA4B,iBAC5BvB;gBAEF,IAAIzI,MAAM,MAAM,IAAA,CAAK6I,UAAL,CAAgBvL,OAAOP,QAAvB,EAAiC1B,KAAKoN;gBACtD,IAAIzI,KACF,OAAOA;YAEV,CAjBC,CAmBF,kCAFC;YAGD,IAAI1C,OAAOP,QAAX,EACE,OAAO,MAAM,IAAA,CAAK+L,aAAL,CAAmBxL,OAAOP,QAA1B,EAAoC,IAApC,EAA0C0L;QAE1D,EAAC,OAAOvK,GAAG,CAEX;IACF;AAlMwE;;;;;;ACE3E,aAAA;AACA;;;;AAKA,MAAM+L,iCAAW;IAACC,QAAQ,IAARA;AAAD;AACjB,KAAK,IAAIC,WAAWpP,CAAAA,GAAAA,uCAAM,AAANA,EAAOqP,cAA3B,CACEH,8BAAQ,CAACE,QAAT,GAAoB,IAApB;AAgBF,MAAME,qCAAgB,CAAA,EAAErP,CAAAA,GAAAA,qCAAAA,EAAKsP,GAAI,CAAA,YAAA,EAActP,CAAAA,GAAAA,qCAAAA,EAAKsP,GAAI,CAAA,CAAxD;AAEO,MAAM9B;IAMX3M,YACEC,EADS,EAETC,WAFS,EAGTwO,UAHS,CAIT;QACA,IAAA,CAAKzO,EAAL,GAAUA;QACV,IAAA,CAAKC,WAAL,GAAmBA;QACnB,IAAA,CAAKwO,UAAL,GACEA,cACA,2BAFF;QAGErJ,OAAOsJ,IAAP,CAAYzP,CAAAA,GAAAA,uCAAAA,EAAO0P,WAAnB;QACF,IAAA,CAAKjB,YAAL,GAAoB,IAAI/N;IACzB;IAEDkB,QAAQa,EAAD,EAA0Bf,IAA1B,EAA6C;QAClD,MAAM,IAAImG,MAAO,CAAA,mBAAA,EAAqBpF,GAAG,QAAA,EAAUf,KAAK,CAAA,CAAlD,EAAN;IACD;IAEDiN,WAAWnJ,IAAD,EAAkC;QAC1C,gCAAA;QACA,IAAIP,MAAM,EAAV;QACA,KAAK,IAAI0K,OAAO,IAAA,CAAKH,UAArB,CAEEvK,IAAIM,IAAJ,CADQC,OAAOmK;QAIjB,IAAI1P,CAAAA,GAAAA,qCAAAA,EAAK2P,OAAL,CAAapK,OACfP,IAAI4K,OAAJ,CAAYrK;aAEZP,IAAIM,IAAJ,CAASC;QAGX,OAAOP;IACR;IAED6J,kBAAkBR,GAAD,EAAgBhO,GAAhB,EAAiD;QAChE,IAAIwP,OAAOxP,IAAIwP,IAAf;QACA,IACE,AACErG,CAAAA,GAAAA,QAAQC,GAAR,CAAYC,iBADd,AACcA,KACd,OAAOrJ,IAAImB,IAAX,KAAoB,YACpB,OAAOnB,IAAIyP,MAAX,KAAsB,YACtBzP,IAAImB,IAAJ,CAAS8I,UAAT,CAAoB,eACpBjK,IAAImB,IAAJ,KAAa,mBAEbqO,OAAOxP,IAAIyP,MAAX;QAGF,OAAO;YAACD;SAAD,CACJ7G,MADI,CACG+G,CAAAA,QAAS,OAAOA,UAAU,UACjClM,GAFI,CAEAgM,CAAAA,OAAQ;YACX,8CAAA;YACA,IAAI,CAACA,QAAQA,SAAS,OAAOA,SAAS,MACpCA,OAAO;YAGTlK,CAAAA,GAAAA,uCAAAA,EAAU,OAAOkK,SAAS;YAC1B,OAAO7P,CAAAA,GAAAA,qCAAAA,EAAK2B,OAAL,CAAa0M,KAAKwB;QAC1B;IACJ;IAEDG,UAAUxO,IAAD,EAAqC;QAC5C,OAAO,CAAC,CAAEyN,CAAAA,8BAAQ,CAACzN,KAAT,IAAkBA,KAAK8I,UAAL,CAAgB,QAAhB;IAC7B;IAEDwE,mBACEtM,EADgB,EAEhBwL,UAFgB,EAGhBP,GAHgB,EAIc;QAC9B,IAAI,IAAA,CAAKuC,SAAL,CAAexN,KACjB,OAAO;YACLd,UAAUc;YACV0C,wBAAwB,IAAIR;YAC5BO,wBAAwB,EAAxBA;QAHK;QAOT,IAAI,CAACqI,YAAYyB,QAAb,GAAwB7O,CAAAA,GAAAA,iCAAc,AAAdA,EAAesC;QAC3C,IAAI6L,MAAMrO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAaiL;QACvB,IAAIgB,YAAY,IAAA,CAAKlO,EAAL,CAAQmP,cAAR,CAAuB3C,YAAYe;QAEnDZ,IAAIxI,sBAAJ,CAA2BK,IAA3B,CAAgC;YAC9B6I,UAAW,CAAA,aAAA,EAAeb,WAAW,CADP;YAE9Bc,eAAeJ;QAFe;QAKhC,IAAI,CAACgB,aAAaxF,QAAQ0G,QAAR,CAAiBC,GAAjB,IAAwB,IAA1C,EACE,IAAI;YACF,2BAAA;YACA,IAAIA,MAAMpQ,CAAAA,GAAAA,uCAAM,AAANA,EAAOqQ,UAAP,CAAkB/B,MAAM;YAClCW,YAAYmB,IAAIE,oBAAJ,CACV/C,aAEG9K,CAAAA,EAAE,CAAC8K,WAAW5G,MAAZ,CAAF,KAA0B,MAAM,MAAM,EAF/B,AAE+B,GACzC2H,MAAM,MAGR,gDAJK7L;YAKLiL,IAAIvI,sBAAJ,CAA2BP,GAA3B,CACEwL,IAAIE,oBAAJ,CAAyB,UAAU,IAAnC;QAEH,EAAC,OAAOnN,GAAG;YACV,IAAIA,EAAEC,IAAF,KAAW,oBACb,MAAMD,EAAN;QAEH;QAGH,IAAI8L,WACF,OAAO;wBACL1B;qBACAyB;YACAC,WAAWA;YACXjN,UAAUgN,UAAU/O,CAAAA,GAAAA,qCAAAA,EAAK+F,IAAL,CAAUiJ,WAAWD,WAAWC,SAApDjN;QAJK;QAQT,OAAO,IAAP;IACD;IAEDkM,0BAA0BD,UAAD,EAAkC;QACzD,0FAAA;QACA,sFAAA;QACA,IAAIsC,QAAQtC,WAAWuC,WAAX,CAAuBlB;QACnC,IAAIiB,SAAS,GAAG;YACdA,SAASjB,mCAAa3I,MAAtB,EAEA,+CAFA4J;YAGA,IAAItC,UAAU,CAACsC,MAAX,KAAsB,KACxBA,QAAQtC,WAAWwC,OAAX,CAAmBxQ,CAAAA,GAAAA,qCAAAA,EAAKsP,GAAxB,EAA6BgB,SAAS;YAGhDA,QAAQtC,WAAWwC,OAAX,CAAmBxQ,CAAAA,GAAAA,qCAAAA,EAAKsP,GAAxB,EAA6BgB;YACrC,OAAOtQ,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CACLiI,WAAWyC,KAAX,CAAiB,GAAGH,SAAS,IAAIA,QAAQnO,SAAzC,GACA;QAEH,CAAA;IACF;IAEDsD,WAAW1D,QAAD,EAAqB;QAC7B,gDAAA;QACA,IAAIsM,MAAMrO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAahB;QACvB,IAAI,QAAC2O,KAAAA,EAAD,GAAS1Q,CAAAA,GAAAA,qCAAI,AAAJA,EAAKyG,KAAL,CAAW4H;QACxB,MAAOA,QAAQqC,QAAQ1Q,CAAAA,GAAAA,qCAAI,AAAJA,EAAKwI,QAAL,CAAc6F,SAAS,eAAgB;YAC5D,IAAA,CAAKG,YAAL,CAAkB9I,MAAlB,CAAyB1F,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUsI,KAAK;YACxCA,MAAMrO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAasL;QACpB;IACF;AA3J8B;;;;;;;;AC/BjC;;;AAGO,MAAM9N,kDAAyBiN,CAAAA,GAAAA,uBAA/B,AAA+BA;IACpC7L,QAAQa,EAAD,EAA0Bf,IAA1B,EAAyD;QAC9D,IAAIgM,MAAM;YACRxI,wBAAwB,EADhB;YAERC,wBAAwB,IAAIR;QAFpB;QAKV,IAAIlC,EAAE,CAAC,EAAH,KAAU,KACZA,KAAKxC,CAAAA,GAAAA,qCAAAA,EAAK2B,OAAL,CAAa3B,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAL,CAAatB,OAAOe;QAGxC,IAAIwC,MAAMhF,CAAAA,GAAAA,qCAAAA,EAAKgC,UAAL,CAAgBQ,MACtB,IAAA,CAAKkL,YAAL,CAAkBlL,IAAIiL,OACtB,IAAA,CAAKE,eAAL,CAAqBnL,IAAIf,MAAMgM,IAFnC;QAIA,IAAI,CAACzI,KAAK;YACR,IAAI9B,IAAI,IAAI0E,MAAO,CAAA,0BAAA,EAA4BpF,GAAG,QAAA,EAAUf,KAAK,CAAA,CAAzD,GACR,aADA;YAEAyB,EAAEC,IAAF,GAAS;YACT,MAAMD,EAAN;QACD,CAAA;QAED,IAAIlD,CAAAA,GAAAA,qCAAAA,EAAKgC,UAAL,CAAgBgD,IAAItD,QAApB,GACFsD,IAAItD,QAAJ,GAAe,IAAA,CAAKZ,EAAL,CAAQ6P,YAAR,CAAqB3L,IAAItD,QAAzB;QAGjB,OAAOsD;IACR;IAED0I,aAAalL,EAAD,EAAeiL,GAAf,EAAqD;QAC/D,4CAAA;QACA,OAAO,IAAA,CAAKI,UAAL,CAAgBrL,IAAI,IAApB,EAA0BiL,QAAQ,IAAA,CAAKK,aAAL,CAAmBtL,IAAI,IAAvB,EAA6BiL;IACvE;IAEDM,YAAYC,UAAD,EAAuBP,GAAvB,EAA2D;QACpE,2FAAA;QACA,IAAIpH,UAAU,IAAA,CAAK4H,yBAAL,CAA+BD;QAC7C,IAAI3H,SACF,OAAO,IAAA,CAAK6H,WAAL,CAAiB7H,SAASoH;QAJiC,CAOpE,4EAFC;QAGD,IAAIY,MAAMrO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+C,OAAL,CAAaiL;QACvB,IAAIM,UAAU,IAAA,CAAKxN,EAAL,CAAQyN,gBAAR,CACZ;YAAC;SADW,EAEZF,KACA,IAAA,CAAKtN,WAHO;QAKd,IAAIuN,WAAW,IAAf,EACE,OAAO,IAAA,CAAKJ,WAAL,CAAiBI,SAASb;IAEpC;IAEDS,YAAY3I,IAAD,EAAiBkI,GAAjB,EAAoD;QAC7D,IAAI1I,SAAS,IAAA,CAAKyJ,YAAL,CAAkBvL,GAAlB,CAAsBsC;QAEnC,IAAIR,QAAQ;YACV0I,IAAIvI,sBAAJ,CAA2BP,GAA3B,CAA+BY;YAC/B,OAAOR;QACR,CAAA;QAED,IAAIb;QACJ,IAAI;YACFA,OAAO,IAAA,CAAKpD,EAAL,CAAQ2B,YAAR,CAAqB8C,MAAM;QACnC,EAAC,OAAO3C,KAAK;YACZ6K,IAAIxI,sBAAJ,CAA2BK,IAA3B,CAAgC;gBAC9BvD,UAAUwD;YADoB;YAGhC,MAAM3C,IAAN;QACD,EAED,2EAFC;QAGD,wEAAA;QACA6K,IAAIvI,sBAAJ,CAA2BP,GAA3B,CAA+BY;QAE/B,IAAIlF,MAAMmG,KAAKC,KAAL,CAAWvC;QAErB,IAAA,CAAKsK,YAAL,CAAkBjK,GAAlB,CAAsBgB,MAAMlF;QAC5B,OAAOA;IACR;IAEDwN,WACEtI,IADQ,EAERlF,GAFQ,EAGRoN,GAHQ,EAIQ;QAChB,+BAAA;QACA,IAAIgB,QAAQ,IAAA,CAAKC,UAAL,CAAgBnJ;QAC5B,IAAIoJ,QAAQ,IAAA,CAAK7N,EAAL,CAAQ8N,aAAR,CAAsBH,QAElC,oDAFA;QAGA,yCAAA;QACA,KAAK,IAAIlJ,QAAQkJ,MAAO;YACtB,IAAIlJ,SAASoJ,OACX,KAAA;YAGFlB,IAAIxI,sBAAJ,CAA2BK,IAA3B,CAAgC;gBAC9BvD,UAAUwD;YADoB;QAGjC;QAED,IAAIoJ,OACF,OAAO;YACLjN,UAAU,IAAA,CAAKZ,EAAL,CAAQ6P,YAAR,CAAqBhC;YAC/B,mDAAA;YACAtO,KAAKA,gBAAAA,iBAAAA,MAAO,IAAA,CAAK0N,WAAL,CAAiBxI,MAAMkI,IAH9B;YAILxI,wBAAwBwI,IAAIxI,sBAJvB;YAKLC,wBAAwBuI,IAAIvI,sBAA5BA;QALK;QAST,OAAO,IAAP;IACD;IAED4I,cACEO,GADW,EAEXhO,MAAoB,IAFT,EAGXoN,GAHW,EAIK;QAChB,IAAI;YACFpN,MAAM,IAAA,CAAK6N,WAAL,CAAiBlO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUsI,KAAK,iBAAiBZ,MAEvD,+CAFApN;YAGA,IAAI8F,UAAU,IAAA,CAAK0I,iBAAL,CAAuBR,KAAKhO;YAE1C,KAAK,IAAIkF,QAAQY,QAAS;gBACxB,qEAAA;gBACA,MAAMnB,MACJ,IAAA,CAAK6I,UAAL,CAAgBtI,MAAMlF,KAAKoN,QAAQ,IAAA,CAAKK,aAAL,CAAmBvI,MAAMlF,KAAKoN;gBACnE,IAAIzI,KACF,OAAOA;YAEV;QACF,EAAC,OAAOpC,KAAK,CAEb,EAED,mDAFC;QAGD,OAAO,IAAA,CAAKiL,UAAL,CAAgB7N,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUsI,KAAK,UAAUhO,KAAKoN;IACtD;IAEDE,gBACEnL,EADa,EAEbf,IAFa,EAGbgM,GAHa,EAIG;QAChB,IAAI;YACF,IAAInL,SAAS,IAAA,CAAKwM,kBAAL,CAAwBtM,IAAIf,MAAMgM;YAC/C,IAAI,CAACnL,UAAUA,OAAOZ,QAAtB,EACE,OAAOY;YAHP,CAMF,+EAFC;YAGD,uDAAA;YACA,IAAIA,OAAOyM,OAAX,EAAoB;gBAClB,IAAI1O,MAAM,IAAA,CAAK6N,WAAL,CACRlO,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUzD,OAAO0M,SAAjB,EAA4B,iBAC5BvB;gBAEF,IAAIzI,MAAM,IAAA,CAAK6I,UAAL,CAAgBvL,OAAOP,QAAvB,EAAiC1B,KAAKoN;gBAChD,IAAIzI,KACF,OAAOA;YAEV,CAjBC,CAmBF,kCAFC;YAGD,IAAI1C,OAAOP,QAAX,EACE,OAAO,IAAA,CAAK+L,aAAL,CAAmBxL,OAAOP,QAA1B,EAAoC,IAApC,EAA0C0L;QAEpD,EAAC,OAAOvK,GAAG,CAEX;IACF;AA7KmE;;;;;;;;ACPtE;;;;;;;;ACIA;;;;;;;AAaO,MAAM2N;IACXC,WAAiC,IAAIrQ,MAArCqQ;IAEAC,SAASC,WAAD,EAAsBlQ,EAAtB,EAAsC6G,WAAtC,EAA6D;QACnE,IAAA,CAAKmJ,QAAL,CAAcvM,GAAd,CAAkByM,aAAa;gBAAClQ;yBAAI6G;QAAL;IAChC;IAEY,MAAPlE,QAAQ,WACZmB,QADY,MAEZ9D,GAFY,OAGZ2G,IAHY,eAIZE,YAJY,WAKZhE,UAAU,IAAVA,GALW,EAMuB;QAClC,IAAIgE,eAAe,IAAnB,EAAyB;YACvBA,cAAc3H,CAAAA,GAAAA,qCAAAA,EAAK+F,IAAL,CAAU0B,KAAK;YAC7B,MAAM3G,GAAGsJ,SAAH,CAAazC,aAAa;QACjC,CAAA;QAED,IAAItH,MAAMmG,KAAKC,KAAL,CAAW,MAAM3F,GAAGyF,QAAH,CAAYoB,aAAa;QACpD,IAAI3E,MAAMW,UAAU,oBAAoB,cAAxC;QAEA,IAAI,CAACtD,GAAG,CAAC2C,IAAT,EACE3C,GAAG,CAAC2C,IAAJ,GAAW,CAAA;QAGb,KAAK,IAAIV,UAAUsC,QACjBvE,GAAG,CAAC2C,IAAJ,CAASV,OAAOd,IAAhB,CAAA,GACE,MAAO,MAAM,IAAA,CAAKpB,cAAL,CAAoBkC,QAAQxB,IAAI6G;QAGjD,MAAM7G,GAAGsJ,SAAH,CAAazC,aAAanB,KAAKyK,SAAL,CAAe5Q;IAChD;IAEmB,MAAdD,eACJ0F,aADkB,EAElBhF,EAFkB,EAGlB6G,WAHkB,EAIJ;QACd,IAAItH,MAAM,IAAA,CAAKyQ,QAAL,CAAc7N,GAAd,CAAkB6C,cAActE,IAAhC;QACV,IAAI,CAACnB,KACH,MAAM,IAAIuH,MAAM,qBAAqB9B,cAActE,IAA7C,EAAN;QAGF,IAAI0P,OAAOlR,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CACT/F,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAL,CAAa4E,cACb,gBACA7B,cAActE,IAHL;QAKX,MAAMoP,CAAAA,GAAAA,mBAAAA,EAAIvQ,IAAIS,EAAL,EAAST,IAAIsH,WAAb,EAA0B7G,IAAIoQ;QAEvC,IAAIC,cAAc3K,KAAKC,KAAL,CAChB,MAAM3F,GAAGyF,QAAH,CAAYvG,CAAAA,GAAAA,qCAAI,AAAJA,EAAK+F,IAAL,CAAUmL,MAAM,iBAAiB;QAGrD,IAAIC,YAAYC,YAAZ,IAA4B,IAAhC,EACE,KAAK,IAAIC,OAAOrL,CAAAA,GAAAA,sCAA+B,AAA/BA,EACdmL,YAAYC,YADiC,EAG7C,MAAM,IAAA,CAAKhR,cAAL,CAAoBiR,KAAKvQ,IAAI6G;QAIvC,OAAOwJ,YAAY7M,OAAnB;IACD;AAhE2D;AAmE9D9E,CAAAA,GAAAA,2CAAAA,EACG,CAAA,EAAEa,CAAAA,GAAAA,6CAAAA,EAAIiE,OAAQ,CAAA,qBAAA,CADQ,EAEvBuM;;;;;", "sources": ["node_modules/isexe/windows.js", "node_modules/isexe/mode.js", "node_modules/path-key/index.js", "node_modules/command-exists/lib/command-exists.js", "node_modules/split2/node_modules/readable-stream/readable.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_readable.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/stream.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/buffer_list.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/destroy.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/state.js", "node_modules/split2/node_modules/readable-stream/errors.js", "node_modules/inherits/inherits.js", "node_modules/inherits/inherits_browser.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_duplex.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_writable.js", "node_modules/util-deprecate/node.js", "node_modules/string_decoder/lib/string_decoder.js", "node_modules/safe-buffer/index.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/async_iterator.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/end-of-stream.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/from.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_transform.js", "node_modules/split2/node_modules/readable-stream/lib/_stream_passthrough.js", "node_modules/split2/node_modules/readable-stream/lib/internal/streams/pipeline.js", "packages/core/package-manager/src/NodePackageManager.js", "packages/core/package-manager/src/utils.js", "packages/core/package-manager/src/installPackage.js", "node_modules/nullthrows/nullthrows.js", "packages/core/package-manager/src/Npm.js", "node_modules/cross-spawn/index.js", "node_modules/cross-spawn/lib/parse.js", "node_modules/nice-try/src/index.js", "node_modules/cross-spawn/lib/util/resolveCommand.js", "node_modules/which/which.js", "node_modules/isexe/index.js", "node_modules/cross-spawn/lib/util/escape.js", "node_modules/cross-spawn/lib/util/readShebang.js", "node_modules/shebang-command/index.js", "node_modules/shebang-regex/index.js", "node_modules/cross-spawn/lib/enoent.js", "packages/core/package-manager/src/promiseFromProcess.js", "packages/core/package-manager/package.json", "packages/core/package-manager/src/Yarn.js", "node_modules/command-exists/index.js", "node_modules/split2/index.js", "packages/core/package-manager/src/JSONParseStream.js", "packages/core/package-manager/src/Pnpm.js", "packages/core/package-manager/src/validateModuleSpecifier.js", "packages/core/package-manager/src/NodeResolver.js", "packages/core/package-manager/src/NodeResolverBase.js", "packages/core/package-manager/src/NodeResolverSync.js", "packages/core/package-manager/src/index.js", "packages/core/package-manager/src/MockPackageInstaller.js"], "sourcesContent": ["module.exports = isexe\nisexe.sync = sync\n\nvar fs = require('fs')\n\nfunction checkPathExt (path, options) {\n  var pathext = options.pathExt !== undefined ?\n    options.pathExt : process.env.PATHEXT\n\n  if (!pathext) {\n    return true\n  }\n\n  pathext = pathext.split(';')\n  if (pathext.indexOf('') !== -1) {\n    return true\n  }\n  for (var i = 0; i < pathext.length; i++) {\n    var p = pathext[i].toLowerCase()\n    if (p && path.substr(-p.length).toLowerCase() === p) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction checkStat (stat, path, options) {\n  if (!stat.isSymbolicLink() && !stat.isFile()) {\n    return false\n  }\n  return checkPathExt(path, options)\n}\n\nfunction isexe (path, options, cb) {\n  fs.stat(path, function (er, stat) {\n    cb(er, er ? false : checkStat(stat, path, options))\n  })\n}\n\nfunction sync (path, options) {\n  return checkStat(fs.statSync(path), path, options)\n}\n", "module.exports = isexe\nisexe.sync = sync\n\nvar fs = require('fs')\n\nfunction isexe (path, options, cb) {\n  fs.stat(path, function (er, stat) {\n    cb(er, er ? false : checkStat(stat, options))\n  })\n}\n\nfunction sync (path, options) {\n  return checkStat(fs.statSync(path), options)\n}\n\nfunction checkStat (stat, options) {\n  return stat.isFile() && checkMode(stat, options)\n}\n\nfunction checkMode (stat, options) {\n  var mod = stat.mode\n  var uid = stat.uid\n  var gid = stat.gid\n\n  var myUid = options.uid !== undefined ?\n    options.uid : process.getuid && process.getuid()\n  var myGid = options.gid !== undefined ?\n    options.gid : process.getgid && process.getgid()\n\n  var u = parseInt('100', 8)\n  var g = parseInt('010', 8)\n  var o = parseInt('001', 8)\n  var ug = u | g\n\n  var ret = (mod & o) ||\n    (mod & g) && gid === myGid ||\n    (mod & u) && uid === myUid ||\n    (mod & ug) && myUid === 0\n\n  return ret\n}\n", "'use strict';\nmodule.exports = opts => {\n\topts = opts || {};\n\n\tconst env = opts.env || process.env;\n\tconst platform = opts.platform || process.platform;\n\n\tif (platform !== 'win32') {\n\t\treturn 'PATH';\n\t}\n\n\treturn Object.keys(env).find(x => x.toUpperCase() === 'PATH') || 'Path';\n};\n", "'use strict';\n\nvar exec = require('child_process').exec;\nvar execSync = require('child_process').execSync;\nvar fs = require('fs');\nvar path = require('path');\nvar access = fs.access;\nvar accessSync = fs.accessSync;\nvar constants = fs.constants || fs;\n\nvar isUsingWindows = process.platform == 'win32'\n\nvar fileNotExists = function(commandName, callback){\n    access(commandName, constants.F_OK,\n    function(err){\n        callback(!err);\n    });\n};\n\nvar fileNotExistsSync = function(commandName){\n    try{\n        accessSync(commandName, constants.F_OK);\n        return false;\n    }catch(e){\n        return true;\n    }\n};\n\nvar localExecutable = function(commandName, callback){\n    access(commandName, constants.F_OK | constants.X_OK,\n        function(err){\n        callback(null, !err);\n    });\n};\n\nvar localExecutableSync = function(commandName){\n    try{\n        accessSync(commandName, constants.F_OK | constants.X_OK);\n        return true;\n    }catch(e){\n        return false;\n    }\n}\n\nvar commandExistsUnix = function(commandName, cleanedCommandName, callback) {\n\n    fileNotExists(commandName, function(isFile){\n\n        if(!isFile){\n            var child = exec('command -v ' + cleanedCommandName +\n                  ' 2>/dev/null' +\n                  ' && { echo >&1 ' + cleanedCommandName + '; exit 0; }',\n                  function (error, stdout, stderr) {\n                      callback(null, !!stdout);\n                  });\n            return;\n        }\n\n        localExecutable(commandName, callback);\n    });\n\n}\n\nvar commandExistsWindows = function(commandName, cleanedCommandName, callback) {\n  // Regex from Julio from: https://stackoverflow.com/questions/51494579/regex-windows-path-validator\n  if (!(/^(?!(?:.*\\s|.*\\.|\\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:\"\\|\\?\\*\\n])+(?:\\/\\/|\\/|\\\\\\\\|\\\\)?)+$/m.test(commandName))) {\n    callback(null, false);\n    return;\n  }\n  var child = exec('where ' + cleanedCommandName,\n    function (error) {\n      if (error !== null){\n        callback(null, false);\n      } else {\n        callback(null, true);\n      }\n    }\n  )\n}\n\nvar commandExistsUnixSync = function(commandName, cleanedCommandName) {\n  if(fileNotExistsSync(commandName)){\n      try {\n        var stdout = execSync('command -v ' + cleanedCommandName +\n              ' 2>/dev/null' +\n              ' && { echo >&1 ' + cleanedCommandName + '; exit 0; }'\n              );\n        return !!stdout;\n      } catch (error) {\n        return false;\n      }\n  }\n  return localExecutableSync(commandName);\n}\n\nvar commandExistsWindowsSync = function(commandName, cleanedCommandName, callback) {\n  // Regex from Julio from: https://stackoverflow.com/questions/51494579/regex-windows-path-validator\n  if (!(/^(?!(?:.*\\s|.*\\.|\\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:\"\\|\\?\\*\\n])+(?:\\/\\/|\\/|\\\\\\\\|\\\\)?)+$/m.test(commandName))) {\n    return false;\n  }\n  try {\n      var stdout = execSync('where ' + cleanedCommandName, {stdio: []});\n      return !!stdout;\n  } catch (error) {\n      return false;\n  }\n}\n\nvar cleanInput = function(s) {\n  if (/[^A-Za-z0-9_\\/:=-]/.test(s)) {\n    s = \"'\"+s.replace(/'/g,\"'\\\\''\")+\"'\";\n    s = s.replace(/^(?:'')+/g, '') // unduplicate single-quote at the beginning\n      .replace(/\\\\'''/g, \"\\\\'\" ); // remove non-escaped single-quote if there are enclosed between 2 escaped\n  }\n  return s;\n}\n\nif (isUsingWindows) {\n  cleanInput = function(s) {\n    var isPathName = /[\\\\]/.test(s);\n    if (isPathName) {\n      var dirname = '\"' + path.dirname(s) + '\"';\n      var basename = '\"' + path.basename(s) + '\"';\n      return dirname + ':' + basename;\n    }\n    return '\"' + s + '\"';\n  }\n}\n\nmodule.exports = function commandExists(commandName, callback) {\n  var cleanedCommandName = cleanInput(commandName);\n  if (!callback && typeof Promise !== 'undefined') {\n    return new Promise(function(resolve, reject){\n      commandExists(commandName, function(error, output) {\n        if (output) {\n          resolve(commandName);\n        } else {\n          reject(error);\n        }\n      });\n    });\n  }\n  if (isUsingWindows) {\n    commandExistsWindows(commandName, cleanedCommandName, callback);\n  } else {\n    commandExistsUnix(commandName, cleanedCommandName, callback);\n  }\n};\n\nmodule.exports.sync = function(commandName) {\n  var cleanedCommandName = cleanInput(commandName);\n  if (isUsingWindows) {\n    return commandExistsWindowsSync(commandName, cleanedCommandName);\n  } else {\n    return commandExistsUnixSync(commandName, cleanedCommandName);\n  }\n};\n", "var Stream = require('stream');\nif (process.env.READABLE_STREAM === 'disable' && Stream) {\n  module.exports = Stream.Readable;\n  Object.assign(module.exports, Stream);\n  module.exports.Stream = Stream;\n} else {\n  exports = module.exports = require('./lib/_stream_readable.js');\n  exports.Stream = Stream || exports;\n  exports.Readable = exports;\n  exports.Writable = require('./lib/_stream_writable.js');\n  exports.Duplex = require('./lib/_stream_duplex.js');\n  exports.Transform = require('./lib/_stream_transform.js');\n  exports.PassThrough = require('./lib/_stream_passthrough.js');\n  exports.finished = require('./lib/internal/streams/end-of-stream.js');\n  exports.pipeline = require('./lib/internal/streams/pipeline.js');\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n'use strict';\n\nmodule.exports = Readable;\n/*<replacement>*/\n\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n/*<replacement>*/\n\nvar EE = require('events').EventEmitter;\n\nvar EElistenerCount = function EElistenerCount(emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\n\n\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n\nvar Buffer = require('buffer').Buffer;\n\nvar OurUint8Array = global.Uint8Array || function () {};\n\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\n\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n/*<replacement>*/\n\n\nvar debugUtil = require('util');\n\nvar debug;\n\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function debug() {};\n}\n/*</replacement>*/\n\n\nvar BufferList = require('./internal/streams/buffer_list');\n\nvar destroyImpl = require('./internal/streams/destroy');\n\nvar _require = require('./internal/streams/state'),\n    getHighWaterMark = _require.getHighWaterMark;\n\nvar _require$codes = require('../errors').codes,\n    ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n    ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF,\n    ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT; // Lazy loaded to improve the startup performance.\n\n\nvar StringDecoder;\nvar createReadableStreamAsyncIterator;\nvar from;\n\nrequire('inherits')(Readable, Stream);\n\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\n\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn); // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\n\nfunction ReadableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {}; // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex; // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode; // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n\n  this.highWaterMark = getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex); // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false; // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n\n  this.sync = true; // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n  this.paused = true; // Should close be emitted on destroy. Defaults to true.\n\n  this.emitClose = options.emitClose !== false; // Should .destroy() be called after 'end' (and potentially 'finish')\n\n  this.autoDestroy = !!options.autoDestroy; // has it been destroyed\n\n  this.destroyed = false; // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n\n  this.defaultEncoding = options.defaultEncoding || 'utf8'; // the number of writers that are awaiting a drain event in .pipe()s\n\n  this.awaitDrain = 0; // if true, a maybeReadMore has been scheduled\n\n  this.readingMore = false;\n  this.decoder = null;\n  this.encoding = null;\n\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\n\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n  if (!(this instanceof Readable)) return new Readable(options); // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the ReadableState constructor, at least with V8 6.5\n\n  var isDuplex = this instanceof Duplex;\n  this._readableState = new ReadableState(options, this, isDuplex); // legacy\n\n  this.readable = true;\n\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n\n  Stream.call(this);\n}\n\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined) {\n      return false;\n    }\n\n    return this._readableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    } // backward compatibility, the user is explicitly\n    // managing destroyed\n\n\n    this._readableState.destroyed = value;\n  }\n});\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\n\nReadable.prototype._destroy = function (err, cb) {\n  cb(err);\n}; // Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\n\n\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n}; // Unshift should *always* be something directly out of read()\n\n\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\n\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  debug('readableAddChunk', chunk);\n  var state = stream._readableState;\n\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n\n    if (er) {\n      errorOrDestroy(stream, er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n\n      if (addToFront) {\n        if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT());else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF());\n      } else if (state.destroyed) {\n        return false;\n      } else {\n        state.reading = false;\n\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n      maybeReadMore(stream, state);\n    }\n  } // We can push more data if we are below the highWaterMark.\n  // Also, if we have no data yet, we can stand some more bytes.\n  // This is to work around cases where hwm=0, such as the repl.\n\n\n  return !state.ended && (state.length < state.highWaterMark || state.length === 0);\n}\n\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    state.awaitDrain = 0;\n    stream.emit('data', chunk);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n    if (state.needReadable) emitReadable(stream);\n  }\n\n  maybeReadMore(stream, state);\n}\n\nfunction chunkInvalid(state, chunk) {\n  var er;\n\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk);\n  }\n\n  return er;\n}\n\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n}; // backwards compatibility.\n\n\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  var decoder = new StringDecoder(enc);\n  this._readableState.decoder = decoder; // If setEncoding(null), decoder.encoding equals utf8\n\n  this._readableState.encoding = this._readableState.decoder.encoding; // Iterate over current buffer to convert already stored Buffers:\n\n  var p = this._readableState.buffer.head;\n  var content = '';\n\n  while (p !== null) {\n    content += decoder.write(p.data);\n    p = p.next;\n  }\n\n  this._readableState.buffer.clear();\n\n  if (content !== '') this._readableState.buffer.push(content);\n  this._readableState.length = content.length;\n  return this;\n}; // Don't raise the hwm > 1GB\n\n\nvar MAX_HWM = 0x40000000;\n\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    // TODO(ronag): Throw ERR_VALUE_OUT_OF_RANGE.\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n\n  return n;\n} // This function is designed to be inlinable, so please take care when making\n// changes to the function body.\n\n\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  } // If we're asking for more than the current hwm, then raise the hwm.\n\n\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n; // Don't have enough\n\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n\n  return state.length;\n} // you can override either this method, or the async _read(n) below.\n\n\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n  if (n !== 0) state.emittedReadable = false; // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n\n  if (n === 0 && state.needReadable && ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n\n  n = howMuchToRead(n, state); // if we've ended, and we're now clear, then finish it up.\n\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  } // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n  // if we need a readable event, then we need to do some reading.\n\n\n  var doRead = state.needReadable;\n  debug('need readable', doRead); // if we currently have less than the highWaterMark, then also read some\n\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  } // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n\n\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true; // if the length is currently zero, then we *need* a readable event.\n\n    if (state.length === 0) state.needReadable = true; // call internal read method\n\n    this._read(state.highWaterMark);\n\n    state.sync = false; // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n\n  if (ret === null) {\n    state.needReadable = state.length <= state.highWaterMark;\n    n = 0;\n  } else {\n    state.length -= n;\n    state.awaitDrain = 0;\n  }\n\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true; // If we tried to read() past the EOF, then emit end on the next tick.\n\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n\n  if (ret !== null) this.emit('data', ret);\n  return ret;\n};\n\nfunction onEofChunk(stream, state) {\n  debug('onEofChunk');\n  if (state.ended) return;\n\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n\n  state.ended = true;\n\n  if (state.sync) {\n    // if we are sync, wait until next tick to emit the data.\n    // Otherwise we risk emitting data in the flow()\n    // the readable code triggers during a read() call\n    emitReadable(stream);\n  } else {\n    // emit 'readable' now to make sure it gets picked up.\n    state.needReadable = false;\n\n    if (!state.emittedReadable) {\n      state.emittedReadable = true;\n      emitReadable_(stream);\n    }\n  }\n} // Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\n\n\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  debug('emitReadable', state.needReadable, state.emittedReadable);\n  state.needReadable = false;\n\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    process.nextTick(emitReadable_, stream);\n  }\n}\n\nfunction emitReadable_(stream) {\n  var state = stream._readableState;\n  debug('emitReadable_', state.destroyed, state.length, state.ended);\n\n  if (!state.destroyed && (state.length || state.ended)) {\n    stream.emit('readable');\n    state.emittedReadable = false;\n  } // The stream needs another readable event if\n  // 1. It is not flowing, as the flow mechanism will take\n  //    care of it.\n  // 2. It is not ended.\n  // 3. It is below the highWaterMark, so we can schedule\n  //    another readable later.\n\n\n  state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark;\n  flow(stream);\n} // at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\n\n\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    process.nextTick(maybeReadMore_, stream, state);\n  }\n}\n\nfunction maybeReadMore_(stream, state) {\n  // Attempt to read more data if we should.\n  //\n  // The conditions for reading more data are (one of):\n  // - Not enough data buffered (state.length < state.highWaterMark). The loop\n  //   is responsible for filling the buffer with enough data if such data\n  //   is available. If highWaterMark is 0 and we are not in the flowing mode\n  //   we should _not_ attempt to buffer any extra data. We'll get more data\n  //   when the stream consumer calls read() instead.\n  // - No data in the buffer, and the stream is in flowing mode. In this mode\n  //   the loop below is responsible for ensuring read() is called. Failing to\n  //   call read here would abort the flow and there's no other mechanism for\n  //   continuing the flow if the stream consumer has just subscribed to the\n  //   'data' event.\n  //\n  // In addition to the above conditions to keep reading data, the following\n  // conditions prevent the data from being read:\n  // - The stream has ended (state.ended).\n  // - There is already a pending 'read' operation (state.reading). This is a\n  //   case where the the stream has called the implementation defined _read()\n  //   method, but they are processing the call asynchronously and have _not_\n  //   called push() with new data. In this case we skip performing more\n  //   read()s. The execution ends in this method again after the _read() ends\n  //   up calling push() with more data.\n  while (!state.reading && !state.ended && (state.length < state.highWaterMark || state.flowing && state.length === 0)) {\n    var len = state.length;\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length) // didn't get any data, stop spinning.\n      break;\n  }\n\n  state.readingMore = false;\n} // abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\n\n\nReadable.prototype._read = function (n) {\n  errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'));\n};\n\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) process.nextTick(endFn);else src.once('end', endFn);\n  dest.on('unpipe', onunpipe);\n\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n\n  function onend() {\n    debug('onend');\n    dest.end();\n  } // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n\n\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n  var cleanedUp = false;\n\n  function cleanup() {\n    debug('cleanup'); // cleanup event handlers once the pipe is broken\n\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n    cleanedUp = true; // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n\n  src.on('data', ondata);\n\n  function ondata(chunk) {\n    debug('ondata');\n    var ret = dest.write(chunk);\n    debug('dest.write', ret);\n\n    if (ret === false) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n      }\n\n      src.pause();\n    }\n  } // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n\n\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er);\n  } // Make sure our error handler is attached before userland ones.\n\n\n  prependListener(dest, 'error', onerror); // Both close and finish should trigger unpipe, but only once.\n\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n\n  dest.once('close', onclose);\n\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n\n  dest.once('finish', onfinish);\n\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  } // tell the dest that it's being piped to\n\n\n  dest.emit('pipe', src); // start the flow if it hasn't been started already.\n\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n\n  return dest;\n};\n\nfunction pipeOnDrain(src) {\n  return function pipeOnDrainFunctionResult() {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\n\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = {\n    hasUnpiped: false\n  }; // if we're not piping anywhere, then do nothing.\n\n  if (state.pipesCount === 0) return this; // just one destination.  most common case.\n\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n    if (!dest) dest = state.pipes; // got a match.\n\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  } // slow case. multiple pipe destinations.\n\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n\n    for (var i = 0; i < len; i++) {\n      dests[i].emit('unpipe', this, {\n        hasUnpiped: false\n      });\n    }\n\n    return this;\n  } // try to find the right one.\n\n\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n  dest.emit('unpipe', this, unpipeInfo);\n  return this;\n}; // set up data events if they are asked for\n// Ensure readable listeners eventually get something\n\n\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n  var state = this._readableState;\n\n  if (ev === 'data') {\n    // update readableListening so that resume() may be a no-op\n    // a few lines down. This is needed to support once('readable').\n    state.readableListening = this.listenerCount('readable') > 0; // Try start flowing on next tick if stream isn't explicitly paused\n\n    if (state.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.flowing = false;\n      state.emittedReadable = false;\n      debug('on readable', state.length, state.reading);\n\n      if (state.length) {\n        emitReadable(this);\n      } else if (!state.reading) {\n        process.nextTick(nReadingNextTick, this);\n      }\n    }\n  }\n\n  return res;\n};\n\nReadable.prototype.addListener = Readable.prototype.on;\n\nReadable.prototype.removeListener = function (ev, fn) {\n  var res = Stream.prototype.removeListener.call(this, ev, fn);\n\n  if (ev === 'readable') {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n\n  return res;\n};\n\nReadable.prototype.removeAllListeners = function (ev) {\n  var res = Stream.prototype.removeAllListeners.apply(this, arguments);\n\n  if (ev === 'readable' || ev === undefined) {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n\n  return res;\n};\n\nfunction updateReadableListening(self) {\n  var state = self._readableState;\n  state.readableListening = self.listenerCount('readable') > 0;\n\n  if (state.resumeScheduled && !state.paused) {\n    // flowing needs to be set to true now, otherwise\n    // the upcoming resume will not flow.\n    state.flowing = true; // crude way to check if we should resume\n  } else if (self.listenerCount('data') > 0) {\n    self.resume();\n  }\n}\n\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n} // pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\n\n\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n\n  if (!state.flowing) {\n    debug('resume'); // we flow only if there is no one listening\n    // for readable, but we still have to call\n    // resume()\n\n    state.flowing = !state.readableListening;\n    resume(this, state);\n  }\n\n  state.paused = false;\n  return this;\n};\n\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    process.nextTick(resume_, stream, state);\n  }\n}\n\nfunction resume_(stream, state) {\n  debug('resume', state.reading);\n\n  if (!state.reading) {\n    stream.read(0);\n  }\n\n  state.resumeScheduled = false;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\n\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n\n  if (this._readableState.flowing !== false) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n\n  this._readableState.paused = true;\n  return this;\n};\n\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n\n  while (state.flowing && stream.read() !== null) {\n    ;\n  }\n} // wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\n\n\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n\n  var state = this._readableState;\n  var paused = false;\n  stream.on('end', function () {\n    debug('wrapped end');\n\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n\n    _this.push(null);\n  });\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk); // don't skip over falsy values in objectMode\n\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n\n    var ret = _this.push(chunk);\n\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  }); // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function methodWrap(method) {\n        return function methodWrapReturnFunction() {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  } // proxy certain important events.\n\n\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  } // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n\n\n  this._read = function (n) {\n    debug('wrapped _read', n);\n\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n\n  return this;\n};\n\nif (typeof Symbol === 'function') {\n  Readable.prototype[Symbol.asyncIterator] = function () {\n    if (createReadableStreamAsyncIterator === undefined) {\n      createReadableStreamAsyncIterator = require('./internal/streams/async_iterator');\n    }\n\n    return createReadableStreamAsyncIterator(this);\n  };\n}\n\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.highWaterMark;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState && this._readableState.buffer;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableFlowing', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.flowing;\n  },\n  set: function set(state) {\n    if (this._readableState) {\n      this._readableState.flowing = state;\n    }\n  }\n}); // exposed for testing purposes only.\n\nReadable._fromList = fromList;\nObject.defineProperty(Readable.prototype, 'readableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.length;\n  }\n}); // Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\n\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.first();else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = state.buffer.consume(n, state.decoder);\n  }\n  return ret;\n}\n\nfunction endReadable(stream) {\n  var state = stream._readableState;\n  debug('endReadable', state.endEmitted);\n\n  if (!state.endEmitted) {\n    state.ended = true;\n    process.nextTick(endReadableNT, state, stream);\n  }\n}\n\nfunction endReadableNT(state, stream) {\n  debug('endReadableNT', state.endEmitted, state.length); // Check that we didn't get one last unshift.\n\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n\n    if (state.autoDestroy) {\n      // In case of duplex streams we need a way to detect\n      // if the writable side is ready for autoDestroy as well\n      var wState = stream._writableState;\n\n      if (!wState || wState.autoDestroy && wState.finished) {\n        stream.destroy();\n      }\n    }\n  }\n}\n\nif (typeof Symbol === 'function') {\n  Readable.from = function (iterable, opts) {\n    if (from === undefined) {\n      from = require('./internal/streams/from');\n    }\n\n    return from(Readable, iterable, opts);\n  };\n}\n\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n\n  return -1;\n}", "module.exports = require('stream');\n", "'use strict';\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nvar _require = require('buffer'),\n    Buffer = _require.Buffer;\n\nvar _require2 = require('util'),\n    inspect = _require2.inspect;\n\nvar custom = inspect && inspect.custom || 'inspect';\n\nfunction copyBuffer(src, target, offset) {\n  Buffer.prototype.copy.call(src, target, offset);\n}\n\nmodule.exports =\n/*#__PURE__*/\nfunction () {\n  function BufferList() {\n    _classCallCheck(this, BufferList);\n\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  _createClass(BufferList, [{\n    key: \"push\",\n    value: function push(v) {\n      var entry = {\n        data: v,\n        next: null\n      };\n      if (this.length > 0) this.tail.next = entry;else this.head = entry;\n      this.tail = entry;\n      ++this.length;\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(v) {\n      var entry = {\n        data: v,\n        next: this.head\n      };\n      if (this.length === 0) this.tail = entry;\n      this.head = entry;\n      ++this.length;\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      if (this.length === 0) return;\n      var ret = this.head.data;\n      if (this.length === 1) this.head = this.tail = null;else this.head = this.head.next;\n      --this.length;\n      return ret;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = this.tail = null;\n      this.length = 0;\n    }\n  }, {\n    key: \"join\",\n    value: function join(s) {\n      if (this.length === 0) return '';\n      var p = this.head;\n      var ret = '' + p.data;\n\n      while (p = p.next) {\n        ret += s + p.data;\n      }\n\n      return ret;\n    }\n  }, {\n    key: \"concat\",\n    value: function concat(n) {\n      if (this.length === 0) return Buffer.alloc(0);\n      var ret = Buffer.allocUnsafe(n >>> 0);\n      var p = this.head;\n      var i = 0;\n\n      while (p) {\n        copyBuffer(p.data, ret, i);\n        i += p.data.length;\n        p = p.next;\n      }\n\n      return ret;\n    } // Consumes a specified amount of bytes or characters from the buffered data.\n\n  }, {\n    key: \"consume\",\n    value: function consume(n, hasStrings) {\n      var ret;\n\n      if (n < this.head.data.length) {\n        // `slice` is the same for buffers and strings.\n        ret = this.head.data.slice(0, n);\n        this.head.data = this.head.data.slice(n);\n      } else if (n === this.head.data.length) {\n        // First chunk is a perfect match.\n        ret = this.shift();\n      } else {\n        // Result spans more than one buffer.\n        ret = hasStrings ? this._getString(n) : this._getBuffer(n);\n      }\n\n      return ret;\n    }\n  }, {\n    key: \"first\",\n    value: function first() {\n      return this.head.data;\n    } // Consumes a specified amount of characters from the buffered data.\n\n  }, {\n    key: \"_getString\",\n    value: function _getString(n) {\n      var p = this.head;\n      var c = 1;\n      var ret = p.data;\n      n -= ret.length;\n\n      while (p = p.next) {\n        var str = p.data;\n        var nb = n > str.length ? str.length : n;\n        if (nb === str.length) ret += str;else ret += str.slice(0, n);\n        n -= nb;\n\n        if (n === 0) {\n          if (nb === str.length) {\n            ++c;\n            if (p.next) this.head = p.next;else this.head = this.tail = null;\n          } else {\n            this.head = p;\n            p.data = str.slice(nb);\n          }\n\n          break;\n        }\n\n        ++c;\n      }\n\n      this.length -= c;\n      return ret;\n    } // Consumes a specified amount of bytes from the buffered data.\n\n  }, {\n    key: \"_getBuffer\",\n    value: function _getBuffer(n) {\n      var ret = Buffer.allocUnsafe(n);\n      var p = this.head;\n      var c = 1;\n      p.data.copy(ret);\n      n -= p.data.length;\n\n      while (p = p.next) {\n        var buf = p.data;\n        var nb = n > buf.length ? buf.length : n;\n        buf.copy(ret, ret.length - n, 0, nb);\n        n -= nb;\n\n        if (n === 0) {\n          if (nb === buf.length) {\n            ++c;\n            if (p.next) this.head = p.next;else this.head = this.tail = null;\n          } else {\n            this.head = p;\n            p.data = buf.slice(nb);\n          }\n\n          break;\n        }\n\n        ++c;\n      }\n\n      this.length -= c;\n      return ret;\n    } // Make sure the linked list only shows the minimal necessary information.\n\n  }, {\n    key: custom,\n    value: function value(_, options) {\n      return inspect(this, _objectSpread({}, options, {\n        // Only inspect one level.\n        depth: 0,\n        // It should not recurse.\n        customInspect: false\n      }));\n    }\n  }]);\n\n  return BufferList;\n}();", "'use strict'; // undocumented cb() API, needed for core, not for public API\n\nfunction destroy(err, cb) {\n  var _this = this;\n\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        process.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorNT, this, err);\n      }\n    }\n\n    return this;\n  } // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  } // if this is a duplex stream mark the writable part as destroyed as well\n\n\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else {\n        process.nextTick(emitCloseNT, _this);\n      }\n    } else if (cb) {\n      process.nextTick(emitCloseNT, _this);\n      cb(err);\n    } else {\n      process.nextTick(emitCloseNT, _this);\n    }\n  });\n\n  return this;\n}\n\nfunction emitErrorAndCloseNT(self, err) {\n  emitErrorNT(self, err);\n  emitCloseNT(self);\n}\n\nfunction emitCloseNT(self) {\n  if (self._writableState && !self._writableState.emitClose) return;\n  if (self._readableState && !self._readableState.emitClose) return;\n  self.emit('close');\n}\n\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\n\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\n\nfunction errorOrDestroy(stream, err) {\n  // We have tests that rely on errors being emitted\n  // in the same tick, so changing this is semver major.\n  // For now when you opt-in to autoDestroy we allow\n  // the error to be emitted nextTick. In a future\n  // semver major update we should change the default to this.\n  var rState = stream._readableState;\n  var wState = stream._writableState;\n  if (rState && rState.autoDestroy || wState && wState.autoDestroy) stream.destroy(err);else stream.emit('error', err);\n}\n\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy,\n  errorOrDestroy: errorOrDestroy\n};", "'use strict';\n\nvar ERR_INVALID_OPT_VALUE = require('../../../errors').codes.ERR_INVALID_OPT_VALUE;\n\nfunction highWaterMarkFrom(options, isDuplex, duplexKey) {\n  return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null;\n}\n\nfunction getHighWaterMark(state, options, duplexKey, isDuplex) {\n  var hwm = highWaterMarkFrom(options, isDuplex, duplexKey);\n\n  if (hwm != null) {\n    if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {\n      var name = isDuplex ? duplexKey : 'highWaterMark';\n      throw new ERR_INVALID_OPT_VALUE(name, hwm);\n    }\n\n    return Math.floor(hwm);\n  } // Default value\n\n\n  return state.objectMode ? 16 : 16 * 1024;\n}\n\nmodule.exports = {\n  getHighWaterMark: getHighWaterMark\n};", "'use strict';\n\nconst codes = {};\n\nfunction createErrorType(code, message, Base) {\n  if (!Base) {\n    Base = Error\n  }\n\n  function getMessage (arg1, arg2, arg3) {\n    if (typeof message === 'string') {\n      return message\n    } else {\n      return message(arg1, arg2, arg3)\n    }\n  }\n\n  class NodeError extends Base {\n    constructor (arg1, arg2, arg3) {\n      super(getMessage(arg1, arg2, arg3));\n    }\n  }\n\n  NodeError.prototype.name = Base.name;\n  NodeError.prototype.code = code;\n\n  codes[code] = NodeError;\n}\n\n// https://github.com/nodejs/node/blob/v10.8.0/lib/internal/errors.js\nfunction oneOf(expected, thing) {\n  if (Array.isArray(expected)) {\n    const len = expected.length;\n    expected = expected.map((i) => String(i));\n    if (len > 2) {\n      return `one of ${thing} ${expected.slice(0, len - 1).join(', ')}, or ` +\n             expected[len - 1];\n    } else if (len === 2) {\n      return `one of ${thing} ${expected[0]} or ${expected[1]}`;\n    } else {\n      return `of ${thing} ${expected[0]}`;\n    }\n  } else {\n    return `of ${thing} ${String(expected)}`;\n  }\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith\nfunction startsWith(str, search, pos) {\n\treturn str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/endsWith\nfunction endsWith(str, search, this_len) {\n\tif (this_len === undefined || this_len > str.length) {\n\t\tthis_len = str.length;\n\t}\n\treturn str.substring(this_len - search.length, this_len) === search;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/includes\nfunction includes(str, search, start) {\n  if (typeof start !== 'number') {\n    start = 0;\n  }\n\n  if (start + search.length > str.length) {\n    return false;\n  } else {\n    return str.indexOf(search, start) !== -1;\n  }\n}\n\ncreateErrorType('ERR_INVALID_OPT_VALUE', function (name, value) {\n  return 'The value \"' + value + '\" is invalid for option \"' + name + '\"'\n}, TypeError);\ncreateErrorType('ERR_INVALID_ARG_TYPE', function (name, expected, actual) {\n  // determiner: 'must be' or 'must not be'\n  let determiner;\n  if (typeof expected === 'string' && startsWith(expected, 'not ')) {\n    determiner = 'must not be';\n    expected = expected.replace(/^not /, '');\n  } else {\n    determiner = 'must be';\n  }\n\n  let msg;\n  if (endsWith(name, ' argument')) {\n    // For cases like 'first argument'\n    msg = `The ${name} ${determiner} ${oneOf(expected, 'type')}`;\n  } else {\n    const type = includes(name, '.') ? 'property' : 'argument';\n    msg = `The \"${name}\" ${type} ${determiner} ${oneOf(expected, 'type')}`;\n  }\n\n  msg += `. Received type ${typeof actual}`;\n  return msg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF');\ncreateErrorType('ERR_METHOD_NOT_IMPLEMENTED', function (name) {\n  return 'The ' + name + ' method is not implemented'\n});\ncreateErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close');\ncreateErrorType('ERR_STREAM_DESTROYED', function (name) {\n  return 'Cannot call ' + name + ' after a stream was destroyed';\n});\ncreateErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times');\ncreateErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable');\ncreateErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end');\ncreateErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError);\ncreateErrorType('ERR_UNKNOWN_ENCODING', function (arg) {\n  return 'Unknown encoding: ' + arg\n}, TypeError);\ncreateErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event');\n\nmodule.exports.codes = codes;\n", "try {\n  var util = require('util');\n  /* istanbul ignore next */\n  if (typeof util.inherits !== 'function') throw '';\n  module.exports = util.inherits;\n} catch (e) {\n  /* istanbul ignore next */\n  module.exports = require('./inherits_browser.js');\n}\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n'use strict';\n/*<replacement>*/\n\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n\n  for (var key in obj) {\n    keys.push(key);\n  }\n\n  return keys;\n};\n/*</replacement>*/\n\n\nmodule.exports = Duplex;\n\nvar Readable = require('./_stream_readable');\n\nvar Writable = require('./_stream_writable');\n\nrequire('inherits')(Duplex, Readable);\n\n{\n  // Allow the keys array to be GC'ed.\n  var keys = objectKeys(Writable.prototype);\n\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\n\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n  Readable.call(this, options);\n  Writable.call(this, options);\n  this.allowHalfOpen = true;\n\n  if (options) {\n    if (options.readable === false) this.readable = false;\n    if (options.writable === false) this.writable = false;\n\n    if (options.allowHalfOpen === false) {\n      this.allowHalfOpen = false;\n      this.once('end', onend);\n    }\n  }\n}\n\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n}); // the no-half-open enforcer\n\nfunction onend() {\n  // If the writable side ended, then we're ok.\n  if (this._writableState.ended) return; // no more data can be written.\n  // But allow more writes to happen in this tick.\n\n  process.nextTick(onEndNT, this);\n}\n\nfunction onEndNT(self) {\n  self.end();\n}\n\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    } // backward compatibility, the user is explicitly\n    // managing destroyed\n\n\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n'use strict';\n\nmodule.exports = Writable;\n/* <replacement> */\n\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n} // It seems a linked list but it is not\n// there will be only 2 of these for each stream\n\n\nfunction CorkedRequest(state) {\n  var _this = this;\n\n  this.next = null;\n  this.entry = null;\n\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\n\n\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n/*<replacement>*/\n\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\n\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n\nvar Buffer = require('buffer').Buffer;\n\nvar OurUint8Array = global.Uint8Array || function () {};\n\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\n\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\nvar destroyImpl = require('./internal/streams/destroy');\n\nvar _require = require('./internal/streams/state'),\n    getHighWaterMark = _require.getHighWaterMark;\n\nvar _require$codes = require('../errors').codes,\n    ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n    ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n    ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE,\n    ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED,\n    ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES,\n    ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END,\n    ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING;\n\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\n\nrequire('inherits')(Writable, Stream);\n\nfunction nop() {}\n\nfunction WritableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {}; // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream,\n  // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.\n\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex; // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode; // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n\n  this.highWaterMark = getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex); // if _final has been called\n\n  this.finalCalled = false; // drain event flag.\n\n  this.needDrain = false; // at the start of calling end()\n\n  this.ending = false; // when end() has been called, and returned\n\n  this.ended = false; // when 'finish' is emitted\n\n  this.finished = false; // has it been destroyed\n\n  this.destroyed = false; // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode; // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n\n  this.defaultEncoding = options.defaultEncoding || 'utf8'; // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n\n  this.length = 0; // a flag to see when we're in the middle of a write.\n\n  this.writing = false; // when true all writes will be buffered until .uncork() call\n\n  this.corked = 0; // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n\n  this.sync = true; // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n\n  this.bufferProcessing = false; // the callback that's passed to _write(chunk,cb)\n\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  }; // the callback that the user supplies to write(chunk,encoding,cb)\n\n\n  this.writecb = null; // the amount that is being written when _write is called.\n\n  this.writelen = 0;\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null; // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n\n  this.pendingcb = 0; // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n\n  this.prefinished = false; // True if the error was already emitted and should not be thrown again\n\n  this.errorEmitted = false; // Should close be emitted on destroy. Defaults to true.\n\n  this.emitClose = options.emitClose !== false; // Should .destroy() be called after 'finish' (and potentially 'end')\n\n  this.autoDestroy = !!options.autoDestroy; // count buffered requests\n\n  this.bufferedRequestCount = 0; // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\n\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n\n  return out;\n};\n\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function writableStateBufferGetter() {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})(); // Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\n\n\nvar realHasInstance;\n\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function value(object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function realHasInstance(object) {\n    return object instanceof this;\n  };\n}\n\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex'); // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the WritableState constructor, at least with V8 6.5\n\n  var isDuplex = this instanceof Duplex;\n  if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options);\n  this._writableState = new WritableState(options, this, isDuplex); // legacy.\n\n  this.writable = true;\n\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n    if (typeof options.writev === 'function') this._writev = options.writev;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n\n  Stream.call(this);\n} // Otherwise people can pipe Writable streams, which is just wrong.\n\n\nWritable.prototype.pipe = function () {\n  errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE());\n};\n\nfunction writeAfterEnd(stream, cb) {\n  var er = new ERR_STREAM_WRITE_AFTER_END(); // TODO: defer error events consistently everywhere, not just the cb\n\n  errorOrDestroy(stream, er);\n  process.nextTick(cb, er);\n} // Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\n\n\nfunction validChunk(stream, state, chunk, cb) {\n  var er;\n\n  if (chunk === null) {\n    er = new ERR_STREAM_NULL_VALUES();\n  } else if (typeof chunk !== 'string' && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer'], chunk);\n  }\n\n  if (er) {\n    errorOrDestroy(stream, er);\n    process.nextTick(cb, er);\n    return false;\n  }\n\n  return true;\n}\n\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n  if (typeof cb !== 'function') cb = nop;\n  if (state.ending) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n  return ret;\n};\n\nWritable.prototype.cork = function () {\n  this._writableState.corked++;\n};\n\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n\n  if (state.corked) {\n    state.corked--;\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\n\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new ERR_UNKNOWN_ENCODING(encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\n\nObject.defineProperty(Writable.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\n\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n\n  return chunk;\n}\n\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n}); // if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\n\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n\n  var len = state.objectMode ? 1 : chunk.length;\n  state.length += len;\n  var ret = state.length < state.highWaterMark; // we must ensure that previous needDrain will not be reset to false.\n\n  if (!ret) state.needDrain = true;\n\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n\n  return ret;\n}\n\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'));else if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\n\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    process.nextTick(cb, er); // this can emit finish, and it will always happen\n    // after error\n\n    process.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er); // this can emit finish, but finish must\n    // always follow error\n\n    finishMaybe(stream, state);\n  }\n}\n\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\n\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n  if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK();\n  onwriteStateUpdate(state);\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state) || stream.destroyed;\n\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n\n    if (sync) {\n      process.nextTick(afterWrite, stream, state, finished, cb);\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\n\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n} // Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\n\n\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n} // if there's something in the buffer waiting, then process it\n\n\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n    var count = 0;\n    var allBuffers = true;\n\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n\n    buffer.allBuffers = allBuffers;\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish); // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--; // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n\n      if (state.writing) {\n        break;\n      }\n    }\n\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\n\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'));\n};\n\nWritable.prototype._writev = null;\n\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding); // .end() fully uncorks\n\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  } // ignore unnecessary end() calls.\n\n\n  if (!state.ending) endWritable(this, state, cb);\n  return this;\n};\n\nObject.defineProperty(Writable.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\n\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\n\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n\n    if (err) {\n      errorOrDestroy(stream, err);\n    }\n\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\n\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function' && !state.destroyed) {\n      state.pendingcb++;\n      state.finalCalled = true;\n      process.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\n\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n\n  if (need) {\n    prefinish(stream, state);\n\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n\n      if (state.autoDestroy) {\n        // In case of duplex streams we need a way to detect\n        // if the readable side is ready for autoDestroy as well\n        var rState = stream._readableState;\n\n        if (!rState || rState.autoDestroy && rState.endEmitted) {\n          stream.destroy();\n        }\n      }\n    }\n  }\n\n  return need;\n}\n\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n\n  if (cb) {\n    if (state.finished) process.nextTick(cb);else stream.once('finish', cb);\n  }\n\n  state.ended = true;\n  stream.writable = false;\n}\n\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  } // reuse the free corkReq.\n\n\n  state.corkedRequestsFree.next = corkReq;\n}\n\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._writableState === undefined) {\n      return false;\n    }\n\n    return this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    } // backward compatibility, the user is explicitly\n    // managing destroyed\n\n\n    this._writableState.destroyed = value;\n  }\n});\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\n\nWritable.prototype._destroy = function (err, cb) {\n  cb(err);\n};", "\n/**\n * For Node.js, simply re-export the core `util.deprecate` function.\n */\n\nmodule.exports = require('util').deprecate;\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\n/*</replacement>*/\n\nvar isEncoding = Buffer.isEncoding || function (encoding) {\n  encoding = '' + encoding;\n  switch (encoding && encoding.toLowerCase()) {\n    case 'hex':case 'utf8':case 'utf-8':case 'ascii':case 'binary':case 'base64':case 'ucs2':case 'ucs-2':case 'utf16le':case 'utf-16le':case 'raw':\n      return true;\n    default:\n      return false;\n  }\n};\n\nfunction _normalizeEncoding(enc) {\n  if (!enc) return 'utf8';\n  var retried;\n  while (true) {\n    switch (enc) {\n      case 'utf8':\n      case 'utf-8':\n        return 'utf8';\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return 'utf16le';\n      case 'latin1':\n      case 'binary':\n        return 'latin1';\n      case 'base64':\n      case 'ascii':\n      case 'hex':\n        return enc;\n      default:\n        if (retried) return; // undefined\n        enc = ('' + enc).toLowerCase();\n        retried = true;\n    }\n  }\n};\n\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n  var nenc = _normalizeEncoding(enc);\n  if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);\n  return nenc || enc;\n}\n\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n  this.encoding = normalizeEncoding(encoding);\n  var nb;\n  switch (this.encoding) {\n    case 'utf16le':\n      this.text = utf16Text;\n      this.end = utf16End;\n      nb = 4;\n      break;\n    case 'utf8':\n      this.fillLast = utf8FillLast;\n      nb = 4;\n      break;\n    case 'base64':\n      this.text = base64Text;\n      this.end = base64End;\n      nb = 3;\n      break;\n    default:\n      this.write = simpleWrite;\n      this.end = simpleEnd;\n      return;\n  }\n  this.lastNeed = 0;\n  this.lastTotal = 0;\n  this.lastChar = Buffer.allocUnsafe(nb);\n}\n\nStringDecoder.prototype.write = function (buf) {\n  if (buf.length === 0) return '';\n  var r;\n  var i;\n  if (this.lastNeed) {\n    r = this.fillLast(buf);\n    if (r === undefined) return '';\n    i = this.lastNeed;\n    this.lastNeed = 0;\n  } else {\n    i = 0;\n  }\n  if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n  return r || '';\n};\n\nStringDecoder.prototype.end = utf8End;\n\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function (buf) {\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n  this.lastNeed -= buf.length;\n};\n\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n  if (byte <= 0x7F) return 0;else if (byte >> 5 === 0x06) return 2;else if (byte >> 4 === 0x0E) return 3;else if (byte >> 3 === 0x1E) return 4;\n  return byte >> 6 === 0x02 ? -1 : -2;\n}\n\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n  var j = buf.length - 1;\n  if (j < i) return 0;\n  var nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 1;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 2;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) {\n      if (nb === 2) nb = 0;else self.lastNeed = nb - 3;\n    }\n    return nb;\n  }\n  return 0;\n}\n\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n  if ((buf[0] & 0xC0) !== 0x80) {\n    self.lastNeed = 0;\n    return '\\ufffd';\n  }\n  if (self.lastNeed > 1 && buf.length > 1) {\n    if ((buf[1] & 0xC0) !== 0x80) {\n      self.lastNeed = 1;\n      return '\\ufffd';\n    }\n    if (self.lastNeed > 2 && buf.length > 2) {\n      if ((buf[2] & 0xC0) !== 0x80) {\n        self.lastNeed = 2;\n        return '\\ufffd';\n      }\n    }\n  }\n}\n\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n  var p = this.lastTotal - this.lastNeed;\n  var r = utf8CheckExtraBytes(this, buf, p);\n  if (r !== undefined) return r;\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, p, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, p, 0, buf.length);\n  this.lastNeed -= buf.length;\n}\n\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n  var total = utf8CheckIncomplete(this, buf, i);\n  if (!this.lastNeed) return buf.toString('utf8', i);\n  this.lastTotal = total;\n  var end = buf.length - (total - this.lastNeed);\n  buf.copy(this.lastChar, 0, end);\n  return buf.toString('utf8', i, end);\n}\n\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + '\\ufffd';\n  return r;\n}\n\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n  if ((buf.length - i) % 2 === 0) {\n    var r = buf.toString('utf16le', i);\n    if (r) {\n      var c = r.charCodeAt(r.length - 1);\n      if (c >= 0xD800 && c <= 0xDBFF) {\n        this.lastNeed = 2;\n        this.lastTotal = 4;\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n        return r.slice(0, -1);\n      }\n    }\n    return r;\n  }\n  this.lastNeed = 1;\n  this.lastTotal = 2;\n  this.lastChar[0] = buf[buf.length - 1];\n  return buf.toString('utf16le', i, buf.length - 1);\n}\n\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) {\n    var end = this.lastTotal - this.lastNeed;\n    return r + this.lastChar.toString('utf16le', 0, end);\n  }\n  return r;\n}\n\nfunction base64Text(buf, i) {\n  var n = (buf.length - i) % 3;\n  if (n === 0) return buf.toString('base64', i);\n  this.lastNeed = 3 - n;\n  this.lastTotal = 3;\n  if (n === 1) {\n    this.lastChar[0] = buf[buf.length - 1];\n  } else {\n    this.lastChar[0] = buf[buf.length - 2];\n    this.lastChar[1] = buf[buf.length - 1];\n  }\n  return buf.toString('base64', i, buf.length - n);\n}\n\nfunction base64End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);\n  return r;\n}\n\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n  return buf.toString(this.encoding);\n}\n\nfunction simpleEnd(buf) {\n  return buf && buf.length ? this.write(buf) : '';\n}", "/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n", "'use strict';\n\nvar _Object$setPrototypeO;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar finished = require('./end-of-stream');\n\nvar kLastResolve = Symbol('lastResolve');\nvar kLastReject = Symbol('lastReject');\nvar kError = Symbol('error');\nvar kEnded = Symbol('ended');\nvar kLastPromise = Symbol('lastPromise');\nvar kHandlePromise = Symbol('handlePromise');\nvar kStream = Symbol('stream');\n\nfunction createIterResult(value, done) {\n  return {\n    value: value,\n    done: done\n  };\n}\n\nfunction readAndResolve(iter) {\n  var resolve = iter[kLastResolve];\n\n  if (resolve !== null) {\n    var data = iter[kStream].read(); // we defer if data is null\n    // we can be expecting either 'end' or\n    // 'error'\n\n    if (data !== null) {\n      iter[kLastPromise] = null;\n      iter[kLastResolve] = null;\n      iter[kLastReject] = null;\n      resolve(createIterResult(data, false));\n    }\n  }\n}\n\nfunction onReadable(iter) {\n  // we wait for the next tick, because it might\n  // emit an error with process.nextTick\n  process.nextTick(readAndResolve, iter);\n}\n\nfunction wrapForNext(lastPromise, iter) {\n  return function (resolve, reject) {\n    lastPromise.then(function () {\n      if (iter[kEnded]) {\n        resolve(createIterResult(undefined, true));\n        return;\n      }\n\n      iter[kHandlePromise](resolve, reject);\n    }, reject);\n  };\n}\n\nvar AsyncIteratorPrototype = Object.getPrototypeOf(function () {});\nvar ReadableStreamAsyncIteratorPrototype = Object.setPrototypeOf((_Object$setPrototypeO = {\n  get stream() {\n    return this[kStream];\n  },\n\n  next: function next() {\n    var _this = this;\n\n    // if we have detected an error in the meanwhile\n    // reject straight away\n    var error = this[kError];\n\n    if (error !== null) {\n      return Promise.reject(error);\n    }\n\n    if (this[kEnded]) {\n      return Promise.resolve(createIterResult(undefined, true));\n    }\n\n    if (this[kStream].destroyed) {\n      // We need to defer via nextTick because if .destroy(err) is\n      // called, the error will be emitted via nextTick, and\n      // we cannot guarantee that there is no error lingering around\n      // waiting to be emitted.\n      return new Promise(function (resolve, reject) {\n        process.nextTick(function () {\n          if (_this[kError]) {\n            reject(_this[kError]);\n          } else {\n            resolve(createIterResult(undefined, true));\n          }\n        });\n      });\n    } // if we have multiple next() calls\n    // we will wait for the previous Promise to finish\n    // this logic is optimized to support for await loops,\n    // where next() is only called once at a time\n\n\n    var lastPromise = this[kLastPromise];\n    var promise;\n\n    if (lastPromise) {\n      promise = new Promise(wrapForNext(lastPromise, this));\n    } else {\n      // fast path needed to support multiple this.push()\n      // without triggering the next() queue\n      var data = this[kStream].read();\n\n      if (data !== null) {\n        return Promise.resolve(createIterResult(data, false));\n      }\n\n      promise = new Promise(this[kHandlePromise]);\n    }\n\n    this[kLastPromise] = promise;\n    return promise;\n  }\n}, _defineProperty(_Object$setPrototypeO, Symbol.asyncIterator, function () {\n  return this;\n}), _defineProperty(_Object$setPrototypeO, \"return\", function _return() {\n  var _this2 = this;\n\n  // destroy(err, cb) is a private API\n  // we can guarantee we have that here, because we control the\n  // Readable class this is attached to\n  return new Promise(function (resolve, reject) {\n    _this2[kStream].destroy(null, function (err) {\n      if (err) {\n        reject(err);\n        return;\n      }\n\n      resolve(createIterResult(undefined, true));\n    });\n  });\n}), _Object$setPrototypeO), AsyncIteratorPrototype);\n\nvar createReadableStreamAsyncIterator = function createReadableStreamAsyncIterator(stream) {\n  var _Object$create;\n\n  var iterator = Object.create(ReadableStreamAsyncIteratorPrototype, (_Object$create = {}, _defineProperty(_Object$create, kStream, {\n    value: stream,\n    writable: true\n  }), _defineProperty(_Object$create, kLastResolve, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kLastReject, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kError, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kEnded, {\n    value: stream._readableState.endEmitted,\n    writable: true\n  }), _defineProperty(_Object$create, kHandlePromise, {\n    value: function value(resolve, reject) {\n      var data = iterator[kStream].read();\n\n      if (data) {\n        iterator[kLastPromise] = null;\n        iterator[kLastResolve] = null;\n        iterator[kLastReject] = null;\n        resolve(createIterResult(data, false));\n      } else {\n        iterator[kLastResolve] = resolve;\n        iterator[kLastReject] = reject;\n      }\n    },\n    writable: true\n  }), _Object$create));\n  iterator[kLastPromise] = null;\n  finished(stream, function (err) {\n    if (err && err.code !== 'ERR_STREAM_PREMATURE_CLOSE') {\n      var reject = iterator[kLastReject]; // reject if we are waiting for data in the Promise\n      // returned by next() and store the error\n\n      if (reject !== null) {\n        iterator[kLastPromise] = null;\n        iterator[kLastResolve] = null;\n        iterator[kLastReject] = null;\n        reject(err);\n      }\n\n      iterator[kError] = err;\n      return;\n    }\n\n    var resolve = iterator[kLastResolve];\n\n    if (resolve !== null) {\n      iterator[kLastPromise] = null;\n      iterator[kLastResolve] = null;\n      iterator[kLastReject] = null;\n      resolve(createIterResult(undefined, true));\n    }\n\n    iterator[kEnded] = true;\n  });\n  stream.on('readable', onReadable.bind(null, iterator));\n  return iterator;\n};\n\nmodule.exports = createReadableStreamAsyncIterator;", "// Ported from https://github.com/mafintosh/end-of-stream with\n// permission from the author, <PERSON> (@mafintosh).\n'use strict';\n\nvar ERR_STREAM_PREMATURE_CLOSE = require('../../../errors').codes.ERR_STREAM_PREMATURE_CLOSE;\n\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    callback.apply(this, args);\n  };\n}\n\nfunction noop() {}\n\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\n\nfunction eos(stream, opts, callback) {\n  if (typeof opts === 'function') return eos(stream, null, opts);\n  if (!opts) opts = {};\n  callback = once(callback || noop);\n  var readable = opts.readable || opts.readable !== false && stream.readable;\n  var writable = opts.writable || opts.writable !== false && stream.writable;\n\n  var onlegacyfinish = function onlegacyfinish() {\n    if (!stream.writable) onfinish();\n  };\n\n  var writableEnded = stream._writableState && stream._writableState.finished;\n\n  var onfinish = function onfinish() {\n    writable = false;\n    writableEnded = true;\n    if (!readable) callback.call(stream);\n  };\n\n  var readableEnded = stream._readableState && stream._readableState.endEmitted;\n\n  var onend = function onend() {\n    readable = false;\n    readableEnded = true;\n    if (!writable) callback.call(stream);\n  };\n\n  var onerror = function onerror(err) {\n    callback.call(stream, err);\n  };\n\n  var onclose = function onclose() {\n    var err;\n\n    if (readable && !readableEnded) {\n      if (!stream._readableState || !stream._readableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n\n    if (writable && !writableEnded) {\n      if (!stream._writableState || !stream._writableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n  };\n\n  var onrequest = function onrequest() {\n    stream.req.on('finish', onfinish);\n  };\n\n  if (isRequest(stream)) {\n    stream.on('complete', onfinish);\n    stream.on('abort', onclose);\n    if (stream.req) onrequest();else stream.on('request', onrequest);\n  } else if (writable && !stream._writableState) {\n    // legacy streams\n    stream.on('end', onlegacyfinish);\n    stream.on('close', onlegacyfinish);\n  }\n\n  stream.on('end', onend);\n  stream.on('finish', onfinish);\n  if (opts.error !== false) stream.on('error', onerror);\n  stream.on('close', onclose);\n  return function () {\n    stream.removeListener('complete', onfinish);\n    stream.removeListener('abort', onclose);\n    stream.removeListener('request', onrequest);\n    if (stream.req) stream.req.removeListener('finish', onfinish);\n    stream.removeListener('end', onlegacyfinish);\n    stream.removeListener('close', onlegacyfinish);\n    stream.removeListener('finish', onfinish);\n    stream.removeListener('end', onend);\n    stream.removeListener('error', onerror);\n    stream.removeListener('close', onclose);\n  };\n}\n\nmodule.exports = eos;", "'use strict';\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar ERR_INVALID_ARG_TYPE = require('../../../errors').codes.ERR_INVALID_ARG_TYPE;\n\nfunction from(Readable, iterable, opts) {\n  var iterator;\n\n  if (iterable && typeof iterable.next === 'function') {\n    iterator = iterable;\n  } else if (iterable && iterable[Symbol.asyncIterator]) iterator = iterable[Symbol.asyncIterator]();else if (iterable && iterable[Symbol.iterator]) iterator = iterable[Symbol.iterator]();else throw new ERR_INVALID_ARG_TYPE('iterable', ['Iterable'], iterable);\n\n  var readable = new Readable(_objectSpread({\n    objectMode: true\n  }, opts)); // Reading boolean to protect against _read\n  // being called before last iteration completion.\n\n  var reading = false;\n\n  readable._read = function () {\n    if (!reading) {\n      reading = true;\n      next();\n    }\n  };\n\n  function next() {\n    return _next2.apply(this, arguments);\n  }\n\n  function _next2() {\n    _next2 = _asyncToGenerator(function* () {\n      try {\n        var _ref = yield iterator.next(),\n            value = _ref.value,\n            done = _ref.done;\n\n        if (done) {\n          readable.push(null);\n        } else if (readable.push((yield value))) {\n          next();\n        } else {\n          reading = false;\n        }\n      } catch (err) {\n        readable.destroy(err);\n      }\n    });\n    return _next2.apply(this, arguments);\n  }\n\n  return readable;\n}\n\nmodule.exports = from;", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n'use strict';\n\nmodule.exports = Transform;\n\nvar _require$codes = require('../errors').codes,\n    ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n    ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING,\n    ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0;\n\nvar Duplex = require('./_stream_duplex');\n\nrequire('inherits')(Transform, Duplex);\n\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n  var cb = ts.writecb;\n\n  if (cb === null) {\n    return this.emit('error', new ERR_MULTIPLE_CALLBACK());\n  }\n\n  ts.writechunk = null;\n  ts.writecb = null;\n  if (data != null) // single equals check for both `null` and `undefined`\n    this.push(data);\n  cb(er);\n  var rs = this._readableState;\n  rs.reading = false;\n\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\n\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n  Duplex.call(this, options);\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  }; // start out asking for a readable event once data is transformed.\n\n  this._readableState.needReadable = true; // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n\n  this._readableState.sync = false;\n\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  } // When the writable side finishes, then flush out anything remaining.\n\n\n  this.on('prefinish', prefinish);\n}\n\nfunction prefinish() {\n  var _this = this;\n\n  if (typeof this._flush === 'function' && !this._readableState.destroyed) {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\n\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n}; // This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\n\n\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'));\n};\n\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n}; // Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\n\n\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n\n  if (ts.writechunk !== null && !ts.transforming) {\n    ts.transforming = true;\n\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\n\nTransform.prototype._destroy = function (err, cb) {\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n  });\n};\n\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n  if (data != null) // single equals check for both `null` and `undefined`\n    stream.push(data); // TODO(BridgeAR): Write a test for these two error cases\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n\n  if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0();\n  if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING();\n  return stream.push(null);\n}", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n'use strict';\n\nmodule.exports = PassThrough;\n\nvar Transform = require('./_stream_transform');\n\nrequire('inherits')(PassThrough, Transform);\n\nfunction PassThrough(options) {\n  if (!(this instanceof PassThrough)) return new PassThrough(options);\n  Transform.call(this, options);\n}\n\nPassThrough.prototype._transform = function (chunk, encoding, cb) {\n  cb(null, chunk);\n};", "// Ported from https://github.com/mafin<PERSON>h/pump with\n// permission from the author, <PERSON> (@mafintosh).\n'use strict';\n\nvar eos;\n\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    callback.apply(void 0, arguments);\n  };\n}\n\nvar _require$codes = require('../../../errors').codes,\n    ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS,\n    ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED;\n\nfunction noop(err) {\n  // Rethrow the error if it exists to avoid swallowing it\n  if (err) throw err;\n}\n\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\n\nfunction destroyer(stream, reading, writing, callback) {\n  callback = once(callback);\n  var closed = false;\n  stream.on('close', function () {\n    closed = true;\n  });\n  if (eos === undefined) eos = require('./end-of-stream');\n  eos(stream, {\n    readable: reading,\n    writable: writing\n  }, function (err) {\n    if (err) return callback(err);\n    closed = true;\n    callback();\n  });\n  var destroyed = false;\n  return function (err) {\n    if (closed) return;\n    if (destroyed) return;\n    destroyed = true; // request.destroy just do .end - .abort is what we want\n\n    if (isRequest(stream)) return stream.abort();\n    if (typeof stream.destroy === 'function') return stream.destroy();\n    callback(err || new ERR_STREAM_DESTROYED('pipe'));\n  };\n}\n\nfunction call(fn) {\n  fn();\n}\n\nfunction pipe(from, to) {\n  return from.pipe(to);\n}\n\nfunction popCallback(streams) {\n  if (!streams.length) return noop;\n  if (typeof streams[streams.length - 1] !== 'function') return noop;\n  return streams.pop();\n}\n\nfunction pipeline() {\n  for (var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++) {\n    streams[_key] = arguments[_key];\n  }\n\n  var callback = popCallback(streams);\n  if (Array.isArray(streams[0])) streams = streams[0];\n\n  if (streams.length < 2) {\n    throw new ERR_MISSING_ARGS('streams');\n  }\n\n  var error;\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1;\n    var writing = i > 0;\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err;\n      if (err) destroys.forEach(call);\n      if (reading) return;\n      destroys.forEach(call);\n      callback(error);\n    });\n  });\n  return streams.reduce(pipe);\n}\n\nmodule.exports = pipeline;", "// @flow\nimport type {FilePath, DependencySpecifier, SemverRange} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {\n  ModuleRequest,\n  PackageManager,\n  PackageInstaller,\n  InstallOptions,\n  Invalidations,\n} from './types';\nimport type {ResolveResult} from './types';\n\nimport {registerSerializableClass} from '@parcel/core';\nimport ThrowableDiagnostic, {\n  encodeJSONKeyComponent,\n  escapeMarkdown,\n  generateJSONCodeHighlights,\n  md,\n} from '@parcel/diagnostic';\nimport nativeFS from 'fs';\nimport Module from 'module';\nimport path from 'path';\nimport semver from 'semver';\n\nimport {getModuleParts} from '@parcel/utils';\nimport {getConflictingLocalDependencies} from './utils';\nimport {installPackage} from './installPackage';\nimport pkg from '../package.json';\nimport {NodeResolver} from './NodeResolver';\nimport {NodeResolverSync} from './NodeResolverSync';\n\n// There can be more than one instance of NodePackageManager, but node has only a single module cache.\n// Therefore, the resolution cache and the map of parent to child modules should also be global.\nconst cache = new Map<DependencySpecifier, ResolveResult>();\nconst children = new Map<FilePath, Set<DependencySpecifier>>();\n\n// This implements a package manager for Node by monkey patching the Node require\n// algorithm so that it uses the specified FileSystem instead of the native one.\n// It also handles installing packages when they are required if not already installed.\n// See https://github.com/nodejs/node/blob/master/lib/internal/modules/cjs/loader.js\n// for reference to Node internals.\nexport class NodePackageManager implements PackageManager {\n  fs: FileSystem;\n  projectRoot: FilePath;\n  installer: ?PackageInstaller;\n  resolver: NodeResolver;\n  syncResolver: NodeResolverSync;\n  invalidationsCache: Map<string, Invalidations> = new Map();\n\n  constructor(\n    fs: FileSystem,\n    projectRoot: FilePath,\n    installer?: ?PackageInstaller,\n  ) {\n    this.fs = fs;\n    this.projectRoot = projectRoot;\n    this.installer = installer;\n    this.resolver = new NodeResolver(this.fs, projectRoot);\n    this.syncResolver = new NodeResolverSync(this.fs, projectRoot);\n  }\n\n  static deserialize(opts: any): NodePackageManager {\n    return new NodePackageManager(opts.fs, opts.projectRoot, opts.installer);\n  }\n\n  serialize(): {|\n    $$raw: boolean,\n    fs: FileSystem,\n    projectRoot: FilePath,\n    installer: ?PackageInstaller,\n  |} {\n    return {\n      $$raw: false,\n      fs: this.fs,\n      projectRoot: this.projectRoot,\n      installer: this.installer,\n    };\n  }\n\n  async require(\n    name: DependencySpecifier,\n    from: FilePath,\n    opts: ?{|\n      range?: ?SemverRange,\n      shouldAutoInstall?: boolean,\n      saveDev?: boolean,\n    |},\n  ): Promise<any> {\n    let {resolved} = await this.resolve(name, from, opts);\n    return this.load(resolved, from);\n  }\n\n  requireSync(name: DependencySpecifier, from: FilePath): any {\n    let {resolved} = this.resolveSync(name, from);\n    return this.load(resolved, from);\n  }\n\n  load(filePath: FilePath, from: FilePath): any {\n    if (!path.isAbsolute(filePath)) {\n      // Node builtin module\n      // $FlowFixMe\n      return require(filePath);\n    }\n\n    // $FlowFixMe[prop-missing]\n    const cachedModule = Module._cache[filePath];\n    if (cachedModule !== undefined) {\n      return cachedModule.exports;\n    }\n\n    // $FlowFixMe\n    let m = new Module(filePath, Module._cache[from] || module.parent);\n    // $FlowFixMe[prop-missing]\n    Module._cache[filePath] = m;\n\n    // Patch require within this module so it goes through our require\n    m.require = id => {\n      return this.requireSync(id, filePath);\n    };\n\n    // Patch `fs.readFileSync` temporarily so that it goes through our file system\n    let readFileSync = nativeFS.readFileSync;\n    // $FlowFixMe\n    nativeFS.readFileSync = (filename, encoding) => {\n      // $FlowFixMe\n      nativeFS.readFileSync = readFileSync;\n      return this.fs.readFileSync(filename, encoding);\n    };\n\n    try {\n      m.load(filePath);\n    } catch (err) {\n      // $FlowFixMe[prop-missing]\n      delete Module._cache[filePath];\n      throw err;\n    }\n\n    return m.exports;\n  }\n\n  async resolve(\n    id: DependencySpecifier,\n    from: FilePath,\n    options?: ?{|\n      range?: ?SemverRange,\n      shouldAutoInstall?: boolean,\n      saveDev?: boolean,\n    |},\n  ): Promise<ResolveResult> {\n    let basedir = path.dirname(from);\n    let key = basedir + ':' + id;\n    let resolved = cache.get(key);\n    if (!resolved) {\n      let [name] = getModuleParts(id);\n      try {\n        resolved = await this.resolver.resolve(id, from);\n      } catch (e) {\n        if (\n          e.code !== 'MODULE_NOT_FOUND' ||\n          options?.shouldAutoInstall !== true\n        ) {\n          if (\n            e.code === 'MODULE_NOT_FOUND' &&\n            options?.shouldAutoInstall !== true\n          ) {\n            let err = new ThrowableDiagnostic({\n              diagnostic: {\n                message: escapeMarkdown(e.message),\n                hints: [\n                  'Autoinstall is disabled, please install this package manually and restart Parcel.',\n                ],\n              },\n            });\n            // $FlowFixMe - needed for loadParcelPlugin\n            err.code = 'MODULE_NOT_FOUND';\n            throw err;\n          } else {\n            throw e;\n          }\n        }\n\n        let conflicts = await getConflictingLocalDependencies(\n          this.fs,\n          name,\n          from,\n          this.projectRoot,\n        );\n\n        if (conflicts == null) {\n          await this.install([{name, range: options?.range}], from, {\n            saveDev: options?.saveDev ?? true,\n          });\n\n          return this.resolve(id, from, {\n            ...options,\n            shouldAutoInstall: false,\n          });\n        }\n\n        throw new ThrowableDiagnostic({\n          diagnostic: conflicts.fields.map(field => ({\n            message: md`Could not find module \"${name}\", but it was listed in package.json. Run your package manager first.`,\n            origin: '@parcel/package-manager',\n            codeFrames: [\n              {\n                filePath: conflicts.filePath,\n                language: 'json',\n                code: conflicts.json,\n                codeHighlights: generateJSONCodeHighlights(conflicts.json, [\n                  {\n                    key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                    type: 'key',\n                    message: 'Defined here, but not installed',\n                  },\n                ]),\n              },\n            ],\n          })),\n        });\n      }\n\n      let range = options?.range;\n      if (range != null) {\n        let pkg = resolved.pkg;\n        if (pkg == null || !semver.satisfies(pkg.version, range)) {\n          let conflicts = await getConflictingLocalDependencies(\n            this.fs,\n            name,\n            from,\n            this.projectRoot,\n          );\n\n          if (conflicts == null && options?.shouldAutoInstall === true) {\n            await this.install([{name, range}], from);\n            return this.resolve(id, from, {\n              ...options,\n              shouldAutoInstall: false,\n            });\n          } else if (conflicts != null) {\n            throw new ThrowableDiagnostic({\n              diagnostic: {\n                message: md`Could not find module \"${name}\" satisfying ${range}.`,\n                origin: '@parcel/package-manager',\n                codeFrames: [\n                  {\n                    filePath: conflicts.filePath,\n                    language: 'json',\n                    code: conflicts.json,\n                    codeHighlights: generateJSONCodeHighlights(\n                      conflicts.json,\n                      conflicts.fields.map(field => ({\n                        key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                        type: 'key',\n                        message: 'Found this conflicting local requirement.',\n                      })),\n                    ),\n                  },\n                ],\n              },\n            });\n          }\n\n          let version = pkg?.version;\n          let message = md`Could not resolve package \"${name}\" that satisfies ${range}.`;\n          if (version != null) {\n            message += md` Found ${version}.`;\n          }\n\n          throw new ThrowableDiagnostic({\n            diagnostic: {\n              message,\n              hints: [\n                'Looks like the incompatible version was installed transitively. Add this package as a direct dependency with a compatible version range.',\n              ],\n            },\n          });\n        }\n      }\n\n      cache.set(key, resolved);\n      this.invalidationsCache.clear();\n\n      // Add the specifier as a child to the parent module.\n      // Don't do this if the specifier was an absolute path, as this was likely a dynamically resolved path\n      // (e.g. babel uses require() to load .babelrc.js configs and we don't want them to be added  as children of babel itself).\n      if (!path.isAbsolute(name)) {\n        let moduleChildren = children.get(from);\n        if (!moduleChildren) {\n          moduleChildren = new Set();\n          children.set(from, moduleChildren);\n        }\n\n        moduleChildren.add(name);\n      }\n    }\n\n    return resolved;\n  }\n\n  resolveSync(name: DependencySpecifier, from: FilePath): ResolveResult {\n    let basedir = path.dirname(from);\n    let key = basedir + ':' + name;\n    let resolved = cache.get(key);\n    if (!resolved) {\n      resolved = this.syncResolver.resolve(name, from);\n      cache.set(key, resolved);\n      this.invalidationsCache.clear();\n\n      if (!path.isAbsolute(name)) {\n        let moduleChildren = children.get(from);\n        if (!moduleChildren) {\n          moduleChildren = new Set();\n          children.set(from, moduleChildren);\n        }\n\n        moduleChildren.add(name);\n      }\n    }\n\n    return resolved;\n  }\n\n  async install(\n    modules: Array<ModuleRequest>,\n    from: FilePath,\n    opts?: InstallOptions,\n  ) {\n    await installPackage(this.fs, this, modules, from, this.projectRoot, {\n      packageInstaller: this.installer,\n      ...opts,\n    });\n  }\n\n  getInvalidations(name: DependencySpecifier, from: FilePath): Invalidations {\n    let key = name + ':' + from;\n    let cached = this.invalidationsCache.get(key);\n    if (cached != null) {\n      return cached;\n    }\n\n    let res = {\n      invalidateOnFileCreate: [],\n      invalidateOnFileChange: new Set(),\n    };\n\n    let seen = new Set();\n    let addKey = (name, from) => {\n      let basedir = path.dirname(from);\n      let key = basedir + ':' + name;\n      if (seen.has(key)) {\n        return;\n      }\n\n      seen.add(key);\n      let resolved = cache.get(key);\n      if (!resolved || !path.isAbsolute(resolved.resolved)) {\n        return;\n      }\n\n      res.invalidateOnFileCreate.push(...resolved.invalidateOnFileCreate);\n      res.invalidateOnFileChange.add(resolved.resolved);\n\n      for (let file of resolved.invalidateOnFileChange) {\n        res.invalidateOnFileChange.add(file);\n      }\n\n      let moduleChildren = children.get(resolved.resolved);\n      if (moduleChildren) {\n        for (let specifier of moduleChildren) {\n          addKey(specifier, resolved.resolved);\n        }\n      }\n    };\n\n    addKey(name, from);\n    this.invalidationsCache.set(key, res);\n    return res;\n  }\n\n  invalidate(name: DependencySpecifier, from: FilePath) {\n    let seen = new Set();\n\n    let invalidate = (name, from) => {\n      let basedir = path.dirname(from);\n      let key = basedir + ':' + name;\n      if (seen.has(key)) {\n        return;\n      }\n\n      seen.add(key);\n      let resolved = cache.get(key);\n      if (!resolved || !path.isAbsolute(resolved.resolved)) {\n        return;\n      }\n\n      // $FlowFixMe\n      let module = Module._cache[resolved.resolved];\n      if (module) {\n        // $FlowFixMe\n        delete Module._cache[resolved.resolved];\n      }\n\n      let moduleChildren = children.get(resolved.resolved);\n      if (moduleChildren) {\n        for (let specifier of moduleChildren) {\n          invalidate(specifier, resolved.resolved);\n        }\n      }\n\n      children.delete(resolved.resolved);\n      cache.delete(key);\n      this.resolver.invalidate(resolved.resolved);\n      this.syncResolver.invalidate(resolved.resolved);\n    };\n\n    invalidate(name, from);\n  }\n}\n\nregisterSerializableClass(\n  `${pkg.version}:NodePackageManager`,\n  NodePackageManager,\n);\n", "// @flow strict-local\n\nimport type {ModuleRequest} from './types';\nimport type {FilePath} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\n\nimport invariant from 'assert';\nimport ThrowableDiagnostic from '@parcel/diagnostic';\nimport {resolveConfig} from '@parcel/utils';\n\nexport function npmSpecifierFromModuleRequest(\n  moduleRequest: ModuleRequest,\n): string {\n  return moduleRequest.range != null\n    ? [moduleRequest.name, moduleRequest.range].join('@')\n    : moduleRequest.name;\n}\n\nexport function moduleRequestsFromDependencyMap(dependencyMap: {|\n  [string]: string,\n|}): Array<ModuleRequest> {\n  return Object.entries(dependencyMap).map(([name, range]) => {\n    invariant(typeof range === 'string');\n    return {\n      name,\n      range,\n    };\n  });\n}\n\nexport async function getConflictingLocalDependencies(\n  fs: FileSystem,\n  name: string,\n  local: FilePath,\n  projectRoot: FilePath,\n): Promise<?{|json: string, filePath: FilePath, fields: Array<string>|}> {\n  let pkgPath = await resolveConfig(fs, local, ['package.json'], projectRoot);\n  if (pkgPath == null) {\n    return;\n  }\n\n  let pkgStr = await fs.readFile(pkgPath, 'utf8');\n  let pkg;\n  try {\n    pkg = JSON.parse(pkgStr);\n  } catch (e) {\n    // TODO: codeframe\n    throw new ThrowableDiagnostic({\n      diagnostic: {\n        message: 'Failed to parse package.json',\n        origin: '@parcel/package-manager',\n      },\n    });\n  }\n\n  if (typeof pkg !== 'object' || pkg == null) {\n    // TODO: codeframe\n    throw new ThrowableDiagnostic({\n      diagnostic: {\n        message: 'Expected package.json contents to be an object.',\n        origin: '@parcel/package-manager',\n      },\n    });\n  }\n\n  let fields = [];\n  for (let field of ['dependencies', 'devDependencies', 'peerDependencies']) {\n    if (\n      typeof pkg[field] === 'object' &&\n      pkg[field] != null &&\n      pkg[field][name] != null\n    ) {\n      fields.push(field);\n    }\n  }\n\n  if (fields.length > 0) {\n    return {\n      filePath: pkgPath,\n      json: pkgStr,\n      fields,\n    };\n  }\n}\n", "// @flow\n\nimport type {FilePath, PackageJSON} from '@parcel/types';\nimport type {\n  ModuleRequest,\n  PackageManager,\n  PackageInstaller,\n  InstallOptions,\n} from './types';\nimport type {FileSystem} from '@parcel/fs';\n\nimport invariant from 'assert';\nimport path from 'path';\nimport nullthrows from 'nullthrows';\nimport semver from 'semver';\nimport ThrowableDiagnostic, {\n  generateJSONCodeHighlights,\n  encodeJSONKeyComponent,\n  md,\n} from '@parcel/diagnostic';\nimport logger from '@parcel/logger';\nimport {loadConfig, PromiseQueue, resolveConfig} from '@parcel/utils';\nimport WorkerFarm from '@parcel/workers';\n\nimport {Npm} from './Npm';\nimport {Yarn} from './Yarn';\nimport {Pnpm} from './Pnpm.js';\nimport {getConflictingLocalDependencies} from './utils';\nimport validateModuleSpecifier from './validateModuleSpecifier';\n\nasync function install(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  from: FilePath,\n  projectRoot: FilePath,\n  options: InstallOptions = {},\n): Promise<void> {\n  let {installPeers = true, saveDev = true, packageInstaller} = options;\n  let moduleNames = modules.map(m => m.name).join(', ');\n\n  logger.progress(`Installing ${moduleNames}...`);\n\n  let fromPkgPath = await resolveConfig(\n    fs,\n    from,\n    ['package.json'],\n    projectRoot,\n  );\n  let cwd = fromPkgPath ? path.dirname(fromPkgPath) : fs.cwd();\n\n  if (!packageInstaller) {\n    packageInstaller = await determinePackageInstaller(fs, from, projectRoot);\n  }\n\n  try {\n    await packageInstaller.install({\n      modules,\n      saveDev,\n      cwd,\n      packagePath: fromPkgPath,\n      fs,\n    });\n  } catch (err) {\n    throw new Error(`Failed to install ${moduleNames}: ${err.message}`);\n  }\n\n  if (installPeers) {\n    await Promise.all(\n      modules.map(m =>\n        installPeerDependencies(\n          fs,\n          packageManager,\n          m,\n          from,\n          projectRoot,\n          options,\n        ),\n      ),\n    );\n  }\n}\n\nasync function installPeerDependencies(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  module: ModuleRequest,\n  from: FilePath,\n  projectRoot: FilePath,\n  options,\n) {\n  const {resolved} = await packageManager.resolve(module.name, from);\n  const modulePkg: PackageJSON = nullthrows(\n    await loadConfig(fs, resolved, ['package.json'], projectRoot),\n  ).config;\n  const peers = modulePkg.peerDependencies || {};\n\n  let modules: Array<ModuleRequest> = [];\n  for (let [name, range] of Object.entries(peers)) {\n    invariant(typeof range === 'string');\n\n    let conflicts = await getConflictingLocalDependencies(\n      fs,\n      name,\n      from,\n      projectRoot,\n    );\n    if (conflicts) {\n      let {pkg} = await packageManager.resolve(name, from);\n      invariant(pkg);\n      if (!semver.satisfies(pkg.version, range)) {\n        throw new ThrowableDiagnostic({\n          diagnostic: {\n            message: md`Could not install the peer dependency \"${name}\" for \"${module.name}\", installed version ${pkg.version} is incompatible with ${range}`,\n            origin: '@parcel/package-manager',\n            codeFrames: [\n              {\n                filePath: conflicts.filePath,\n                language: 'json',\n                code: conflicts.json,\n                codeHighlights: generateJSONCodeHighlights(\n                  conflicts.json,\n                  conflicts.fields.map(field => ({\n                    key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                    type: 'key',\n                    message: 'Found this conflicting local requirement.',\n                  })),\n                ),\n              },\n            ],\n          },\n        });\n      }\n\n      continue;\n    }\n    modules.push({name, range});\n  }\n\n  if (modules.length) {\n    await install(\n      fs,\n      packageManager,\n      modules,\n      from,\n      projectRoot,\n      Object.assign({}, options, {installPeers: false}),\n    );\n  }\n}\n\nasync function determinePackageInstaller(\n  fs: FileSystem,\n  filepath: FilePath,\n  projectRoot: FilePath,\n): Promise<PackageInstaller> {\n  let configFile = await resolveConfig(\n    fs,\n    filepath,\n    ['package-lock.json', 'pnpm-lock.yaml', 'yarn.lock'],\n    projectRoot,\n  );\n\n  let configName = configFile && path.basename(configFile);\n\n  // Always use the package manager that seems to be used in the project,\n  // falling back to a different one wouldn't update the existing lockfile.\n  if (configName === 'package-lock.json') {\n    return new Npm();\n  } else if (configName === 'pnpm-lock.yaml') {\n    return new Pnpm();\n  } else if (configName === 'yarn.lock') {\n    return new Yarn();\n  }\n\n  if (await Yarn.exists()) {\n    return new Yarn();\n  } else if (await Pnpm.exists()) {\n    return new Pnpm();\n  } else {\n    return new Npm();\n  }\n}\n\nlet queue = new PromiseQueue({maxConcurrent: 1});\nlet modulesInstalling: Set<string> = new Set();\n\n// Exported so that it may be invoked from the worker api below.\n// Do not call this directly! This can result in concurrent package installations\n// across multiple instances of the package manager.\nexport function _addToInstallQueue(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  filePath: FilePath,\n  projectRoot: FilePath,\n  options?: InstallOptions,\n): Promise<mixed> {\n  modules = modules.map(request => ({\n    name: validateModuleSpecifier(request.name),\n    range: request.range,\n  }));\n\n  // Wrap PromiseQueue and track modules that are currently installing.\n  // If a request comes in for a module that is currently installing, don't bother\n  // enqueuing it.\n  let modulesToInstall = modules.filter(\n    m => !modulesInstalling.has(getModuleRequestKey(m)),\n  );\n  if (modulesToInstall.length) {\n    for (let m of modulesToInstall) {\n      modulesInstalling.add(getModuleRequestKey(m));\n    }\n\n    queue\n      .add(() =>\n        install(\n          fs,\n          packageManager,\n          modulesToInstall,\n          filePath,\n          projectRoot,\n          options,\n        ).then(() => {\n          for (let m of modulesToInstall) {\n            modulesInstalling.delete(getModuleRequestKey(m));\n          }\n        }),\n      )\n      .then(\n        () => {},\n        () => {},\n      );\n  }\n\n  return queue.run();\n}\n\nexport function installPackage(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  filePath: FilePath,\n  projectRoot: FilePath,\n  options?: InstallOptions,\n): Promise<mixed> {\n  if (WorkerFarm.isWorker()) {\n    let workerApi = WorkerFarm.getWorkerApi();\n    // TODO this should really be `__filename` but without the rewriting.\n    let bundlePath =\n      process.env.PARCEL_BUILD_ENV === 'production' &&\n      !process.env.PARCEL_SELF_BUILD\n        ? path.join(__dirname, '..', 'lib/index.js')\n        : __filename;\n    return workerApi.callMaster({\n      location: bundlePath,\n      args: [fs, packageManager, modules, filePath, projectRoot, options],\n      method: '_addToInstallQueue',\n    });\n  }\n\n  return _addToInstallQueue(\n    fs,\n    packageManager,\n    modules,\n    filePath,\n    projectRoot,\n    options,\n  );\n}\n\nfunction getModuleRequestKey(moduleRequest: ModuleRequest): string {\n  return [moduleRequest.name, moduleRequest.range].join('@');\n}\n", "'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from './types';\n\nimport path from 'path';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst NPM_CMD = 'npm';\n\nexport class Npm implements PackageInstaller {\n  async install({\n    modules,\n    cwd,\n    fs,\n    packagePath,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    // npm doesn't auto-create a package.json when installing,\n    // so create an empty one if needed.\n    if (packagePath == null) {\n      await fs.writeFile(path.join(cwd, 'package.json'), '{}');\n    }\n\n    let args = ['install', '--json', saveDev ? '--save-dev' : '--save'].concat(\n      modules.map(npmSpecifierFromModuleRequest),\n    );\n\n    // When Parcel is run by npm (e.g. via package.json scripts), several environment variables are\n    // added. When parcel in turn calls npm again, these can cause npm to behave stragely, so we\n    // filter them out when installing packages.\n    let env = {};\n    for (let key in process.env) {\n      if (!key.startsWith('npm_') && key !== 'INIT_CWD' && key !== 'NODE_ENV') {\n        env[key] = process.env[key];\n      }\n    }\n\n    let installProcess = spawn(NPM_CMD, args, {cwd, env});\n    let stdout = '';\n    installProcess.stdout.on('data', (buf: Buffer) => {\n      stdout += buf.toString();\n    });\n\n    let stderr = [];\n    installProcess.stderr.on('data', (buf: Buffer) => {\n      stderr.push(buf.toString().trim());\n    });\n\n    try {\n      await promiseFromProcess(installProcess);\n\n      let results: NPMResults = JSON.parse(stdout);\n      let addedCount = results.added.length;\n      if (addedCount > 0) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message: `Added ${addedCount} packages via npm`,\n        });\n      }\n\n      // Since we succeeded, stderr might have useful information not included\n      // in the json written to stdout. It's also not necessary to log these as\n      // errors as they often aren't.\n      for (let message of stderr) {\n        if (message.length > 0) {\n          logger.log({\n            origin: '@parcel/package-manager',\n            message,\n          });\n        }\n      }\n    } catch (e) {\n      throw new Error(\n        'npm failed to install modules: ' +\n          e.message +\n          ' - ' +\n          stderr.join('\\n'),\n      );\n    }\n  }\n}\n\ntype NPMResults = {|\n  added: Array<{name: string, ...}>,\n|};\n\nregisterSerializableClass(`${pkg.version}:Npm`, Npm);\n", "'use strict';\n\nconst cp = require('child_process');\nconst parse = require('./lib/parse');\nconst enoent = require('./lib/enoent');\n\nfunction spawn(command, args, options) {\n    // Parse the arguments\n    const parsed = parse(command, args, options);\n\n    // Spawn the child process\n    const spawned = cp.spawn(parsed.command, parsed.args, parsed.options);\n\n    // Hook into child process \"exit\" event to emit an error if the command\n    // does not exists, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16\n    enoent.hookChildProcess(spawned, parsed);\n\n    return spawned;\n}\n\nfunction spawnSync(command, args, options) {\n    // Parse the arguments\n    const parsed = parse(command, args, options);\n\n    // Spawn the child process\n    const result = cp.spawnSync(parsed.command, parsed.args, parsed.options);\n\n    // Analyze if the command does not exist, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16\n    result.error = result.error || enoent.verifyENOENTSync(result.status, parsed);\n\n    return result;\n}\n\nmodule.exports = spawn;\nmodule.exports.spawn = spawn;\nmodule.exports.sync = spawnSync;\n\nmodule.exports._parse = parse;\nmodule.exports._enoent = enoent;\n", "'use strict';\n\nconst path = require('path');\nconst niceTry = require('nice-try');\nconst resolveCommand = require('./util/resolveCommand');\nconst escape = require('./util/escape');\nconst readShebang = require('./util/readShebang');\nconst semver = require('semver');\n\nconst isWin = process.platform === 'win32';\nconst isExecutableRegExp = /\\.(?:com|exe)$/i;\nconst isCmdShimRegExp = /node_modules[\\\\/].bin[\\\\/][^\\\\/]+\\.cmd$/i;\n\n// `options.shell` is supported in Node ^4.8.0, ^5.7.0 and >= 6.0.0\nconst supportsShellOption = niceTry(() => semver.satisfies(process.version, '^4.8.0 || ^5.7.0 || >= 6.0.0', true)) || false;\n\nfunction detectShebang(parsed) {\n    parsed.file = resolveCommand(parsed);\n\n    const shebang = parsed.file && readShebang(parsed.file);\n\n    if (shebang) {\n        parsed.args.unshift(parsed.file);\n        parsed.command = shebang;\n\n        return resolveCommand(parsed);\n    }\n\n    return parsed.file;\n}\n\nfunction parseNonShell(parsed) {\n    if (!isWin) {\n        return parsed;\n    }\n\n    // Detect & add support for shebangs\n    const commandFile = detectShebang(parsed);\n\n    // We don't need a shell if the command filename is an executable\n    const needsShell = !isExecutableRegExp.test(commandFile);\n\n    // If a shell is required, use cmd.exe and take care of escaping everything correctly\n    // Note that `forceShell` is an hidden option used only in tests\n    if (parsed.options.forceShell || needsShell) {\n        // Need to double escape meta chars if the command is a cmd-shim located in `node_modules/.bin/`\n        // The cmd-shim simply calls execute the package bin file with NodeJS, proxying any argument\n        // Because the escape of metachars with ^ gets interpreted when the cmd.exe is first called,\n        // we need to double escape them\n        const needsDoubleEscapeMetaChars = isCmdShimRegExp.test(commandFile);\n\n        // Normalize posix paths into OS compatible paths (e.g.: foo/bar -> foo\\bar)\n        // This is necessary otherwise it will always fail with ENOENT in those cases\n        parsed.command = path.normalize(parsed.command);\n\n        // Escape command & arguments\n        parsed.command = escape.command(parsed.command);\n        parsed.args = parsed.args.map((arg) => escape.argument(arg, needsDoubleEscapeMetaChars));\n\n        const shellCommand = [parsed.command].concat(parsed.args).join(' ');\n\n        parsed.args = ['/d', '/s', '/c', `\"${shellCommand}\"`];\n        parsed.command = process.env.comspec || 'cmd.exe';\n        parsed.options.windowsVerbatimArguments = true; // Tell node's spawn that the arguments are already escaped\n    }\n\n    return parsed;\n}\n\nfunction parseShell(parsed) {\n    // If node supports the shell option, there's no need to mimic its behavior\n    if (supportsShellOption) {\n        return parsed;\n    }\n\n    // Mimic node shell option\n    // See https://github.com/nodejs/node/blob/b9f6a2dc059a1062776133f3d4fd848c4da7d150/lib/child_process.js#L335\n    const shellCommand = [parsed.command].concat(parsed.args).join(' ');\n\n    if (isWin) {\n        parsed.command = typeof parsed.options.shell === 'string' ? parsed.options.shell : process.env.comspec || 'cmd.exe';\n        parsed.args = ['/d', '/s', '/c', `\"${shellCommand}\"`];\n        parsed.options.windowsVerbatimArguments = true; // Tell node's spawn that the arguments are already escaped\n    } else {\n        if (typeof parsed.options.shell === 'string') {\n            parsed.command = parsed.options.shell;\n        } else if (process.platform === 'android') {\n            parsed.command = '/system/bin/sh';\n        } else {\n            parsed.command = '/bin/sh';\n        }\n\n        parsed.args = ['-c', shellCommand];\n    }\n\n    return parsed;\n}\n\nfunction parse(command, args, options) {\n    // Normalize arguments, similar to nodejs\n    if (args && !Array.isArray(args)) {\n        options = args;\n        args = null;\n    }\n\n    args = args ? args.slice(0) : []; // Clone array to avoid changing the original\n    options = Object.assign({}, options); // Clone object to avoid changing the original\n\n    // Build our parsed object\n    const parsed = {\n        command,\n        args,\n        options,\n        file: undefined,\n        original: {\n            command,\n            args,\n        },\n    };\n\n    // Delegate further parsing to shell or non-shell\n    return options.shell ? parseShell(parsed) : parseNonShell(parsed);\n}\n\nmodule.exports = parse;\n", "'use strict'\n\n/**\n * Tri<PERSON> to execute a function and discards any error that occurs.\n * @param {Function} fn - Function that might or might not throw an error.\n * @returns {?*} Return-value of the function when no error occurred.\n */\nmodule.exports = function(fn) {\n\n\ttry { return fn() } catch (e) {}\n\n}", "'use strict';\n\nconst path = require('path');\nconst which = require('which');\nconst pathKey = require('path-key')();\n\nfunction resolveCommandAttempt(parsed, withoutPathExt) {\n    const cwd = process.cwd();\n    const hasCustomCwd = parsed.options.cwd != null;\n\n    // If a custom `cwd` was specified, we need to change the process cwd\n    // because `which` will do stat calls but does not support a custom cwd\n    if (hasCustomCwd) {\n        try {\n            process.chdir(parsed.options.cwd);\n        } catch (err) {\n            /* Empty */\n        }\n    }\n\n    let resolved;\n\n    try {\n        resolved = which.sync(parsed.command, {\n            path: (parsed.options.env || process.env)[pathKey],\n            pathExt: withoutPathExt ? path.delimiter : undefined,\n        });\n    } catch (e) {\n        /* Empty */\n    } finally {\n        process.chdir(cwd);\n    }\n\n    // If we successfully resolved, ensure that an absolute path is returned\n    // Note that when a custom `cwd` was used, we need to resolve to an absolute path based on it\n    if (resolved) {\n        resolved = path.resolve(hasCustomCwd ? parsed.options.cwd : '', resolved);\n    }\n\n    return resolved;\n}\n\nfunction resolveCommand(parsed) {\n    return resolveCommandAttempt(parsed) || resolveCommandAttempt(parsed, true);\n}\n\nmodule.exports = resolveCommand;\n", "module.exports = which\nwhich.sync = whichSync\n\nvar isWindows = process.platform === 'win32' ||\n    process.env.OSTYPE === 'cygwin' ||\n    process.env.OSTYPE === 'msys'\n\nvar path = require('path')\nvar COLON = isWindows ? ';' : ':'\nvar isexe = require('isexe')\n\nfunction getNotFoundError (cmd) {\n  var er = new Error('not found: ' + cmd)\n  er.code = 'ENOENT'\n\n  return er\n}\n\nfunction getPathInfo (cmd, opt) {\n  var colon = opt.colon || COLON\n  var pathEnv = opt.path || process.env.PATH || ''\n  var pathExt = ['']\n\n  pathEnv = pathEnv.split(colon)\n\n  var pathExtExe = ''\n  if (isWindows) {\n    pathEnv.unshift(process.cwd())\n    pathExtExe = (opt.pathExt || process.env.PATHEXT || '.EXE;.CMD;.BAT;.COM')\n    pathExt = pathExtExe.split(colon)\n\n\n    // Always test the cmd itself first.  isexe will check to make sure\n    // it's found in the pathExt set.\n    if (cmd.indexOf('.') !== -1 && pathExt[0] !== '')\n      pathExt.unshift('')\n  }\n\n  // If it has a slash, then we don't bother searching the pathenv.\n  // just check the file itself, and that's it.\n  if (cmd.match(/\\//) || isWindows && cmd.match(/\\\\/))\n    pathEnv = ['']\n\n  return {\n    env: pathEnv,\n    ext: pathExt,\n    extExe: pathExtExe\n  }\n}\n\nfunction which (cmd, opt, cb) {\n  if (typeof opt === 'function') {\n    cb = opt\n    opt = {}\n  }\n\n  var info = getPathInfo(cmd, opt)\n  var pathEnv = info.env\n  var pathExt = info.ext\n  var pathExtExe = info.extExe\n  var found = []\n\n  ;(function F (i, l) {\n    if (i === l) {\n      if (opt.all && found.length)\n        return cb(null, found)\n      else\n        return cb(getNotFoundError(cmd))\n    }\n\n    var pathPart = pathEnv[i]\n    if (pathPart.charAt(0) === '\"' && pathPart.slice(-1) === '\"')\n      pathPart = pathPart.slice(1, -1)\n\n    var p = path.join(pathPart, cmd)\n    if (!pathPart && (/^\\.[\\\\\\/]/).test(cmd)) {\n      p = cmd.slice(0, 2) + p\n    }\n    ;(function E (ii, ll) {\n      if (ii === ll) return F(i + 1, l)\n      var ext = pathExt[ii]\n      isexe(p + ext, { pathExt: pathExtExe }, function (er, is) {\n        if (!er && is) {\n          if (opt.all)\n            found.push(p + ext)\n          else\n            return cb(null, p + ext)\n        }\n        return E(ii + 1, ll)\n      })\n    })(0, pathExt.length)\n  })(0, pathEnv.length)\n}\n\nfunction whichSync (cmd, opt) {\n  opt = opt || {}\n\n  var info = getPathInfo(cmd, opt)\n  var pathEnv = info.env\n  var pathExt = info.ext\n  var pathExtExe = info.extExe\n  var found = []\n\n  for (var i = 0, l = pathEnv.length; i < l; i ++) {\n    var pathPart = pathEnv[i]\n    if (pathPart.charAt(0) === '\"' && pathPart.slice(-1) === '\"')\n      pathPart = pathPart.slice(1, -1)\n\n    var p = path.join(pathPart, cmd)\n    if (!pathPart && /^\\.[\\\\\\/]/.test(cmd)) {\n      p = cmd.slice(0, 2) + p\n    }\n    for (var j = 0, ll = pathExt.length; j < ll; j ++) {\n      var cur = p + pathExt[j]\n      var is\n      try {\n        is = isexe.sync(cur, { pathExt: pathExtExe })\n        if (is) {\n          if (opt.all)\n            found.push(cur)\n          else\n            return cur\n        }\n      } catch (ex) {}\n    }\n  }\n\n  if (opt.all && found.length)\n    return found\n\n  if (opt.nothrow)\n    return null\n\n  throw getNotFoundError(cmd)\n}\n", "var fs = require('fs')\nvar core\nif (process.platform === 'win32' || global.TESTING_WINDOWS) {\n  core = require('./windows.js')\n} else {\n  core = require('./mode.js')\n}\n\nmodule.exports = isexe\nisexe.sync = sync\n\nfunction isexe (path, options, cb) {\n  if (typeof options === 'function') {\n    cb = options\n    options = {}\n  }\n\n  if (!cb) {\n    if (typeof Promise !== 'function') {\n      throw new TypeError('callback not provided')\n    }\n\n    return new Promise(function (resolve, reject) {\n      isexe(path, options || {}, function (er, is) {\n        if (er) {\n          reject(er)\n        } else {\n          resolve(is)\n        }\n      })\n    })\n  }\n\n  core(path, options || {}, function (er, is) {\n    // ignore EACCES because that just means we aren't allowed to run it\n    if (er) {\n      if (er.code === 'EACCES' || options && options.ignoreErrors) {\n        er = null\n        is = false\n      }\n    }\n    cb(er, is)\n  })\n}\n\nfunction sync (path, options) {\n  // my kingdom for a filtered catch\n  try {\n    return core.sync(path, options || {})\n  } catch (er) {\n    if (options && options.ignoreErrors || er.code === 'EACCES') {\n      return false\n    } else {\n      throw er\n    }\n  }\n}\n", "'use strict';\n\n// See http://www.robvanderwoude.com/escapechars.php\nconst metaCharsRegExp = /([()\\][%!^\"`<>&|;, *?])/g;\n\nfunction escapeCommand(arg) {\n    // Escape meta chars\n    arg = arg.replace(metaCharsRegExp, '^$1');\n\n    return arg;\n}\n\nfunction escapeArgument(arg, doubleEscapeMetaChars) {\n    // Convert to string\n    arg = `${arg}`;\n\n    // Algorithm below is based on https://qntm.org/cmd\n\n    // Sequence of backslashes followed by a double quote:\n    // double up all the backslashes and escape the double quote\n    arg = arg.replace(/(\\\\*)\"/g, '$1$1\\\\\"');\n\n    // Sequence of backslashes followed by the end of the string\n    // (which will become a double quote later):\n    // double up all the backslashes\n    arg = arg.replace(/(\\\\*)$/, '$1$1');\n\n    // All other backslashes occur literally\n\n    // Quote the whole thing:\n    arg = `\"${arg}\"`;\n\n    // Escape meta chars\n    arg = arg.replace(metaCharsRegExp, '^$1');\n\n    // Double escape meta chars if necessary\n    if (doubleEscapeMetaChars) {\n        arg = arg.replace(metaCharsRegExp, '^$1');\n    }\n\n    return arg;\n}\n\nmodule.exports.command = escapeCommand;\nmodule.exports.argument = escapeArgument;\n", "'use strict';\n\nconst fs = require('fs');\nconst shebangCommand = require('shebang-command');\n\nfunction readShebang(command) {\n    // Read the first 150 bytes from the file\n    const size = 150;\n    let buffer;\n\n    if (Buffer.alloc) {\n        // Node.js v4.5+ / v5.10+\n        buffer = Buffer.alloc(size);\n    } else {\n        // Old Node.js API\n        buffer = new Buffer(size);\n        buffer.fill(0); // zero-fill\n    }\n\n    let fd;\n\n    try {\n        fd = fs.openSync(command, 'r');\n        fs.readSync(fd, buffer, 0, size, 0);\n        fs.closeSync(fd);\n    } catch (e) { /* Empty */ }\n\n    // Attempt to extract shebang (null is returned if not a shebang)\n    return shebangCommand(buffer.toString());\n}\n\nmodule.exports = readShebang;\n", "'use strict';\nvar shebangRegex = require('shebang-regex');\n\nmodule.exports = function (str) {\n\tvar match = str.match(shebangRegex);\n\n\tif (!match) {\n\t\treturn null;\n\t}\n\n\tvar arr = match[0].replace(/#! ?/, '').split(' ');\n\tvar bin = arr[0].split('/').pop();\n\tvar arg = arr[1];\n\n\treturn (bin === 'env' ?\n\t\targ :\n\t\tbin + (arg ? ' ' + arg : '')\n\t);\n};\n", "'use strict';\nmodule.exports = /^#!.*/;\n", "'use strict';\n\nconst isWin = process.platform === 'win32';\n\nfunction notFoundError(original, syscall) {\n    return Object.assign(new Error(`${syscall} ${original.command} ENOENT`), {\n        code: 'ENOENT',\n        errno: 'ENOENT',\n        syscall: `${syscall} ${original.command}`,\n        path: original.command,\n        spawnargs: original.args,\n    });\n}\n\nfunction hookChildProcess(cp, parsed) {\n    if (!isWin) {\n        return;\n    }\n\n    const originalEmit = cp.emit;\n\n    cp.emit = function (name, arg1) {\n        // If emitting \"exit\" event and exit code is 1, we need to check if\n        // the command exists and emit an \"error\" instead\n        // See https://github.com/IndigoUnited/node-cross-spawn/issues/16\n        if (name === 'exit') {\n            const err = verifyENOENT(arg1, parsed, 'spawn');\n\n            if (err) {\n                return originalEmit.call(cp, 'error', err);\n            }\n        }\n\n        return originalEmit.apply(cp, arguments); // eslint-disable-line prefer-rest-params\n    };\n}\n\nfunction verifyENOENT(status, parsed) {\n    if (isWin && status === 1 && !parsed.file) {\n        return notFoundError(parsed.original, 'spawn');\n    }\n\n    return null;\n}\n\nfunction verifyENOENTSync(status, parsed) {\n    if (isWin && status === 1 && !parsed.file) {\n        return notFoundError(parsed.original, 'spawnSync');\n    }\n\n    return null;\n}\n\nmodule.exports = {\n    hookChildProcess,\n    verifyENOENT,\n    verifyENOENTSync,\n    notFoundError,\n};\n", "// @flow strict-local\n\nimport type {ChildProcess} from 'child_process';\n\nexport default function promiseFromProcess(\n  childProcess: ChildProcess,\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    childProcess.on('error', reject);\n    childProcess.on('close', code => {\n      if (code !== 0) {\n        reject(new Error('Child process failed'));\n        return;\n      }\n\n      resolve();\n    });\n  });\n}\n", "{\n  \"name\": \"@parcel/package-manager\",\n  \"version\": \"2.8.3\",\n  \"description\": \"Blazing fast, zero configuration web application bundler\",\n  \"license\": \"MIT\",\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"funding\": {\n    \"type\": \"opencollective\",\n    \"url\": \"https://opencollective.com/parcel\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/parcel-bundler/parcel.git\"\n  },\n  \"main\": \"lib/index.js\",\n  \"source\": \"src/index.js\",\n  \"types\": \"index.d.ts\",\n  \"engines\": {\n    \"node\": \">= 12.0.0\"\n  },\n  \"scripts\": {\n    \"build-ts\": \"mkdir -p lib && flow-to-ts src/types.js > lib/types.d.ts\",\n    \"check-ts\": \"tsc --noEmit index.d.ts\"\n  },\n  \"targets\": {\n    \"types\": false,\n    \"main\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/diagnostic\": false,\n        \"@parcel/fs\": false,\n        \"@parcel/logger\": false,\n        \"@parcel/types\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/workers\": false,\n        \"semver\": false\n      }\n    }\n  },\n  \"dependencies\": {\n    \"@parcel/diagnostic\": \"2.8.3\",\n    \"@parcel/fs\": \"2.8.3\",\n    \"@parcel/logger\": \"2.8.3\",\n    \"@parcel/types\": \"2.8.3\",\n    \"@parcel/utils\": \"2.8.3\",\n    \"@parcel/workers\": \"2.8.3\",\n    \"semver\": \"^5.7.1\"\n  },\n  \"devDependencies\": {\n    \"command-exists\": \"^1.2.6\",\n    \"cross-spawn\": \"^6.0.4\",\n    \"nullthrows\": \"^1.1.1\",\n    \"split2\": \"^3.1.1\"\n  },\n  \"peerDependencies\": {\n    \"@parcel/core\": \"^2.8.3\"\n  },\n  \"browser\": {\n    \"./src/Npm.js\": false,\n    \"./src/Pnpm.js\": false,\n    \"./src/Yarn.js\": false\n  },\n  \"gitHead\": \"349a6caf40ec8abb6a49fcae0765f8f8deb2073d\"\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from './types';\n\nimport commandExists from 'command-exists';\nimport spawn from 'cross-spawn';\nimport {exec as _exec} from 'child_process';\nimport {promisify} from 'util';\nimport logger from '@parcel/logger';\nimport split from 'split2';\nimport JSONParseStream from './JSONParseStream';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst YARN_CMD = 'yarn';\nconst exec = promisify(_exec);\n\ntype YarnStdOutMessage =\n  | {|\n      +type: 'step',\n      data: {|\n        message: string,\n        current: number,\n        total: number,\n      |},\n    |}\n  | {|+type: 'success', data: string|}\n  | {|+type: 'info', data: string|}\n  | {|+type: 'tree' | 'progressStart' | 'progressTick'|};\n\ntype YarnStdErrMessage = {|\n  +type: 'error' | 'warning',\n  data: string,\n|};\n\nlet hasYarn: ?boolean;\nlet yarnVersion: ?number;\n\nexport class Yarn implements PackageInstaller {\n  static async exists(): Promise<boolean> {\n    if (hasYarn != null) {\n      return hasYarn;\n    }\n\n    try {\n      hasYarn = Boolean(await commandExists('yarn'));\n    } catch (err) {\n      hasYarn = false;\n    }\n\n    return hasYarn;\n  }\n\n  async install({\n    modules,\n    cwd,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (yarnVersion == null) {\n      let version = await exec('yarn --version');\n      yarnVersion = parseInt(version.stdout, 10);\n    }\n\n    let args = ['add', '--json'].concat(\n      modules.map(npmSpecifierFromModuleRequest),\n    );\n\n    if (saveDev) {\n      args.push('-D');\n      if (yarnVersion < 2) {\n        args.push('-W');\n      }\n    }\n\n    // When Parcel is run by Yarn (e.g. via package.json scripts), several environment variables are\n    // added. When parcel in turn calls Yarn again, these can cause Yarn to behave stragely, so we\n    // filter them out when installing packages.\n    let env = {};\n    for (let key in process.env) {\n      if (\n        !key.startsWith('npm_') &&\n        key !== 'YARN_WRAP_OUTPUT' &&\n        key !== 'INIT_CWD' &&\n        key !== 'NODE_ENV'\n      ) {\n        env[key] = process.env[key];\n      }\n    }\n\n    let installProcess = spawn(YARN_CMD, args, {cwd, env});\n    installProcess.stdout\n      // Invoking yarn with --json provides streaming, newline-delimited JSON output.\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.error(e, '@parcel/package-manager');\n      })\n      .on('data', (message: YarnStdOutMessage) => {\n        switch (message.type) {\n          case 'step':\n            logger.progress(\n              prefix(\n                `[${message.data.current}/${message.data.total}] ${message.data.message}`,\n              ),\n            );\n            return;\n          case 'success':\n          case 'info':\n            logger.info({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          default:\n          // ignore\n        }\n      });\n\n    installProcess.stderr\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.error(e, '@parcel/package-manager');\n      })\n      .on('data', (message: YarnStdErrMessage) => {\n        switch (message.type) {\n          case 'warning':\n            logger.warn({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          case 'error':\n            logger.error({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          default:\n          // ignore\n        }\n      });\n\n    try {\n      return await promiseFromProcess(installProcess);\n    } catch (e) {\n      throw new Error('Yarn failed to install modules:' + e.message);\n    }\n  }\n}\n\nfunction prefix(message: string): string {\n  return 'yarn: ' + message;\n}\n\nregisterSerializableClass(`${pkg.version}:Yarn`, Yarn);\n", "module.exports = require('./lib/command-exists');\n", "/*\nCopyright (c) 2014-2018, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n*/\n\n'use strict'\n\nconst { Transform } = require('readable-stream')\nconst { StringDecoder } = require('string_decoder')\nconst kLast = Symbol('last')\nconst kDecoder = Symbol('decoder')\n\nfunction transform (chunk, enc, cb) {\n  var list\n  if (this.overflow) { // Line buffer is full. Skip to start of next line.\n    var buf = this[kDecoder].write(chunk)\n    list = buf.split(this.matcher)\n\n    if (list.length === 1) return cb() // Line ending not found. Discard entire chunk.\n\n    // Line ending found. Discard trailing fragment of previous line and reset overflow state.\n    list.shift()\n    this.overflow = false\n  } else {\n    this[kLast] += this[kDecoder].write(chunk)\n    list = this[kLast].split(this.matcher)\n  }\n\n  this[kLast] = list.pop()\n\n  for (var i = 0; i < list.length; i++) {\n    try {\n      push(this, this.mapper(list[i]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  this.overflow = this[kLast].length > this.maxLength\n  if (this.overflow && !this.skipOverflow) return cb(new Error('maximum buffer reached'))\n\n  cb()\n}\n\nfunction flush (cb) {\n  // forward any gibberish left in there\n  this[kLast] += this[kDecoder].end()\n\n  if (this[kLast]) {\n    try {\n      push(this, this.mapper(this[kLast]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  cb()\n}\n\nfunction push (self, val) {\n  if (val !== undefined) {\n    self.push(val)\n  }\n}\n\nfunction noop (incoming) {\n  return incoming\n}\n\nfunction split (matcher, mapper, options) {\n  // Set defaults for any arguments not supplied.\n  matcher = matcher || /\\r?\\n/\n  mapper = mapper || noop\n  options = options || {}\n\n  // Test arguments explicitly.\n  switch (arguments.length) {\n    case 1:\n      // If mapper is only argument.\n      if (typeof matcher === 'function') {\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If options is only argument.\n      } else if (typeof matcher === 'object' && !(matcher instanceof RegExp)) {\n        options = matcher\n        matcher = /\\r?\\n/\n      }\n      break\n\n    case 2:\n      // If mapper and options are arguments.\n      if (typeof matcher === 'function') {\n        options = mapper\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If matcher and options are arguments.\n      } else if (typeof mapper === 'object') {\n        options = mapper\n        mapper = noop\n      }\n  }\n\n  options = Object.assign({}, options)\n  options.transform = transform\n  options.flush = flush\n  options.readableObjectMode = true\n\n  const stream = new Transform(options)\n\n  stream[kLast] = ''\n  stream[kDecoder] = new StringDecoder('utf8')\n  stream.matcher = matcher\n  stream.mapper = mapper\n  stream.maxLength = options.maxLength\n  stream.skipOverflow = options.skipOverflow\n  stream.overflow = false\n\n  return stream\n}\n\nmodule.exports = split\n", "// @flow strict-local\n\nimport type {JSONObject} from '@parcel/types';\n\nimport logger from '@parcel/logger';\nimport {Transform} from 'stream';\n\n// Transforms chunks of json strings to parsed objects.\n// Pair with split2 to parse stream of newline-delimited text.\nexport default class JSONParseStream extends Transform {\n  constructor(options: mixed) {\n    super({...options, objectMode: true});\n  }\n\n  // $FlowFixMe We are in object mode, so we emit objects, not strings\n  _transform(\n    chunk: Buffer | string,\n    encoding: string,\n    callback: (err: ?Error, parsed: ?JSONObject) => mixed,\n  ) {\n    try {\n      let parsed;\n      try {\n        parsed = JSON.parse(chunk.toString());\n      } catch (e) {\n        // Be permissive and ignoreJSON parse errors in case there was\n        // a non-JSON line in the package manager's stdout.\n        logger.verbose({\n          message: 'Ignored invalid JSON message: ' + chunk.toString(),\n          origin: '@parcel/package-manager',\n        });\n        return;\n      }\n      callback(null, parsed);\n    } catch (err) {\n      callback(err);\n    }\n  }\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from './types';\n\nimport commandExists from 'command-exists';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport split from 'split2';\nimport JSONParseStream from './JSONParseStream';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst PNPM_CMD = 'pnpm';\n\ntype LogLevel = 'error' | 'warn' | 'info' | 'debug';\n\ntype ErrorLog = {|\n  err: {|\n    message: string,\n    code: string,\n    stack: string,\n  |},\n|};\n\ntype PNPMLog =\n  | {|\n      +name: 'pnpm:progress',\n      packageId: string,\n      status: 'fetched' | 'found_in_store' | 'resolved',\n    |}\n  | {|\n      +name: 'pnpm:root',\n      added?: {|\n        id?: string,\n        name: string,\n        realName: string,\n        version?: string,\n        dependencyType?: 'prod' | 'dev' | 'optional',\n        latest?: string,\n        linkedFrom?: string,\n      |},\n      removed?: {|\n        name: string,\n        version?: string,\n        dependencyType?: 'prod' | 'dev' | 'optional',\n      |},\n    |}\n  | {|+name: 'pnpm:importing', from: string, method: string, to: string|}\n  | {|+name: 'pnpm:link', target: string, link: string|}\n  | {|+name: 'pnpm:stats', prefix: string, removed?: number, added?: number|};\n\ntype PNPMResults = {|\n  level: LogLevel,\n  prefix?: string,\n  message?: string,\n  ...ErrorLog,\n  ...PNPMLog,\n|};\n\nlet hasPnpm: ?boolean;\nexport class Pnpm implements PackageInstaller {\n  static async exists(): Promise<boolean> {\n    if (hasPnpm != null) {\n      return hasPnpm;\n    }\n\n    try {\n      hasPnpm = Boolean(await commandExists('pnpm'));\n    } catch (err) {\n      hasPnpm = false;\n    }\n\n    return hasPnpm;\n  }\n\n  async install({\n    modules,\n    cwd,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    let args = ['add', '--reporter', 'ndjson'];\n    if (saveDev) {\n      args.push('-D', '-W');\n    }\n    args = args.concat(modules.map(npmSpecifierFromModuleRequest));\n\n    let env = {};\n    for (let key in process.env) {\n      if (!key.startsWith('npm_') && key !== 'INIT_CWD' && key !== 'NODE_ENV') {\n        env[key] = process.env[key];\n      }\n    }\n\n    let addedCount = 0,\n      removedCount = 0;\n\n    let installProcess = spawn(PNPM_CMD, args, {\n      cwd,\n      env,\n    });\n    installProcess.stdout\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.warn({\n          origin: '@parcel/package-manager',\n          message: e.chunk,\n          stack: e.stack,\n        });\n      })\n      .on('data', (json: PNPMResults) => {\n        if (json.level === 'error') {\n          logger.error({\n            origin: '@parcel/package-manager',\n            message: json.err.message,\n            stack: json.err.stack,\n          });\n        } else if (json.level === 'info' && typeof json.message === 'string') {\n          logger.info({\n            origin: '@parcel/package-manager',\n            message: prefix(json.message),\n          });\n        } else if (json.name === 'pnpm:stats') {\n          addedCount += json.added ?? 0;\n          removedCount += json.removed ?? 0;\n        }\n      });\n\n    let stderr = [];\n    installProcess.stderr\n      .on('data', str => {\n        stderr.push(str.toString());\n      })\n      .on('error', e => {\n        logger.warn({\n          origin: '@parcel/package-manager',\n          message: e.message,\n        });\n      });\n\n    try {\n      await promiseFromProcess(installProcess);\n\n      if (addedCount > 0 || removedCount > 0) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message: `Added ${addedCount} and ${\n            removedCount > 0 ? `removed ${removedCount}` : ''\n          } packages via pnpm`,\n        });\n      }\n\n      // Since we succeeded, stderr might have useful information not included\n      // in the json written to stdout. It's also not necessary to log these as\n      // errors as they often aren't.\n      for (let message of stderr) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message,\n        });\n      }\n    } catch (e) {\n      throw new Error('pnpm failed to install modules');\n    }\n  }\n}\n\nfunction prefix(message: string): string {\n  return 'pnpm: ' + message;\n}\n\nregisterSerializableClass(`${pkg.version}:Pnpm`, Pnpm);\n", "// @flow\n\nconst MODULE_REGEX = /^((@[^/\\s]+\\/){0,1}([^/\\s.~]+[^/\\s]*)){1}(@[^/\\s]+){0,1}/;\n\nexport default function validateModuleSpecifier(moduleName: string): string {\n  let matches = MODULE_REGEX.exec(moduleName);\n  if (matches) {\n    return matches[0];\n  }\n\n  return '';\n}\n", "// @flow\n\nimport type {FilePath, DependencySpecifier, PackageJSON} from '@parcel/types';\nimport type {ResolverContext} from './NodeResolverBase';\nimport type {ResolveResult} from './types';\n\nimport path from 'path';\nimport {NodeResolverBase} from './NodeResolverBase';\n\nexport class NodeResolver extends NodeResolverBase<Promise<ResolveResult>> {\n  async resolve(\n    id: DependencySpecifier,\n    from: FilePath,\n  ): Promise<ResolveResult> {\n    let ctx = {\n      invalidateOnFileCreate: [],\n      invalidateOnFileChange: new Set(),\n    };\n\n    if (id[0] === '.') {\n      id = path.resolve(path.dirname(from), id);\n    }\n\n    let res = path.isAbsolute(id)\n      ? await this.loadRelative(id, ctx)\n      : await this.loadNodeModules(id, from, ctx);\n\n    if (!res) {\n      let e = new Error(`Could not resolve module \"${id}\" from \"${from}\"`);\n      // $FlowFixMe[prop-missing]\n      e.code = 'MODULE_NOT_FOUND';\n      throw e;\n    }\n\n    if (path.isAbsolute(res.resolved)) {\n      res.resolved = await this.fs.realpath(res.resolved);\n    }\n\n    return res;\n  }\n\n  async loadRelative(\n    id: FilePath,\n    ctx: ResolverContext,\n  ): Promise<?ResolveResult> {\n    // First try as a file, then as a directory.\n    return (\n      (await this.loadAsFile(id, null, ctx)) ||\n      (await this.loadDirectory(id, null, ctx)) // eslint-disable-line no-return-await\n    );\n  }\n\n  findPackage(\n    sourceFile: FilePath,\n    ctx: ResolverContext,\n  ): Promise<?PackageJSON> {\n    // If in node_modules, take a shortcut to find the package.json in the root of the package.\n    let pkgPath = this.getNodeModulesPackagePath(sourceFile);\n    if (pkgPath) {\n      return this.readPackage(pkgPath, ctx);\n    }\n\n    ctx.invalidateOnFileCreate.push({\n      fileName: 'package.json',\n      aboveFilePath: sourceFile,\n    });\n\n    let dir = path.dirname(sourceFile);\n    let pkgFile = this.fs.findAncestorFile(\n      ['package.json'],\n      dir,\n      this.projectRoot,\n    );\n    if (pkgFile != null) {\n      return this.readPackage(pkgFile, ctx);\n    }\n\n    return Promise.resolve(null);\n  }\n\n  async readPackage(\n    file: FilePath,\n    ctx: ResolverContext,\n  ): Promise<PackageJSON> {\n    let cached = this.packageCache.get(file);\n\n    if (cached) {\n      ctx.invalidateOnFileChange.add(file);\n      return cached;\n    }\n\n    let json;\n    try {\n      json = await this.fs.readFile(file, 'utf8');\n    } catch (err) {\n      ctx.invalidateOnFileCreate.push({\n        filePath: file,\n      });\n      throw err;\n    }\n\n    // Add the invalidation *before* we try to parse the JSON in case of errors\n    // so that changes are picked up if the file is edited to fix the error.\n    ctx.invalidateOnFileChange.add(file);\n\n    let pkg = JSON.parse(json);\n    this.packageCache.set(file, pkg);\n    return pkg;\n  }\n\n  async loadAsFile(\n    file: FilePath,\n    pkg: ?PackageJSON,\n    ctx: ResolverContext,\n  ): Promise<?ResolveResult> {\n    // Try all supported extensions\n    let files = this.expandFile(file);\n    let found = this.fs.findFirstFile(files);\n\n    // Add invalidations for higher priority files so we\n    // re-resolve if any of them are created.\n    for (let file of files) {\n      if (file === found) {\n        break;\n      }\n\n      ctx.invalidateOnFileCreate.push({\n        filePath: file,\n      });\n    }\n\n    if (found) {\n      return {\n        resolved: await this.fs.realpath(found),\n        // Find a package.json file in the current package.\n        pkg: pkg ?? (await this.findPackage(file, ctx)),\n        invalidateOnFileCreate: ctx.invalidateOnFileCreate,\n        invalidateOnFileChange: ctx.invalidateOnFileChange,\n      };\n    }\n\n    return null;\n  }\n\n  async loadDirectory(\n    dir: FilePath,\n    pkg: ?PackageJSON = null,\n    ctx: ResolverContext,\n  ): Promise<?ResolveResult> {\n    try {\n      pkg = await this.readPackage(path.join(dir, 'package.json'), ctx);\n\n      // Get a list of possible package entry points.\n      let entries = this.getPackageEntries(dir, pkg);\n\n      for (let file of entries) {\n        // First try loading package.main as a file, then try as a directory.\n        const res =\n          (await this.loadAsFile(file, pkg, ctx)) ||\n          (await this.loadDirectory(file, pkg, ctx));\n        if (res) {\n          return res;\n        }\n      }\n    } catch (err) {\n      // ignore\n    }\n\n    // Fall back to an index file inside the directory.\n    return this.loadAsFile(path.join(dir, 'index'), pkg, ctx);\n  }\n\n  async loadNodeModules(\n    id: DependencySpecifier,\n    from: FilePath,\n    ctx: ResolverContext,\n  ): Promise<?ResolveResult> {\n    try {\n      let module = this.findNodeModulePath(id, from, ctx);\n      if (!module || module.resolved) {\n        return module;\n      }\n\n      // If a module was specified as a module sub-path (e.g. some-module/some/path),\n      // it is likely a file. Try loading it as a file first.\n      if (module.subPath) {\n        let pkg = await this.readPackage(\n          path.join(module.moduleDir, 'package.json'),\n          ctx,\n        );\n        let res = await this.loadAsFile(module.filePath, pkg, ctx);\n        if (res) {\n          return res;\n        }\n      }\n\n      // Otherwise, load as a directory.\n      if (module.filePath) {\n        return await this.loadDirectory(module.filePath, null, ctx);\n      }\n    } catch (e) {\n      // ignore\n    }\n  }\n}\n", "// @flow\n\nimport type {\n  PackageJSON,\n  FileCreateInvalidation,\n  FilePath,\n  DependencySpecifier,\n} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {ResolveResult} from './types';\n\n// $FlowFixMe\nimport Module from 'module';\nimport path from 'path';\nimport invariant from 'assert';\nimport {getModuleParts} from '@parcel/utils';\n\nconst builtins = {pnpapi: true};\nfor (let builtin of Module.builtinModules) {\n  builtins[builtin] = true;\n}\n\nexport type ModuleInfo = {|\n  moduleName: string,\n  subPath: ?string,\n  moduleDir: FilePath,\n  filePath: FilePath,\n  code?: string,\n|};\n\nexport type ResolverContext = {|\n  invalidateOnFileCreate: Array<FileCreateInvalidation>,\n  invalidateOnFileChange: Set<FilePath>,\n|};\n\nconst NODE_MODULES = `${path.sep}node_modules${path.sep}`;\n\nexport class NodeResolverBase<T> {\n  fs: FileSystem;\n  extensions: Array<string>;\n  packageCache: Map<string, PackageJSON>;\n  projectRoot: FilePath;\n\n  constructor(\n    fs: FileSystem,\n    projectRoot: FilePath,\n    extensions?: Array<string>,\n  ) {\n    this.fs = fs;\n    this.projectRoot = projectRoot;\n    this.extensions =\n      extensions ||\n      // $FlowFixMe[prop-missing]\n      Object.keys(Module._extensions);\n    this.packageCache = new Map();\n  }\n\n  resolve(id: DependencySpecifier, from: FilePath): T {\n    throw new Error(`Could not resolve \"${id}\" from \"${from}\"`);\n  }\n\n  expandFile(file: FilePath): Array<FilePath> {\n    // Expand extensions and aliases\n    let res = [];\n    for (let ext of this.extensions) {\n      let f = file + ext;\n      res.push(f);\n    }\n\n    if (path.extname(file)) {\n      res.unshift(file);\n    } else {\n      res.push(file);\n    }\n\n    return res;\n  }\n\n  getPackageEntries(dir: FilePath, pkg: PackageJSON): Array<string> {\n    let main = pkg.main;\n    if (\n      (process.env.PARCEL_BUILD_ENV !== 'production' ||\n        process.env.PARCEL_SELF_BUILD) &&\n      typeof pkg.name === 'string' &&\n      typeof pkg.source === 'string' &&\n      pkg.name.startsWith('@parcel/') &&\n      pkg.name !== '@parcel/watcher'\n    ) {\n      main = pkg.source;\n    }\n\n    return [main]\n      .filter(entry => typeof entry === 'string')\n      .map(main => {\n        // Default to index file if no main field find\n        if (!main || main === '.' || main === './') {\n          main = 'index';\n        }\n\n        invariant(typeof main === 'string');\n        return path.resolve(dir, main);\n      });\n  }\n\n  isBuiltin(name: DependencySpecifier): boolean {\n    return !!(builtins[name] || name.startsWith('node:'));\n  }\n\n  findNodeModulePath(\n    id: DependencySpecifier,\n    sourceFile: FilePath,\n    ctx: ResolverContext,\n  ): ?ResolveResult | ?ModuleInfo {\n    if (this.isBuiltin(id)) {\n      return {\n        resolved: id,\n        invalidateOnFileChange: new Set(),\n        invalidateOnFileCreate: [],\n      };\n    }\n\n    let [moduleName, subPath] = getModuleParts(id);\n    let dir = path.dirname(sourceFile);\n    let moduleDir = this.fs.findNodeModule(moduleName, dir);\n\n    ctx.invalidateOnFileCreate.push({\n      fileName: `node_modules/${moduleName}`,\n      aboveFilePath: sourceFile,\n    });\n\n    if (!moduleDir && process.versions.pnp != null) {\n      try {\n        // $FlowFixMe[prop-missing]\n        let pnp = Module.findPnpApi(dir + '/');\n        moduleDir = pnp.resolveToUnqualified(\n          moduleName +\n            // retain slash in `require('assert/')` to force loading builtin from npm\n            (id[moduleName.length] === '/' ? '/' : ''),\n          dir + '/',\n        );\n\n        // Invalidate whenever the .pnp.js file changes.\n        ctx.invalidateOnFileChange.add(\n          pnp.resolveToUnqualified('pnpapi', null),\n        );\n      } catch (e) {\n        if (e.code !== 'MODULE_NOT_FOUND') {\n          throw e;\n        }\n      }\n    }\n\n    if (moduleDir) {\n      return {\n        moduleName,\n        subPath,\n        moduleDir: moduleDir,\n        filePath: subPath ? path.join(moduleDir, subPath) : moduleDir,\n      };\n    }\n\n    return null;\n  }\n\n  getNodeModulesPackagePath(sourceFile: FilePath): ?FilePath {\n    // If the file is in node_modules, we can find the package.json in the root of the package\n    // by slicing from the start of the string until 1-2 path segments after node_modules.\n    let index = sourceFile.lastIndexOf(NODE_MODULES);\n    if (index >= 0) {\n      index += NODE_MODULES.length;\n\n      // If a scoped path, add an extra path segment.\n      if (sourceFile[index] === '@') {\n        index = sourceFile.indexOf(path.sep, index) + 1;\n      }\n\n      index = sourceFile.indexOf(path.sep, index);\n      return path.join(\n        sourceFile.slice(0, index >= 0 ? index : undefined),\n        'package.json',\n      );\n    }\n  }\n\n  invalidate(filePath: FilePath) {\n    // Invalidate the package.jsons above `filePath`\n    let dir = path.dirname(filePath);\n    let {root} = path.parse(dir);\n    while (dir !== root && path.basename(dir) !== 'node_modules') {\n      this.packageCache.delete(path.join(dir, 'package.json'));\n      dir = path.dirname(dir);\n    }\n  }\n}\n", "// @flow\n\nimport type {FilePath, DependencySpecifier, PackageJSON} from '@parcel/types';\nimport type {ResolverContext} from './NodeResolverBase';\nimport type {ResolveResult} from './types';\n\nimport path from 'path';\nimport {NodeResolverBase} from './NodeResolverBase';\n\nexport class NodeResolverSync extends NodeResolverBase<ResolveResult> {\n  resolve(id: DependencySpecifier, from: FilePath): ResolveResult {\n    let ctx = {\n      invalidateOnFileCreate: [],\n      invalidateOnFileChange: new Set(),\n    };\n\n    if (id[0] === '.') {\n      id = path.resolve(path.dirname(from), id);\n    }\n\n    let res = path.isAbsolute(id)\n      ? this.loadRelative(id, ctx)\n      : this.loadNodeModules(id, from, ctx);\n\n    if (!res) {\n      let e = new Error(`Could not resolve module \"${id}\" from \"${from}\"`);\n      // $FlowFixMe\n      e.code = 'MODULE_NOT_FOUND';\n      throw e;\n    }\n\n    if (path.isAbsolute(res.resolved)) {\n      res.resolved = this.fs.realpathSync(res.resolved);\n    }\n\n    return res;\n  }\n\n  loadRelative(id: FilePath, ctx: ResolverContext): ?ResolveResult {\n    // First try as a file, then as a directory.\n    return this.loadAsFile(id, null, ctx) || this.loadDirectory(id, null, ctx);\n  }\n\n  findPackage(sourceFile: FilePath, ctx: ResolverContext): ?PackageJSON {\n    // If in node_modules, take a shortcut to find the package.json in the root of the package.\n    let pkgPath = this.getNodeModulesPackagePath(sourceFile);\n    if (pkgPath) {\n      return this.readPackage(pkgPath, ctx);\n    }\n\n    // Find the nearest package.json file within the current node_modules folder\n    let dir = path.dirname(sourceFile);\n    let pkgFile = this.fs.findAncestorFile(\n      ['package.json'],\n      dir,\n      this.projectRoot,\n    );\n    if (pkgFile != null) {\n      return this.readPackage(pkgFile, ctx);\n    }\n  }\n\n  readPackage(file: FilePath, ctx: ResolverContext): PackageJSON {\n    let cached = this.packageCache.get(file);\n\n    if (cached) {\n      ctx.invalidateOnFileChange.add(file);\n      return cached;\n    }\n\n    let json;\n    try {\n      json = this.fs.readFileSync(file, 'utf8');\n    } catch (err) {\n      ctx.invalidateOnFileCreate.push({\n        filePath: file,\n      });\n      throw err;\n    }\n\n    // Add the invalidation *before* we try to parse the JSON in case of errors\n    // so that changes are picked up if the file is edited to fix the error.\n    ctx.invalidateOnFileChange.add(file);\n\n    let pkg = JSON.parse(json);\n\n    this.packageCache.set(file, pkg);\n    return pkg;\n  }\n\n  loadAsFile(\n    file: FilePath,\n    pkg: ?PackageJSON,\n    ctx: ResolverContext,\n  ): ?ResolveResult {\n    // Try all supported extensions\n    let files = this.expandFile(file);\n    let found = this.fs.findFirstFile(files);\n\n    // Add invalidations for higher priority files so we\n    // re-resolve if any of them are created.\n    for (let file of files) {\n      if (file === found) {\n        break;\n      }\n\n      ctx.invalidateOnFileCreate.push({\n        filePath: file,\n      });\n    }\n\n    if (found) {\n      return {\n        resolved: this.fs.realpathSync(found),\n        // Find a package.json file in the current package.\n        pkg: pkg ?? this.findPackage(file, ctx),\n        invalidateOnFileCreate: ctx.invalidateOnFileCreate,\n        invalidateOnFileChange: ctx.invalidateOnFileChange,\n      };\n    }\n\n    return null;\n  }\n\n  loadDirectory(\n    dir: FilePath,\n    pkg: ?PackageJSON = null,\n    ctx: ResolverContext,\n  ): ?ResolveResult {\n    try {\n      pkg = this.readPackage(path.join(dir, 'package.json'), ctx);\n\n      // Get a list of possible package entry points.\n      let entries = this.getPackageEntries(dir, pkg);\n\n      for (let file of entries) {\n        // First try loading package.main as a file, then try as a directory.\n        const res =\n          this.loadAsFile(file, pkg, ctx) || this.loadDirectory(file, pkg, ctx);\n        if (res) {\n          return res;\n        }\n      }\n    } catch (err) {\n      // ignore\n    }\n\n    // Fall back to an index file inside the directory.\n    return this.loadAsFile(path.join(dir, 'index'), pkg, ctx);\n  }\n\n  loadNodeModules(\n    id: DependencySpecifier,\n    from: FilePath,\n    ctx: ResolverContext,\n  ): ?ResolveResult {\n    try {\n      let module = this.findNodeModulePath(id, from, ctx);\n      if (!module || module.resolved) {\n        return module;\n      }\n\n      // If a module was specified as a module sub-path (e.g. some-module/some/path),\n      // it is likely a file. Try loading it as a file first.\n      if (module.subPath) {\n        let pkg = this.readPackage(\n          path.join(module.moduleDir, 'package.json'),\n          ctx,\n        );\n        let res = this.loadAsFile(module.filePath, pkg, ctx);\n        if (res) {\n          return res;\n        }\n      }\n\n      // Otherwise, load as a directory.\n      if (module.filePath) {\n        return this.loadDirectory(module.filePath, null, ctx);\n      }\n    } catch (e) {\n      // ignore\n    }\n  }\n}\n", "// @flow\nexport type * from './types';\nexport * from './Npm';\nexport * from './Pnpm';\nexport * from './Yarn';\nexport * from './MockPackageInstaller';\nexport * from './NodePackageManager';\nexport {_addToInstallQueue} from './installPackage';\n", "// @flow\n\nimport type {ModuleRequest, PackageInstaller, InstallerOptions} from './types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {FilePath} from '@parcel/types';\n\nimport path from 'path';\nimport {ncp} from '@parcel/fs';\nimport {registerSerializableClass} from '@parcel/core';\nimport pkg from '../package.json';\nimport {moduleRequestsFromDependencyMap} from './utils';\n\ntype Package = {|\n  fs: FileSystem,\n  packagePath: FilePath,\n|};\n\n// This PackageInstaller implementation simply copies files from one filesystem to another.\n// Mostly useful for testing purposes.\nexport class MockPackageInstaller implements PackageInstaller {\n  packages: Map<string, Package> = new Map<string, Package>();\n\n  register(packageName: string, fs: FileSystem, packagePath: FilePath) {\n    this.packages.set(packageName, {fs, packagePath});\n  }\n\n  async install({\n    modules,\n    fs,\n    cwd,\n    packagePath,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (packagePath == null) {\n      packagePath = path.join(cwd, 'package.json');\n      await fs.writeFile(packagePath, '{}');\n    }\n\n    let pkg = JSON.parse(await fs.readFile(packagePath, 'utf8'));\n    let key = saveDev ? 'devDependencies' : 'dependencies';\n\n    if (!pkg[key]) {\n      pkg[key] = {};\n    }\n\n    for (let module of modules) {\n      pkg[key][module.name] =\n        '^' + (await this.installPackage(module, fs, packagePath));\n    }\n\n    await fs.writeFile(packagePath, JSON.stringify(pkg));\n  }\n\n  async installPackage(\n    moduleRequest: ModuleRequest,\n    fs: FileSystem,\n    packagePath: FilePath,\n  ): Promise<any> {\n    let pkg = this.packages.get(moduleRequest.name);\n    if (!pkg) {\n      throw new Error('Unknown package ' + moduleRequest.name);\n    }\n\n    let dest = path.join(\n      path.dirname(packagePath),\n      'node_modules',\n      moduleRequest.name,\n    );\n    await ncp(pkg.fs, pkg.packagePath, fs, dest);\n\n    let packageJSON = JSON.parse(\n      await fs.readFile(path.join(dest, 'package.json'), 'utf8'),\n    );\n\n    if (packageJSON.dependencies != null) {\n      for (let dep of moduleRequestsFromDependencyMap(\n        packageJSON.dependencies,\n      )) {\n        await this.installPackage(dep, fs, packagePath);\n      }\n    }\n\n    return packageJSON.version;\n  }\n}\n\nregisterSerializableClass(\n  `${pkg.version}:MockPackageInstaller`,\n  MockPackageInstaller,\n);\n"], "names": ["registerSerializableClass", "ThrowableDiagnostic", "encodeJSONKeyComponent", "escapeMarkdown", "generateJSONCodeHighlights", "md", "nativeFS", "<PERSON><PERSON><PERSON>", "path", "semver", "getModuleParts", "getConflictingLocalDependencies", "installPackage", "pkg", "NodeResolver", "NodeResolverSync", "cache", "Map", "children", "NodePackageManager", "invalidationsCache", "constructor", "fs", "projectRoot", "installer", "resolver", "syncResolver", "deserialize", "opts", "serialize", "$$raw", "require", "name", "from", "resolved", "resolve", "load", "requireSync", "resolveSync", "filePath", "isAbsolute", "cachedModule", "_cache", "undefined", "exports", "m", "module", "parent", "id", "readFileSync", "filename", "encoding", "err", "options", "basedir", "dirname", "key", "get", "e", "code", "shouldAutoInstall", "diagnostic", "message", "hints", "conflicts", "install", "range", "saveDev", "fields", "map", "field", "origin", "codeFrames", "language", "json", "codeHighlights", "type", "satisfies", "version", "set", "clear", "module<PERSON><PERSON><PERSON><PERSON>", "Set", "add", "modules", "packageInstaller", "getInvalidations", "cached", "res", "invalidateOnFileCreate", "invalidateOnFileChange", "seen", "<PERSON><PERSON><PERSON>", "has", "push", "file", "specifier", "invalidate", "delete", "invariant", "resolveConfig", "npmSpecifierFromModuleRequest", "moduleRequest", "join", "moduleRequestsFromDependencyMap", "dependencyMap", "Object", "entries", "local", "pkgPath", "pkgStr", "readFile", "JSON", "parse", "length", "nullthrows", "logger", "loadConfig", "PromiseQueue", "WorkerFarm", "Npm", "Yarn", "Pnpm", "validateModuleSpecifier", "packageManager", "installPeers", "moduleNames", "progress", "fromPkgPath", "cwd", "determinePackageInstaller", "packagePath", "Error", "Promise", "all", "installPeerDependencies", "modulePkg", "config", "peers", "peerDependencies", "assign", "filepath", "configFile", "config<PERSON><PERSON>", "basename", "exists", "queue", "maxConcurrent", "modulesInstalling", "_addToInstallQueue", "request", "modulesToInstall", "filter", "getModuleRequestKey", "then", "run", "isWorker", "workerApi", "getWorkerApi", "bundlePath", "process", "env", "PARCEL_SELF_BUILD", "__dirname", "__filename", "callMaster", "location", "args", "method", "spawn", "promiseFromProcess", "NPM_CMD", "writeFile", "concat", "startsWith", "installProcess", "stdout", "on", "buf", "toString", "stderr", "trim", "results", "addedCount", "added", "log", "childProcess", "reject", "commandExists", "exec", "_exec", "promisify", "split", "JSONParseStream", "YARN_CMD", "<PERSON><PERSON><PERSON><PERSON>", "yarnVersion", "Boolean", "parseInt", "pipe", "error", "prefix", "data", "current", "total", "info", "warn", "Transform", "objectMode", "_transform", "chunk", "callback", "parsed", "verbose", "PNPM_CMD", "hasPnpm", "removedCount", "stack", "level", "removed", "str", "MODULE_REGEX", "moduleName", "matches", "NodeResolverBase", "ctx", "loadRelative", "loadNodeModules", "realpath", "loadAsFile", "loadDirectory", "findPackage", "sourceFile", "getNodeModulesPackagePath", "readPackage", "fileName", "aboveFile<PERSON><PERSON>", "dir", "pkgFile", "findAncestorFile", "packageCache", "files", "expandFile", "found", "findFirstFile", "getPackageEntries", "findNodeModulePath", "subPath", "moduleDir", "builtins", "pnpapi", "builtin", "builtinModules", "NODE_MODULES", "sep", "extensions", "keys", "_extensions", "ext", "extname", "unshift", "main", "source", "entry", "isBuiltin", "findNodeModule", "versions", "pnp", "findPnpApi", "resolveToUnqualified", "index", "lastIndexOf", "indexOf", "slice", "root", "realpathSync", "ncp", "MockPackageInstaller", "packages", "register", "packageName", "stringify", "dest", "packageJSON", "dependencies", "dep"], "version": 3, "file": "index.js.map", "sourceRoot": "../../../../"}