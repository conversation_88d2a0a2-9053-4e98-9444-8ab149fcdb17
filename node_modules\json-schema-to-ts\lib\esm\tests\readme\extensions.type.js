var bigIntSchema = {
  type: "number",
  numberType: "bigInt"
};
var assertExtends = 1;
assertExtends;
var invalidSchema = {
  type: "number",
  numberType: "bigIntt"
};
var assertNotExtends = 0;
assertNotExtends;
var assertBigInt = 1;
assertBigInt;
var nestedSchema = {
  type: "object",
  properties: {
    nested: {
      numberType: "bigInt"
    }
  },
  required: ["nested"],
  additionalProperties: false
};
var assertNestedBigInt = 1;
assertNestedBigInt;
export {};
//# sourceMappingURL=extensions.type.js.map